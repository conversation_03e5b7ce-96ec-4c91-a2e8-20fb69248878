import pandas as pd
from openai import OpenAI
import openai
import os
import requests # Added for a direct check

# --- Configuration ---
# Ollama's OpenAI-compatible API endpoint
OLLAMA_OPENAI_COMPATIBLE_BASE_URL = "http://localhost:11434/v1"

# The model name as it's known to Ollama (e.g., gemma:2b, gemma:7b, or your custom gemma3:12b)
OLLAMA_MODEL = "gemma3:12b" # <--- CONFIRM THIS IS THE EXACT NAME FROM `ollama list`!

CSV_FILE_PATH = "reg.csv" # Use the same dummy CSV from before

# --- Configure the OpenAI client to point to Ollama's local endpoint ---
# IMPORTANT: These lines must be executed BEFORE you make any calls using the openai client.
# It's best practice to set them right after importing the openai library.
client = OpenAI(
    base_url = 'http://localhost:11434/v1',
    api_key='ollama', # required, but unused
)
# --- 1. Load CSV Data ---
def load_csv_data(file_path):
    try:
        df = pd.read_csv(file_path)
        print(f"Successfully loaded {len(df)} rows from {file_path}")
        return df
    except FileNotFoundError:
        print(f"Error: CSV file not found at {file_path}")
        return None
    except Exception as e:
        print(f"Error loading CSV: {e}")
        return None

# --- 2. Interact with Ollama (via OpenAI-compatible interface) ---
def ask_ollama_via_openai(prompt_messages):
    """
    Sends a list of messages to the local Ollama server's OpenAI-compatible API.
    """
    try:
        # Debugging: Print the exact request parameters being sent
        print(f"\nDEBUG: Sending request to model: {OLLAMA_MODEL}")
        print(f"DEBUG: Messages: {prompt_messages}")

        response = client.chat.completions.create(
            model=OLLAMA_MODEL,
            messages=prompt_messages,
            temperature=0.0,  # Set to 0.0 for more deterministic answers based on context
            max_tokens=500    # Maximum number of tokens in the response
        )
        return response.choices[0].message.content
    except openai.APIConnectionError as e:
        print(f"Please ensure Ollama is running and the model '{OLLAMA_MODEL}' is pulled.")


        return "Sorry, I can't connect to the local AI model right now."
    except openai.APIStatusError as e:
        print(f"Ollama API error (status {e.status_code}): {e.response}")
        print(f"Check if the model '{OLLAMA_MODEL}' is correctly loaded in Ollama or if the base URL  is correct.")
        return "An error occurred with the local AI model."
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return "An unexpected error occurred while getting a response from the AI model."

# --- 3. Chatbot Logic ---
def run_chatbot():
    df = load_csv_data(CSV_FILE_PATH)
    if df is None:
        return

    print("\nChatbot initialized. Ask me questions about your SCADA tags (type 'exit' to quit).")
    print(f"Using local Ollama with model: {OLLAMA_MODEL}")
    print(f"Ollama API base: {OLLAMA_OPENAI_COMPATIBLE_BASE_URL}")

    while True:
        user_question = input("\nYour question: ").strip()
        if user_question.lower() == 'exit':
            print("Exiting chatbot. Goodbye!")
            break

        if not user_question:
            print("Please enter a question.")
            continue

        csv_data_string = df.to_string(index=False)

        prompt_messages = [
            {"role": "system", "content": "You are a helpful assistant that answers questions based on provided SCADA tag data."},
            {"role": "system", "content": "Sei un assistente per domande relative ai tag SCADA."},

            {"role": "user", "content": f"""
Here is some information about SCADA tags from a CSV file:

{csv_data_string}

Based on the information above, please answer the following question:
{user_question}

If the information is not directly in the provided text, state that you don't have enough information.
            """}
        ]

        print("\nThinking...")
        response = ask_ollama_via_openai(prompt_messages)
        print(f"\nChatbot: {response}")

if __name__ == "__main__":
    run_chatbot()