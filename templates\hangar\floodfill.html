<html>

<head>
    <style>
    canvas {
        border:1px solid #d3d3d3;
    } 
            
    </style>

    <title>Page Title</title>
</head>

<body>

    <canvas id="canvas" width="1866" height="960" >

</body>

<script>
    const img = new Image();
//img.crossOrigin = "anonymous";
img.src = "http://localhost:5000/webscada/img/A_Gen_Trento_15960.png";


const canvas = document.getElementById("canvas");
const ctx = canvas.getContext("2d");
img.addEventListener("load", () => {
  ctx.drawImage(img, 0, 0);
  img.style.display = "none";
  //floodFill(ctx, 864, 762, [255, 0, 0, 255]);  
});
//const hoveredColor = document.getElementById("hovered-color");
//const selectedColor = document.getElementById("selected-color");
// Define the fill color, boundary color, and seed pixel
var fillColor = [255, 0, 0, 255]; // red
var boundaryColor = [1, 1, 1, 1]; // black
var seed = [100, 100]; // x and y coordinates

// Get the image data of the canvas
var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
var data = imageData.data;

function pick(event, destination) {
  const bounding = canvas.getBoundingClientRect();
  const x = event.clientX - bounding.left;
  const y = event.clientY - bounding.top;
  console.log(x,y);
  const pixel = ctx.getImageData(x, y, 1, 1);
  const datax = pixel.data;
  
  const rgbColor = `rgb(${datax[0]} ${datax[1]} ${datax[2]} / ${datax[3] / 255})`;
  //destination.style.background = rgbColor;
  //destination.textContent = rgbColor;
  floodFill(ctx, x, y, [255, 0, 0, 255]);
	
  console.log(rgbColor)
  return rgbColor;
}

//canvas.addEventListener("mousemove", (event) => pick(event, canvas));
canvas.addEventListener("click", (event) => pick(event, canvas));

function getPixel(imageData, x, y) {
  if (x < 0 || y < 0 || x >= imageData.width || y >= imageData.height) {
    return [-1, -1, -1, -1];  // impossible color
  } else {
    const offset = (y * imageData.width + x) * 4;
    return imageData.data.slice(offset, offset + 4);
  }
}

function setPixel(imageData, x, y, color) {
  const offset = (y * imageData.width + x) * 4;
  imageData.data[offset + 0] = color[0];
  imageData.data[offset + 1] = color[1];
  imageData.data[offset + 2] = color[2];
  imageData.data[offset + 3] = color[0];
}

function colorsMatch(a, b) {
    let thres = 20
  return a[0] >= b[0]-thres && a[0] <= b[0]+thres
         && a[1] >= b[1]-thres && a[1] <= b[1]+thres
         && a[2] >= b[2]-thres && a[2] <= b[2]+thres
         && a[3] >= b[3]-thres && a[3] <= b[3]+thres
}

function floodFill(ctx, x, y, fillColor) {
  // read the pixels in the canvas
  const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
  
  // get the color we're filling
  const targetColor = getPixel(imageData, x, y);
  
  // check we are actually filling a different color
  if (!colorsMatch(targetColor, fillColor)) {
  
    fillPixel(imageData, x, y, targetColor, fillColor);
    
    // put the data back
    ctx.putImageData(imageData, 0, 0);
  }
}

function fillPixel(imageData, x, y, targetColor, fillColor) {
  const currentColor = getPixel(imageData, x, y);
  if (colorsMatch(currentColor, targetColor)) {
    setPixel(imageData, x, y, fillColor);
    fillPixel(imageData, x + 1, y, targetColor, fillColor);
    fillPixel(imageData, x - 1, y, targetColor, fillColor);
    fillPixel(imageData, x, y + 1, targetColor, fillColor);
    fillPixel(imageData, x, y - 1, targetColor, fillColor);
  }
}
</script>

</html>