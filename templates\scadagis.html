<style>
  .scrollable-div {
      max-height: 80vh; /* Full viewport height */
      overflow-y: auto; /* Enable vertical scrolling */
      padding: 4px; /* Padding set to 4 */
  }
  .analytics {
      max-height: 70vh; /* Full viewport height */
      overflow-y: auto; /* Enable vertical scrolling */
      padding: 4px; /* Padding set to 4 */
  }

</style>

<div id="gis-context-menu" class="analytics h-auto overflow-y-auto z-50 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm  dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
  <div class="p-3 space-y-2">
      <p> Caricamento config ....</p>
      <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
  </div>
</div>
<div id="gis-container" style ="height: 65vh" hx-ext="sse"  sse-connect="/webscada/gis_alarms" sse-swap="message" hx-swap="none">
</div>
<div id="gis-alarms-container" style ="height: 25vh" class="overflow-hidden" hx-get="/webscada/page/allarmi" hx-swap="innerHTML" hx-trigger="load">
</div>
<div class="flex absolute top-4 left-1/2 transform -translate-x-1/2  z-50 ">
      <label for="marker-search" class="sr-only">Search</label>
      <div class="relative">
        <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
          <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
          </svg>
        </div>   
        <input data-dropdown-toggle="markerSearch" data-dropdown-placement="bottom" type="text" id="marker-search"   class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Trova impianto">
    </div>
      <!-- marker menu -->
      <div id="markerSearch" class="absolute hidden bg-white rounded-lg shadow w-96 dark:bg-gray-700">

        <ul id="marker-group-ul" class="h-96  px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="markerSearchButton">
        </ul>
    </div>
    <div data-popover="" id="plants-search" role="tooltip" 
    class="absolute overflow-auto z-10 min-w-max  text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-2xl dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 opacity-0 invisible " style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(221px, 731px);" data-popper-placement="bottom-start">
      <div class="p-2">
        <div id="layer-control-menu" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 w-64">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Controllo Layer</h3>
        <div class="space-y-2">
          <!-- Gas Layer Toggle -->
          <div id="toggle-gas-div" class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-gas-layer" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Gas</span>
            </label>
          </div>
          
          <!-- Water Layer Toggle -->
          <div id="toggle-water-div" class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-water-layer" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Idrico</span>
            </label>
          </div>
          
          <!-- Waste Layer Toggle -->
          <div id="toggle-waste-div" class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-waste-layer" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Fognatura</span>
            </label>
          </div>
          
          <!-- Production Layer Toggle -->
          <div id="toggle-prod-div" class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-prod-layer" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Produzione</span>
            </label>
          </div>
          
          <!-- Eco Layer Toggle -->
          <div id="toggle-eco-div" class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-eco-layer" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Eco</span>
            </label>
          </div>
          
          <!-- Cogeneration Layer Toggle -->
          <div id="toggle-coge-div" class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-coge-layer" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Cogenerazione</span>
            </label>
          </div>
          
          <!-- 3D Buildings Toggle -->
          <div class="flex items-center mt-4 pt-2 border-t border-gray-200 dark:border-gray-700">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-buildings" class="sr-only peer" checked>
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">3D layer</span>
            </label>
          </div>
          
          <!-- Add this after the other toggle buttons in the layer controls section -->
          <div id="toggle-alarms-div" class="flex items-center mt-2">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="toggle-alarms-only" class="sr-only peer">
              <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-red-600"></div>
              <span class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">Solo allarmi</span>
            </label>
          </div>
        </div>
        </div>
      </div>                                        
      <div data-popper-arrow="" style="position: absolute; left: 0px; transform: translate(7px, 0px);"></div>
    </div>        
    <button onclick="{document.getElementById('context-menu').classList.add('hidden')}" 
    data-popover-trigger="click" data-popover-target="plants-search" data-popover-placement="bottom-end"
    class="px-2  ms-1 py-1.5 text-xs font-medium bg-blue-700 hover:bg-blue-800 text-gray-900 hover:bg-blue-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
      <svg class="w-6 h-6 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
        <path fill-rule="evenodd" d="M11.906 1.994a8.002 8.002 0 0 1 8.09 8.421 7.996 7.996 0 0 1-1.297 3.957.996.996 0 0 1-.133.204l-.108.129c-.178.243-.37.477-.573.699l-5.112 6.224a1 1 0 0 1-1.545 0L5.982 15.26l-.002-.002a18.146 18.146 0 0 1-.309-.38l-.133-.163a.999.999 0 0 1-.13-.202 7.995 7.995 0 0 1 6.498-12.518ZM15 9.997a3 3 0 1 1-5.999 0 3 3 0 0 1 5.999 0Z" clip-rule="evenodd"/>
      </svg>       
    </button>
</div> 

<script type="text/javascript">
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    resetFilters.classList.add('hidden');
    ackButton.classList.add('hidden');
    // Global variables to store map, layers and data
    var map = null;
    var deckOverlay = null;
    
    // Function to properly cleanup WebGL resources before reloading
    function cleanupMapResources() {
      try {
        // Stop any blinking animations
        if (typeof MarkerBlinker !== 'undefined') {
          MarkerBlinker.stopAlarmBlinking();
        }
        
        // Clear all layers
        gasIconLayer = null;
        waterIconLayer = null;
        wasteIconLayer = null;
        ecoIconLayer = null;
        prodIconLayer = null;
        cogeIconLayer = null;
        circleLayer = null;
        
        // Reset map data
        mapData = [];
        
        // Properly cleanup deck.gl resources
        if (deckOverlay && deckOverlay._deck) {
          deckOverlay.finalize && deckOverlay.finalize();
          deckOverlay._deck && deckOverlay._deck.finalize();
          deckOverlay = null;
        }
        
        // Remove the map
        if (map) {
          map.remove();
          map = null;
        }
        
        // Force garbage collection hint
        setTimeout(() => {
          // This timeout helps ensure resources are released before new ones are created
        }, 50);
      } catch (e) {
        console.error("Error during cleanup:", e);
      }
    }
    

    
    var mapData = [];
    var gasIconLayer = null;
    var waterIconLayer = null;
    var wasteIconLayer = null; 
    var ecoIconLayer= null;
    var prodIconLayer = null;
    var cogeIconLayer = null;
    var circleLayer = null;
    var layersVisibility = [];
    var searchResults = document.getElementById('marker-group-ul');
    var searchMarker = document.getElementById('marker-search');
    var ctxMenu = document.getElementById("gis-context-menu");   
    var {MapboxOverlay, ScatterplotLayer, ArcLayer, IconLayer} = deck;
    try{
        map.remove();
        map = null;
    }catch{
        ;
    }

    async function fetchAndParseJSON(url) {
        try {
            // Fetch the JSON file
            const response = await fetch(url);

            // Check if the response is OK (status code 200-299)
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Parse the JSON file
            const data = await response.json();

            // Log the parsed data
            return(data)
        } catch (error) {
            // Handle any errors
            console.error('Error fetching or parsing JSON:', error);
        }
    }

    // Fetch data function - returns a promise
    async function fetchGISData() {
      try {
        const data = await fetchAndParseJSON("/webscada/gisdata");
        mapData = data; // Store data globally
        return data;
      } catch (error) {
        console.error('Error fetching GIS data:', error);
        return [];
      }
    }


    // Setup 3D buildings
    var buildingLayerId = null;

    function setup3DBuildings(firstLabelLayerId) {
      /*if(! map.getLayer('3d-buildings'))
        return*/
      map.removeLayer('building');
      map.removeLayer('building-top');
      
      map.addLayer({
        'id': '3d-buildings',
        'source': 'carto',
        'source-layer': 'building',
        'type': 'fill-extrusion',
        'minzoom': 15,
        'paint': {
          'fill-extrusion-color': '#aaa',
          'fill-extrusion-height': [
            "interpolate", ["linear"], ["zoom"],
            15, 0,
            15.05, ["get", "render_height"]
          ],
          'fill-extrusion-base': [
            "interpolate", ["linear"], ["zoom"],
            15, 0,
            15.05, ["get", "render_min_height"]
          ],
          'fill-extrusion-opacity': .9
        }
      }, firstLabelLayerId);
      
      // Store the building layer ID for later use
      buildingLayerId = '3d-buildings';
    }

    // Function to update layer toggle visibility based on data
    function updateLayerToggleVisibility() {
      // Check each layer and hide its toggle button if empty
      const layers = [
        { id: 'gasIconLayer', data: gasIconLayer?.props?.data, toggleId: 'toggle-gas-div' },
        { id: 'waterIconLayer', data: waterIconLayer?.props?.data, toggleId: 'toggle-water-div' },
        { id: 'wasteIconLayer', data: wasteIconLayer?.props?.data, toggleId: 'toggle-waste-div' },
        { id: 'prodIconLayer', data: prodIconLayer?.props?.data, toggleId: 'toggle-prod-div' },
        { id: 'ecoIconLayer', data: ecoIconLayer?.props?.data, toggleId: 'toggle-eco-div' },
        { id: 'cogeIconLayer', data: cogeIconLayer?.props?.data, toggleId: 'toggle-coge-div' }
      ];
      
      layers.forEach(layer => {
        const toggleElement = document.getElementById(layer.toggleId);
        if (toggleElement) {
          if (layer.data && layer.data.length > 0) {
            toggleElement.style.display = 'block';
          } else {
            toggleElement.style.display = 'none';
          }
        }
      });
    }

    // Create icon layers based on data
    function createIconLayers(data) {

      const iconAtlas = { 
        "marker": {
          "x": 0, "y": 0, "width": 512, "height": 512, "anchorY": 512, "mask": false
        },
        "marker-blink": {
          "x": 0, "y": 0, "width": 512, "height": 512, "anchorY": 512, "mask": true
        },
      };
      
      // Create all icon layers
      gasIconLayer = createGasIconLayer(data, iconAtlas, IconLayer);
      waterIconLayer = createWaterIconLayer(data, iconAtlas, IconLayer);
      wasteIconLayer = createWasteIconLayer(data, iconAtlas, IconLayer);
      prodIconLayer = createProdIconLayer(data, iconAtlas, IconLayer);
      ecoIconLayer = createEcoIconLayer(data, iconAtlas, IconLayer);
      cogeIconLayer = createCogeIconLayer(data, iconAtlas, IconLayer);

      const allLayers = [gasIconLayer, waterIconLayer, wasteIconLayer, prodIconLayer, ecoIconLayer,cogeIconLayer];
      
      // Update toggle button visibility based on layer data
      updateLayerToggleVisibility();

      // Check for alarms and start blinking if needed
      checkAndStartAlarmBlinking(allLayers);
      return allLayers;
    }

    // Helper functions to create each layer
    function createGasIconLayer(data, iconAtlas, IconLayer) {
      return new IconLayer({
        id: 'gasIconLayer',
        data: data.filter(item => item.COMMODITY === "GAS"),
        //getColor: 'yellow',
        getIcon: d => 'marker',
        getPosition: d => [d.LONGITUDINE, d.LATITUDINE],
        getSize: 30,
        iconAtlas: '/webscada/static/img/fire.png',
        iconMapping: iconAtlas,
        pickable: true,
        visible: layersVisibility.gasIconLayer ?? true,          
        onClick: ({ object, pixel }) => {
          htmx.ajax('GET', `/webscada/gisanalytics/${object.INDEX}`, {
            target: '#gis-context-menu', 
            swap: 'innerHTML'
          }).then(() => {
            positionContextMenu(pixel[0], pixel[1]);
          });
        }
      });
    }

    // Similar functions for other layers (water, waste, prod, eco)
    function createWaterIconLayer(data, iconAtlas, IconLayer) {
      return new IconLayer({    
        id: 'waterIconLayer',
        data: data.filter(item => item.COMMODITY  && item.COMMODITY.includes("Acqua")),
        //getColor: 'yellow',
        getIcon: d => 'marker',
        getPosition: d => [Number(d.LONGITUDINE), Number(d.LATITUDINE)],
        getSize: 30,
        iconAtlas: '/webscada/static/img/drop.png',
        iconMapping: iconAtlas,
        pickable: true,
        isVisible: layersVisibility.waterIconLayer ?? false,          
        onClick: ({ object, pixel }) => {
          htmx.ajax('GET', `/webscada/gisanalytics/${object.INDEX}`, {
            target: '#gis-context-menu', 
            swap: 'innerHTML'
          }).then(() => {
            positionContextMenu(pixel[0], pixel[1]);
          });
        }
      });
    }

  // Create waste icon layer
  function createWasteIconLayer(data, iconAtlas, IconLayer) {
    return new IconLayer({
      id: 'wasteIconLayer',
      data: data.filter(item =>  item.COMMODITY && item.COMMODITY.includes("Fogna")),
      //getColor: 'yellow',
      getIcon: d => 'marker',
      getPosition: d => [Number(d.LONGITUDINE), Number(d.LATITUDINE)],
      getSize: 30,
      iconAtlas: '/webscada/static/img/waste.png',
      iconMapping: iconAtlas,
      pickable: true,
      isVisible: layersVisibility.wasteIconLayer ?? false,      
      onClick: ({ object, pixel }) => {
        htmx.ajax('GET', `/webscada/gisanalytics/${object.INDEX}`, {
          target: '#gis-context-menu', 
          swap: 'innerHTML'
        }).then(() => {
          positionContextMenu(pixel[0], pixel[1]);
        });
      }
    });
  }

  // Create production icon layer
  function createProdIconLayer(data, iconAtlas, IconLayer) {
    return new IconLayer({
      id: 'prodIconLayer',
      data: data.filter(item => item.COMMODITY && item.COMMODITY.includes("Produzione")),
      //getColor: 'yellow',
      getIcon: d => 'marker',
      getPosition: d => [Number(d.LONGITUDINE), Number(d.LATITUDINE)],
      getSize: 30,
      iconAtlas: '/webscada/static/img/prod.png',
      iconMapping: iconAtlas,
      pickable: true,
      isVisible: layersVisibility.prodIconLayer ?? false,
      onClick: ({ object, pixel }) => {
        htmx.ajax('GET', `/webscada/gisanalytics/${object.INDEX}`, {
          target: '#gis-context-menu', 
          swap: 'innerHTML'
        }).then(() => {
          positionContextMenu(pixel[0], pixel[1]);
        });
      }
    });
  }

  // Create eco icon layer
  function createEcoIconLayer(data, iconAtlas, IconLayer) {
    return new IconLayer({
      id: 'ecoIconLayer',
      data: data.filter(item => item.COMMODITY && item.COMMODITY.includes("Igiene Urbana")),
      //getColor: 'yellow',
      getIcon: d => 'marker',
      getPosition: d => [Number(d.LONGITUDINE), Number(d.LATITUDINE)],
      getSize: 30,
      iconAtlas: '/webscada/static/img/eco.png',
      iconMapping: iconAtlas,
      pickable: true,
      isVisible: layersVisibility.ecoIconLayer ?? false,
      onClick: ({ object, pixel }) => {
        htmx.ajax('GET', `/webscada/gisanalytics/${object.INDEX}`, {
          target: '#gis-context-menu', 
          swap: 'innerHTML'
        }).then(() => {
          positionContextMenu(pixel[0], pixel[1]);
        });
      }
    });
  }

    // Create coge icon layer
    function createCogeIconLayer(data, iconAtlas, IconLayer) {
    return new IconLayer({
      id: 'cogeIconLayer',
      data: data.filter(item => item.COMMODITY && item.COMMODITY.includes("cogenerazione")),
      //getColor: 'yellow',
      getIcon: d => 'marker',
      getPosition: d => [Number(d.LONGITUDINE), Number(d.LATITUDINE)],
      getSize: 30,
      iconAtlas: '/webscada/static/img/coge.png',
      //'/webscada/static/img/coge.png',
      iconMapping: iconAtlas,
      pickable: true,
      isVisible: layersVisibility.cogeIconLayer ?? false,      
      onClick: ({ object, pixel }) => {
        htmx.ajax('GET', `/webscada/gisanalytics/${object.INDEX}`, {
          target: '#gis-context-menu', 
          swap: 'innerHTML'
        }).then(() => {
          positionContextMenu(pixel[0], pixel[1]);
        });
      }
    });
  }
    // Create and add deck overlay
    function createDeckOverlay(layers, firstLabelLayerId) {
      circleLayer =  new ScatterplotLayer({
            id: 'deckgl-circle',
            data: [
              {position: [11.12108, 46.06787], color: [0, 0, 0], radius: 50000}
            ],
            getPosition: d => d.position,
            getFillColor: d => d.color,
            getRadius: d => d.radius,
            opacity: 0.3,
            beforeId: firstLabelLayerId
          })      
      deckOverlay = new MapboxOverlay({
        interleaved: true,
        getTooltip: ({object}) => object && `${object.NAME}\n${object.PAGE}`,
        layers: [
          ...layers,
          circleLayer
        ]
      });
      return deckOverlay;
    }

    // Main initialization function
    async function initializeGIS() {
      // Initialize the map first
      map = new maplibregl.Map({
        container: 'gis-container',
        style: 'https://basemaps.cartocdn.com/gl/positron-gl-style/style.json',
        center: [11.12108, 46.0],
        zoom: 9,
        pitch: 50,
        antialias: true
      });

      
      // Fetch data
      //const data = await fetchGISData();
      // Wait for map to be loaded
      map.on('style.load', () => {
        const firstLabelLayerId = map.getStyle().layers.find(layer => layer.type === 'symbol').id;
        setup3DBuildings(firstLabelLayerId);
        // Create icon layers
        fetchGISData().then(data =>{
          const iconLayers = createIconLayers(data);
          
          // Create and add deck overlay
          deckOverlay = createDeckOverlay(iconLayers, firstLabelLayerId);
          map.addControl(deckOverlay);
          
          // Setup event listeners
          searchList(data)
          setupEventListeners();
          //blinkSpecificMarker(waterIconLayer, 'NAME', 'P634');
        })
        

      });

    }

    // Search results 
    function searchList(list){
      searchResults.innerHTML = ""

      list.forEach(item => {
            const li = document.createElement('li');
            const coordinates = [item.LONGITUDINE,item.LATITUDINE]
            li.innerHTML =  `
                <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                <label  onclick="flyTo(${coordinates})" class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">${item['NAME']}</label>
                </div>`;
            searchResults.appendChild(li);
        });
    }

    // Setup event listeners
    function setupEventListeners() {
      // Debounce function to limit how often the search runs
      function debounce(func, wait) {
        let timeout;
        return function(...args) {
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(this, args), wait);
        };
      }
      
      // Create an index for faster searching
      const searchIndex = new Map();
      
      // Index all markers for faster search
      function buildSearchIndex() {
        // Clear existing index
        searchIndex.clear();
        
        const allLayers = [
          { layer: gasIconLayer, type: 'gas' },
          { layer: waterIconLayer, type: 'water' },
          { layer: wasteIconLayer, type: 'waste' },
          { layer: prodIconLayer, type: 'production' },
          { layer: ecoIconLayer, type: 'eco' },
          { layer: cogeIconLayer, type : 'coge'}
        ];
        
        // Track items we've already indexed by a unique identifier
        const indexedItems = new Set();
        
        allLayers.forEach(({ layer, type }) => {
          if (layer && layer.props && layer.props.data) {
            layer.props.data.forEach(item => {
              if (item.NAME) {
                // Create a unique identifier using INDEX or combination of coordinates
                const uniqueId = item.INDEX || `${item.LONGITUDINE}-${item.LATITUDINE}`;
                
                // Skip if we've already indexed this item
                if (indexedItems.has(uniqueId)) {
                  return;
                }
                
                // Mark as indexed
                indexedItems.add(uniqueId);
                
                // Add to search index
                const key = item.NAME.toLowerCase();
                if (!searchIndex.has(key)) {
                  searchIndex.set(key, []);
                }
                searchIndex.get(key).push({ ...item, type });
              }
            });
          }
        });
        
      }
      
      // Build the index once data is loaded
      buildSearchIndex();
      
      // Optimized search function
      const performSearch = debounce(function(queryString) {
        // If query is empty, show all results
        if (!queryString || queryString.trim() === '') {
          // Collect all items from the index
          const allResults = [];
          searchIndex.forEach(items => {
            items.forEach(item => allResults.push(item));
          });
          
          // Sort results alphabetically by name for better usability
          allResults.sort((a, b) => a.NAME.localeCompare(b.NAME));
          
          // Limit to prevent performance issues with very large datasets
          const limitedResults = allResults.slice(0, 100);
          searchList(limitedResults);
          return;
        }
        
        // For very short queries (1 character), still require it to be meaningful
        /*if (queryString.length < 2) {
          searchList([]);
          return;
        }*/
        
        queryString = queryString.toLowerCase();
        const results = [];
        const maxResults = 50; // Limit results for performance
        
        // First try exact matches
        searchIndex.forEach((items, key) => {
          if (key === queryString) {
            items.forEach(item => results.push(item));
          }
        });
        
        // Then try contains matches if we need more results
        if (results.length < maxResults) {
          searchIndex.forEach((items, key) => {
            if (results.length < maxResults && key.includes(queryString) && key !== queryString) {
              items.forEach(item => {
                if (results.length < maxResults) {
                  results.push(item);
                }
              });
            }
          });
        }
        
        searchList(results.slice(0, 100));
        
        // If only one result is found, animate it
        if (results.length === 1) {
          flyTo(results[0].LONGITUDINE, results[0].LATITUDINE);
        }
      }, 200); // 300ms debounce
      
      // Search functionality
      searchMarker.addEventListener('input', function(event) {
        performSearch(event.target.value);
      });

      // Layer visibility toggles
      document.getElementById('toggle-gas-layer').addEventListener('change', function(e) {
        toggleLayerVisibility('gasIconLayer', e.target.checked);
      });
      
      document.getElementById('toggle-water-layer').addEventListener('change', function(e) {
        toggleLayerVisibility('waterIconLayer', e.target.checked);
      });
      
      document.getElementById('toggle-waste-layer').addEventListener('change', function(e) {
        toggleLayerVisibility('wasteIconLayer', e.target.checked);
      });
      
      document.getElementById('toggle-prod-layer').addEventListener('change', function(e) {
        toggleLayerVisibility('prodIconLayer', e.target.checked);
      });
      
      document.getElementById('toggle-eco-layer').addEventListener('change', function(e) {
        toggleLayerVisibility('ecoIconLayer', e.target.checked);
      });
      
      document.getElementById('toggle-coge-layer').addEventListener('change', function(e) {
        toggleLayerVisibility('cogeIconLayer', e.target.checked);
      });
      
      document.getElementById('toggle-buildings').addEventListener('change', function(e) {
        toggle3DBuildings(e.target.checked);
      });
    }


    initializeGIS();


document.addEventListener("click",(e) => {
  if (!ctxMenu.contains(e.target)) {
    ctxMenu.classList.add('hidden');
  }

  })   

function positionContextMenu(x, y) {
  // Get viewport dimensions (visible area only)
  const viewportWidth = document.documentElement.clientWidth;
  const viewportHeight = document.documentElement.clientHeight;
  
  // Make the menu visible but off-screen to measure its dimensions
  ctxMenu.style.visibility = 'hidden';
  ctxMenu.classList.remove('hidden');
  
  // Get context menu dimensions
  const menuWidth = ctxMenu.offsetWidth;
  const menuHeight = ctxMenu.offsetHeight;
  
  // Calculate positions that keep menu in viewport
  let posX = x;
  let posY = y;
  
  // Adjust horizontal position if needed
  if (x + menuWidth > viewportWidth) {
    posX = Math.max(10, x - menuWidth); // Position to the left of the click
  }
  
  // Adjust vertical position if needed
  if (y + menuHeight > viewportHeight) {
    posY = Math.max(10, y - menuHeight); // Position above the click
  }
  
  // Apply the adjusted position
  ctxMenu.style.top = `${posY+50}px`;
  ctxMenu.style.left = `${posX}px`;
  ctxMenu.style.visibility = 'visible';
}

function getPage(e, page) {
  htmx.ajax('GET', `/webscada/gisanalytics/${page}`, {
    target: '#gis-context-menu', 
    swap: 'innerHTML'
  }).then(() => {
    positionContextMenu(e.pageX, e.pageY);
  });
}
function deleteElement(id) {
  const element = document.getElementById(id);
  if (element) {
      element.remove();
  }
}

function flyTo(long,lat){
    marker = document.getElementById("markerSearch")
    //marker.classList.toggle('hidden')
    map.flyTo({center: [long,lat],
          zoom: 15,
          essential: true
      });
}

// Simplified MarkerBlinker with complete re-rendering on visibility change
var MarkerBlinker = {
  blinkInterval: null,
  isBlinking: false,
  blinkingLayer: null,
  originalLayerData: null,
  blinkingMarkerIndex: -1,
  allLayers: null,
  alarmBlinkingEnabled: false,
  visibilityState: 'visible',
  
  init() {
    // Handle tab visibility changes
    document.addEventListener('visibilitychange', () => {
      const isVisible = document.visibilityState === 'visible';
      this.handleVisibilityChange(isVisible);
    });
    
    // Backup with focus events
    window.addEventListener('blur', () => {
      this.handleVisibilityChange(false);
    });
    
    window.addEventListener('focus', () => {
      this.handleVisibilityChange(true);
    });
  },
  
  handleVisibilityChange(isVisible) {
    this.visibilityState = isVisible ? 'visible' : 'hidden';
    
    if (isVisible) {
      this.pauseBlinking();
      this.forceRerender();
    } else {
      this.pauseBlinking();
    }
  },
  
  // Force complete re-rendering of the map and layers
  forceRerender() {
    try {      
      // Stop any current blinking
      this.pauseBlinking();
      
      // Re-create all layers from the original data
      if (mapData && mapData.length > 0) {
        const firstLabelLayerId = map.getStyle().layers.find(layer => layer.type === 'symbol').id;
        
        // Re-setup 3D buildings
        //setup3DBuildings(firstLabelLayerId);
        
        // Re-create icon layers
        const iconLayers = createIconLayers(mapData);
        
        // Re-create and add deck overlay
        if (deckOverlay && deckOverlay._deck) {
          //deckOverlay.setProps({ layers: iconLayers });
          deckOverlay.setProps({ layers: [...iconLayers,circleLayer] });
        } else {
          // If deck overlay is completely gone, recreate it
          deckOverlay = createDeckOverlay(iconLayers, firstLabelLayerId);
          map.addControl(deckOverlay);
        }
        
        // Resume blinking if needed
        setTimeout(() => {
          if (this.alarmBlinkingEnabled) {
            this.startAlarmBlinking(iconLayers);
          }
        }, 500);
                
      } else {
        console.warn('Cannot re-render: mapData not available');
      }
    } catch (e) {
      console.error('Error during re-render:', e);
    }
  },
  
  pauseBlinking() {
    if (this.blinkInterval) {
      clearInterval(this.blinkInterval);
      this.blinkInterval = null;
    }
  },
  
  // Add the missing stopAlarmBlinking method
  stopAlarmBlinking() {
    this.pauseBlinking();
    this.alarmBlinkingEnabled = false;
    this.allLayers = null;
  },
  
  // Start blinking all markers with alarm flag across all layers
  startAlarmBlinking(allLayers) {
    // Stop any existing blinking
    this.stopAlarmBlinking();
    
    // Enable alarm blinking mode
    this.alarmBlinkingEnabled = true;
    
    // Store original layers and their data
    this.allLayers = allLayers.filter(layer => 
      layer && layer.props && layer.props.data && layer.props.data.length > 0
    );
    
    // Start the blinking interval only if page is visible
    if (this.visibilityState === 'visible') {
      this.blinkInterval = setInterval(() => this.updateAlarmBlinkingMarkers(), 500);
    }
  },
  
  // Add the updateAlarmBlinkingMarkers method if it's missing
  updateAlarmBlinkingMarkers() {
    if (!this.allLayers || !this.alarmBlinkingEnabled) {
      return;
    }
    
    // Toggle the blinking state
    this.isBlinking = !this.isBlinking;
    
    // Update each layer
    this.allLayers.forEach(layer => {
      // Skip if this layer should be hidden in alarm-only mode
      if (alarmsOnlyMode) {
        // Check if this layer has any items with alarms
        const hasAlarms = layer.props.data.some(item => item.allarm === true);
        if (!hasAlarms) {
          return; // Skip this layer entirely
        }
      }
      
      // Create a modified copy of the data with blinking property for alarm markers
      let modifiedData = layer.props.data;
      
      // In alarm-only mode, filter to only show alarm items
      if (alarmsOnlyMode) {
        modifiedData = modifiedData.filter(item => item.allarm === true);
      }
      
      // Add blinking property
      modifiedData = modifiedData.map(item => ({
        ...item,
        _isBlinking: item.allarm === true && this.isBlinking
      }));
      
      // Create a new layer with the modified data
      const updatedLayer = new IconLayer({
        ...layer.props,
        id: layer.id,
        data: modifiedData,
        getColor: d => [230,0,38],
        getSize: d => {
          // Increase size when blinking
          if (d._isBlinking) {
            return layer.props.getSize * 1.2; // 20% larger when blinking
          }
          return layer.props.getSize;
        },
        getIcon: d => {
          if (d._isBlinking) {
            return 'marker-blink';
          }
          return 'marker';
        },
        iconAtlas: layer.props.iconAtlas,
        iconMapping: layer.props.iconMapping,
        pickable: layer.props.pickable,
        onClick: layer.props.onClick,
        visible: alarmsOnlyMode ? (modifiedData.length > 0) : (layersVisibility[layer.id] ?? true),
      });
      
      // Update the layer
      this.updateDeckLayer(updatedLayer);
    });
  },
  
  // Helper to update the deck layer
  updateDeckLayer(updatedLayer) {
    if (!deckOverlay || !deckOverlay._deck) return;
    
    const currentLayers = deckOverlay._deck.props.layers || [];
    const newLayers = currentLayers.map(layer => 
      layer.id === updatedLayer.id ? updatedLayer : layer
    );
    
    deckOverlay.setProps({ layers: newLayers });
  }
};

// Add a function to check for alarms and start blinking if needed
function checkAndStartAlarmBlinking(layers) {
  // Check if any markers have the alarm flag
  const hasAlarms = layers.some(layer => 
    layer && layer.props && layer.props.data && 
    layer.props.data.some(item => item.allarm === true)
  );
  
  if (hasAlarms) {
    MarkerBlinker.startAlarmBlinking(layers);
  } else {
    ;//console.log('No markers with alarms found');
  }
}

// Updated blinkSpecificMarker function that uses the MarkerBlinker
function blinkSpecificMarker(targetLayer, searchKey, searchValue) {
  // Start the blinking effect
  const marker = MarkerBlinker.startBlinking(targetLayer, searchKey, searchValue);
  
  if (marker) {
    // Fly to the marker
    map.flyTo({
      center: [Number(marker.LONGITUDINE), Number(marker.LATITUDINE)],
      zoom: 15,
      essential: true
    });
  }
  
  return marker;
}

  // Initialize the MarkerBlinker when the page loads
  MarkerBlinker.init();

  // Add these functions to handle layer visibility
  function toggleLayerVisibility(layerId, isVisible) {
    if (!deckOverlay || !deckOverlay._deck) return;
    layersVisibility[layerId] = isVisible;
    const currentLayers = deckOverlay._deck.props.layers || [];
    const newLayers = currentLayers.map(layer => {
      if (layer && layer.id === layerId) {
        // Create a new layer with updated visibility
        return new IconLayer({
          ...layer.props,
          visible: isVisible
        });
      }
      return layer;
    });
    
    deckOverlay.setProps({ layers: newLayers });
  }

  function toggle3DBuildings(isVisible) {
    if (!map) return;
    
    const visibility = isVisible ? 'visible' : 'none';
    
    // Toggle visibility of 3D building layers
    if (map.getLayer('3d-buildings')) {
      map.setLayoutProperty('3d-buildings', 'visibility', visibility);
    }
    
    // You might need to adjust this if you have multiple building layers
    if (map.getLayer('building-extrusion')) {
      map.setLayoutProperty('building-extrusion', 'visibility', visibility);
    }
  }
</script>
<script>
    function sseScadagis(msg){
      if (msg.detail.target.url.includes("/gis_alarms"))
              {
                const result = JSON.parse(msg.detail.data); 
                if (!mapData || mapData.length === 0) {
                  console.warn('Map data not loaded yet, cannot update alarms');
                  return;
                }
              
                let alarmsUpdated = false;
                let out = []
            
              // Update the alarm status in our map data
              mapData.forEach(item => {
                  item.allarm = result.indexOf(item.NAME) > -1;
                  if (item.allarm) {
                    alarmsUpdated = true;
                    out.push(item)
                  }
                });
              if(alarmsUpdated){
                // Stop current blinking          
                MarkerBlinker.stopAlarmBlinking();
                const firstLabelLayerId = map.getStyle().layers.find(layer => layer.type === 'symbol').id;
                //setup3DBuildings(firstLabelLayerId);
                // Recreate layers with updated data
                const iconLayers = createIconLayers(mapData);        
                // Update the deck overlay with new layers
                if (deckOverlay && deckOverlay._deck) {
                  deckOverlay.setProps({ layers: [...iconLayers,circleLayer] });
                }
              }
            }
    }
    /*  htmx.on('htmx:sseMessage', (msg) => {
        if (msg.detail.target.url.includes("/gis_alarms"))
          sseScadagis(msg);
      })
    */
</script>
<script>
  // Store the original state of all layers
  var originalLayersState = {};
  var alarmsOnlyMode = false;
  
  // Add event listener for the alarms-only toggle
  // document.addEventListener('DOMContentLoaded', function() {
    var alarmsToggle = document.getElementById('toggle-alarms-only');
    if (alarmsToggle) {
      alarmsToggle.addEventListener('change', function(e) {
        toggleAlarmsOnly(e.target.checked);
      });
    }
  //});
  
  // Function to toggle showing only plants in alarm
  function toggleAlarmsOnly(showOnlyAlarms) {
    if (!deckOverlay || !deckOverlay._deck) return;
    
    alarmsOnlyMode = showOnlyAlarms;
    const currentLayers = deckOverlay._deck.props.layers || [];
    
    if (showOnlyAlarms) {
      // Save current visibility state before changing
      currentLayers.forEach(layer => {
        if (layer && layer.id) {
          originalLayersState[layer.id] = layersVisibility[layer.id] ?? true;
        }
      });
      
      // Update layers to show only those with alarms
      const newLayers = currentLayers.map(layer => {
        if (layer && layer.props && layer.props.data) {
          // Check if this layer has any items with alarms
          const hasAlarms = layer.props.data.some(item => item.allarm === true);
          
          // If no alarms in this layer, hide it
          if (!hasAlarms) {
            layersVisibility[layer.id] = false;
            return new IconLayer({
              ...layer.props,
              visible: false
            });
          } else {
            // For layers with alarms, filter to show only alarm items
            const alarmData = layer.props.data.filter(item => item.allarm === true);
            return new IconLayer({
              ...layer.props,
              data: alarmData,
              visible: true
            });
          }
        }
        return layer;
      });
      
      //deckOverlay.setProps({ layers: newLayers });
      deckOverlay.setProps({ layers: [circleLayer,...newLayers] });
      
      // Update toggle buttons UI to match the current state
      updateToggleButtonsUI();
      
    } else {
      // Restore original visibility and data
      restoreAllLayers();
    }
  }
  
  // Function to restore all layers to their original state
  function restoreAllLayers() {
    if (!mapData || !deckOverlay || !deckOverlay._deck) return;
    
    // Restore original visibility settings
    for (const layerId in originalLayersState) {
      layersVisibility[layerId] = originalLayersState[layerId];
    }
    
    // Recreate all layers with full data
    const iconLayers = createIconLayers(mapData);
    
    // Update the deck overlay with restored layers
    deckOverlay.setProps({ layers: [...iconLayers, circleLayer] });
    
    // Update toggle buttons UI to match restored state
    updateToggleButtonsUI();
  }
  
  // Function to update toggle button UI states
  function updateToggleButtonsUI() {
    // Update each layer toggle to match the current visibility state
    const toggles = [
      { id: 'toggle-gas-layer', layerId: 'gasIconLayer' },
      { id: 'toggle-water-layer', layerId: 'waterIconLayer' },
      { id: 'toggle-waste-layer', layerId: 'wasteIconLayer' },
      { id: 'toggle-prod-layer', layerId: 'prodIconLayer' },
      { id: 'toggle-eco-layer', layerId: 'ecoIconLayer' },
      { id: 'toggle-coge-layer', layerId: 'cogeIconLayer' }
    ];
    
    toggles.forEach(toggle => {
      const toggleElement = document.getElementById(toggle.id);
      if (toggleElement) {
        toggleElement.checked = layersVisibility[toggle.layerId] ?? false;
        
        // Disable regular toggles when in alarm-only mode
        toggleElement.disabled = alarmsOnlyMode;
        
        // Update the parent div to show disabled state
        const parentDiv = document.getElementById(`toggle-${toggle.layerId.replace('IconLayer', '')}-div`);
        if (parentDiv) {
          if (alarmsOnlyMode) {
            parentDiv.classList.add('opacity-50');
          } else {
            parentDiv.classList.remove('opacity-50');
          }
        }
      }
    });
  }
  
  // Modify the existing toggleLayerVisibility function to respect alarm-only mode
  var originalToggleLayerVisibility = toggleLayerVisibility;
  toggleLayerVisibility = function(layerId, isVisible) {
    // If in alarm-only mode, don't allow manual toggling
    if (alarmsOnlyMode) return;
    
    // Otherwise use the original function
    originalToggleLayerVisibility(layerId, isVisible);
  };
  
  // Modify the sseScadagis function to respect alarm-only mode
  var originalSseScadagis = sseScadagis;
  sseScadagis = function(msg) {
    originalSseScadagis(msg);
    
    // If we're in alarm-only mode, reapply the filter after alarm updates
    if (alarmsOnlyMode) {
      setTimeout(() => toggleAlarmsOnly(true), 100);
    }
  };
</script>
