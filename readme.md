todo :
- user management :
    - store and retreive user info (email, level) in the jwt token
    - access control on every request

- analytics pages :
  handle ux feedback for writes 

- synoptics pages:
    - optimise floodfill on the browser or find alternatives 
    - handle js errors better    

- ticketing/support system

user dashboard
Nuovo ticket

admin dashboard(table)

ticketid, categoria, soggetto, nome, tecnico, creato, aggiornato,stato, priorità, note

          gas:                                                     
            -nuovo utente
            -nuova periferica
            -variazione periferica
            -collaudo
            -taratura   
            -allarmi  
            -richiesta dati
            -custom

          catodica
            -nuovo utente
            -nuova periferica
            -variazione periferica
            -collaudo
            -taratura   
            -allarmi  

          acqua:
            -nuovo utente
            -nuova periferica
            -variazione periferica
            -collaudo
            -taratura   
            -allarmi 

          cogenerazione:
            -nuovo utente
            -nuova periferica
            -variazione periferica
            -collaudo
            -taratura   
            -allarmi          

ticket workflow:
     templates : 
      - name 
      - fasi
      - tecnici

helpdesk.workflow.templates.{id} put/delete key