
<div class="relative overflow-x-auto pt-16" id="alarms-table" ></div>
    <table id="alarmsTable" class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4">
                    <div class="flex items-center">
                        <input id="checkbox-all" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-all" class="sr-only">checkbox</label>
                    </div>
                </th>
                <th scope="col" class="px-6 py-3 text-center">timestamp</th>
                <th scope="col" class="px-6 py-3 text-center">nome</th>
                <th scope="col" class="px-6 py-3 text-center">messaggio</th>
                <th scope="col" class="px-6 py-3 text-center">periferica</th>
                <th scope="col" class="px-6 py-3 text-center">livello</th>                
            </tr>
        </thead>
        <tbody hx-ext="sse"  sse-connect="/webscada/rtalarms" sse-swap="message" >
        </tbody>
    </table>
</div>