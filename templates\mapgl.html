<div id="container" style="height: 80px"></div>


<script type="text/javascript">
async function fetchAndParseJSON(url) {
    try {
        // Fetch the JSON file
        const response = await fetch(url);

        // Check if the response is OK (status code 200-299)
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Parse the JSON file
        const data = await response.json();

        // Log the parsed data
        return(data)
    } catch (error) {
        // Handle any errors
        console.error('Error fetching or parsing JSON:', error);
    }
}

fetchAndParseJSON("/webscada/gisdata").then(data =>{

  const {MapboxOverlay, ScatterplotLayer, ArcLayer, IconLayer} = deck;

  const icoLayer = new IconLayer({
          id: 'IconLayer',
          data: data,//'https://raw.githubusercontent.com/visgl/deck.gl-data/master/website/bart-stations.json',
          //getColor: d => [Math.sqrt(d.exits), 140, 0],
          getIcon: d => 'marker',
          getPosition: d => [d.LONGITUDINE,d.LATITUDINE],
          getSize: 40,
          iconAtlas: 'https://raw.githubusercontent.com/visgl/deck.gl-data/master/website/icon-atlas.png',
          iconMapping: 'https://raw.githubusercontent.com/visgl/deck.gl-data/master/website/icon-atlas.json',
          pickable: true,
          onClick: ({ object }) => {
          const sidePanel = document.getElementById('side-panel');
          sidePanel.innerHTML = `
            <h2>${object.ID_MISURA}</h2>
            <p>ID periferica: ${object.ID_PERIFERICA}</p>
            <p>ID tipologia misura: ${object.ID_TIPOLOGIA_MISURA}</p>
            <p>Commodity: ${object.COMMODITY}</p>
            <p>Identificativo oggetto mappa: ${object.IDENTIFICATIVO_OGGETTO_MAPPA}</p>
          `;
        }
        });

  const map = new maplibregl.Map({
    container: 'container',
    style: 'https://basemaps.cartocdn.com/gl/positron-gl-style/style.json',
    center: [11.12108, 46.06787],
    zoom: 12,
    pitch: 60,
    antialias: true
  });

  map.on('style.load', () => {
    const firstLabelLayerId = map.getStyle().layers.find(layer => layer.type === 'symbol').id;

    //map.removeLayer('building')
    //map.removeLayer('building-top')
    map.addLayer({
      'id': '3d-buildings',
      'source': 'carto',
      'source-layer': 'building',
      'type': 'fill-extrusion',
      'minzoom': 15,
      'paint': {
          'fill-extrusion-color': '#aaa',

          // use an 'interpolate' expression to add a smooth transition effect to the
          // buildings as the user zooms in
          'fill-extrusion-height': [
              "interpolate", ["linear"], ["zoom"],
              15, 0,
              15.05, ["get", "render_height"]
          ],
          'fill-extrusion-base': [
              "interpolate", ["linear"], ["zoom"],
              15, 0,
              15.05, ["get", "render_min_height"]
          ],
          'fill-extrusion-opacity': .6
      }
    }, firstLabelLayerId);

    const deckOverlay = new MapboxOverlay({
      interleaved: true,
      getTooltip: ({object}) => object && `${object.ID_MISURA}
      ${object.ID_PERIFERICA}`,
      layers: [icoLayer,
              new ScatterplotLayer({
                id: 'deckgl-circle',
                data: [
                  {position: [11.12108, 46.06787], color: [255, 0, 0], radius: 100000}
                ],
                getPosition: d => d.position,
                getFillColor: d => d.color,
                getRadius: d => d.radius,
                opacity: 0.3,
                beforeId: firstLabelLayerId
              }), 
       /* new ArcLayer({
          id: 'deckgl-arc',
          data: [
            {source: [-122.3998664, 37.7883697], target: [-122.400068, 37.7900503]}
          ],
          getSourcePosition: d => d.source,
          getTargetPosition: d => d.target,
          getSourceColor: [255, 208, 0],
          getTargetColor: [0, 128, 255],
          getWidth: 8
        })*/
      ]
    });

    map.addControl(deckOverlay);
  });
});

</script>