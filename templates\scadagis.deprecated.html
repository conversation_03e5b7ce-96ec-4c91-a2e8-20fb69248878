<style>
  .scrollable-div {
      max-height: 80vh; /* Full viewport height */
      overflow-y: auto; /* Enable vertical scrolling */
      padding: 4px; /* Padding set to 4 */
  }
  .analytics {
      max-height: 70vh; /* Full viewport height */
      overflow-y: auto; /* Enable vertical scrolling */
      padding: 4px; /* Padding set to 4 */
  }
</style>
<div id="gis-context-menu" class="analytics h-auto overflow-y-auto z-40 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm  dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
  <div class="p-3 space-y-2">
      <p> Caricamento config ....</p>
      <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
  </div>
</div>
<div id="gis-container" style ="height: 85vh" >

  <div  class="absolute scrollable-div  p-4 top-10 left-10 z-10 inline-block text-sm font-light text-gray-500
  bg-white dark:bg-gray-800 shadow-sm  dark:text-gray-400 opacity-50 bg-opacity-5 bg-[rgba(107,114,128,0.5)]">
  <ul id="side-panel" aria-labelledby="markerSearchButton">
  </ul>
  </div>
</div>
<div class="absolute top-4 left-1/2 transform -translate-x-1/2  z-50 ">
      <label for="marker-search" class="sr-only">Search</label>
      <div class="relative">
        <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
          <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
          </svg>
        </div>   
        <input data-dropdown-toggle="markerSearch" data-dropdown-placement="bottom" type="text" id="marker-search"   class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Trova impianto">
    </div>
      <!-- marker menu -->
      <div id="markerSearch" class="absolute hidden bg-white rounded-lg shadow w-72 dark:bg-gray-700">

        <ul id="marker-group-ul" class="h-96  px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="markerSearchButton">
        </ul>
    </div>
</div> 

<script type="text/javascript">
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    resetFilters.classList.add('hidden');
    ackButton.classList.add('hidden');
    var map,deckOverlay;
    var searchResults = document.getElementById('marker-group-ul');
    var searchMarker = document.getElementById('marker-search');
    var ctxMenu = document.getElementById("gis-context-menu");                                        

async function fetchAndParseJSON(url) {
    try {
        // Fetch the JSON file
        const response = await fetch(url);

        // Check if the response is OK (status code 200-299)
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Parse the JSON file
        const data = await response.json();

        // Log the parsed data
        return(data)
    } catch (error) {
        // Handle any errors
        console.error('Error fetching or parsing JSON:', error);
    }
}

fetchAndParseJSON("/webscada/gisdata").then(data =>{
  const {MapboxOverlay, ScatterplotLayer, ArcLayer, IconLayer} = deck;
  const iconAtlas =  { "marker": {
    "x": 0,
    "y": 0,
    "width": 512,
    "height": 512,
    "anchorY": 512,
    "mask": false
  }}
  const iconAtlasW =  { "marker": {
    "x": 0,
    "y": 0,
    "width": 512,
    "height": 512,
    "anchorY": 512,
    "mask": true
  }}  
  const wasteIconLayer = new IconLayer({    
          id: 'wasteIconLayer',
          data:  data.filter(item => item.COMMODITY != "GAS" & item.COMMODITY.includes("Fogna")),
          getColor: 'yellow',//d => [Math.sqrt(d.exits), 140, 0],
          getIcon: d => 'marker',
          getPosition: d => [Number(d.LONGITUDINE),Number(d.LATITUDINE)],
          getSize: 30,
          iconAtlas: '/webscada/static/img/drop.png',
          iconMapping: iconAtlasW,
          pickable: true,
          onClick: ({ object }) => {
                  console.log(object)
                  const sidePanel = document.getElementById('side-panel');
                  const element = document.getElementById(object.IDENTIFICATIVO_OGGETTO_MAPPA);
                  if(! element){
                      const li = document.createElement('li');
                      li.setAttribute('id',object.IDENTIFICATIVO_OGGETTO_MAPPA);
                      li.innerHTML =  `
                        <div class="flex items-center justify-between mb-2">
                            <label id="settings-${object.ID_MISURA}" onclick="getPage(event,${object.LONGITUDINE},${object.LATITUDINE})" class="bg-blue-700 hover:bg-blue-800
                            focus:ring-4 focus:ring-blue-300 font-medium  text-left px-2 rounded-sm font-semibold 
                             text-white">${object.ID_PERIFERICA}</label>
                            <button class="text-white bg-red-700 hover:bg-red-800 font-medium text-sm px-3 
                            rounded-sm text-center  dark:bg-red-600 dark:hover:bg-red-700 "
                              onclick="deleteElement('${object.IDENTIFICATIVO_OGGETTO_MAPPA}','${object.ID_MISURA}')"> 
                              <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14"/>
                                </svg>
                              </button>
                        </div>    
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white mr-2">ID:</h3>
                            ${object.NAME}
                        </div>            

                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Imputazione:</h3>
                            <p>${object.COMMODITY}</p>
                        </div>    
                        <div class="flex   border-gray-300   items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">POD:</h3>
                            <p>${object.IDENTIFICATIVO_OGGETTO_MAPPA}</p>
                        </div>                            
                        <hr class="my-2 border-gray-200 dark:border-gray-700">                     
                      `;
                      htmx.process(li);
                    sidePanel.appendChild(li);
                  }
          }
        });
  
  const waterIconLayer = new IconLayer({    
          id: 'waterIconLayer',
          data:  data.filter(item => item.COMMODITY != "GAS" & !item.COMMODITY.includes("Fognature")),
          getColor: 'yellow',//d => [Math.sqrt(d.exits), 140, 0],
          getIcon: d => 'marker',
          getPosition: d => [Number(d.LONGITUDINE),Number(d.LATITUDINE)],
          getSize: 30,
          iconAtlas: '/webscada/static/img/drop.png',
          iconMapping: iconAtlas,
          pickable: true,
          onClick: ({ object }) => {
                  console.log(object)
                  const sidePanel = document.getElementById('side-panel');
                  const element = document.getElementById(object.IDENTIFICATIVO_OGGETTO_MAPPA);
                  if(! element){
                      const li = document.createElement('li');
                      li.setAttribute('id',object.IDENTIFICATIVO_OGGETTO_MAPPA);
                      li.innerHTML =  `
                        <div class="flex items-center justify-between mb-2">
                            <label id="settings-${object.ID_MISURA}" onclick="getPage(event,${object.LONGITUDINE},${object.LATITUDINE})"  class="bg-blue-700 hover:bg-blue-800
                            focus:ring-4 focus:ring-blue-300 font-medium  text-left px-2 rounded-sm font-semibold 
                             text-white">${object.ID_PERIFERICA}</label>
                            <button class="text-white bg-red-700 hover:bg-red-800 font-medium text-sm px-3 
                            rounded-sm text-center  dark:bg-red-600 dark:hover:bg-red-700 "
                              onclick="deleteElement('${object.IDENTIFICATIVO_OGGETTO_MAPPA}','${object.ID_MISURA}')"> 
                              <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14"/>
                                </svg>
                              </button>
                        </div>   

                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white mr-2">ID:</h3>
                            ${object.NAME}
                        </div>            

                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Imputazione:</h3>
                            <p>${object.COMMODITY}</p>
                        </div>    
                        <div class="flex   border-gray-300   items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">POD:</h3>
                            <p>${object.IDENTIFICATIVO_OGGETTO_MAPPA}</p>
                        </div>                             
                        <hr class="my-2 border-gray-200 dark:border-gray-700">                     
                      `;
                      htmx.process(li);
                    sidePanel.appendChild(li);
                  }
          }
        });
  
  const gasIconLayer = new IconLayer({
          id: 'gasIconLayer',
          data:  data.filter(item => item.COMMODITY === "GAS"),
          getColor: 'yellow',//d => [Math.sqrt(d.exits), 140, 0],
          getIcon: d => 'marker',
          getPosition: d => [d.LONGITUDINE,d.LATITUDINE],
          getSize: 30,
          iconAtlas: '/webscada/static/img/fire.png',
          iconMapping: iconAtlas,
          pickable: true,
          onClick: ({ object }) => {
                    console.log(object)
                  const sidePanel = document.getElementById('side-panel');
                  const element = document.getElementById(object.IDENTIFICATIVO_OGGETTO_MAPPA);
                  if(! element){
                      const li = document.createElement('li');
                      li.setAttribute('id',object.IDENTIFICATIVO_OGGETTO_MAPPA);
                      li.innerHTML =  `
                        <div class="flex items-center justify-between">
                            <label id="settings-${object.ID_MISURA}" onclick="getPage(event,${object.LONGITUDINE},${object.LATITUDINE})"
                             class="bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium  text-left px-3 rounded-sm font-semibold 
                             text-white">${object.ID_MISURA}</label>
                            <button class="text-white bg-red-700 hover:bg-red-800 font-medium text-sm px-3 
                            rounded-sm text-center  dark:bg-red-600 dark:hover:bg-red-700 "
                              onclick="deleteElement('${object.IDENTIFICATIVO_OGGETTO_MAPPA}','${object.TAG}')"> 
                              <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14"/>
                                </svg>
                              </button>
                        </div>   

                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white mr-2">ID :</h3>
                            <p>${object.ID_PERIFERICA}</p>
                        </div>            

                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white mr-2">Commodity:</h3>
                            <p>${object.COMMODITY}</p>
                        </div>    
                        <div class="flex   border-gray-300   items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white mr-2">Id mappa:</h3>
                            <p>${object.IDENTIFICATIVO_OGGETTO_MAPPA}</p>
                        </div>                           
                        <hr class="my-2 border-gray-200 dark:border-gray-700">                     
                      `;
                      htmx.process(li);
                    sidePanel.appendChild(li);
                  }
        }
        });

  map = new maplibregl.Map({
    container: 'gis-container',
    style: 'https://basemaps.cartocdn.com/gl/positron-gl-style/style.json',
    center: [11.12108, 46.06787],
    zoom: 12,
    pitch: 60,
    antialias: true
  });

  map.on('style.load', () => {
    const firstLabelLayerId = map.getStyle().layers.find(layer => layer.type === 'symbol').id;

    map.removeLayer('building')
    map.removeLayer('building-top')
    map.addLayer({
      'id': '3d-buildings',
      'source': 'carto',
      'source-layer': 'building',
      'type': 'fill-extrusion',
      'minzoom': 15,
      'paint': {
          'fill-extrusion-color': '#aaa',

          // use an 'interpolate' expression to add a smooth transition effect to the
          // buildings as the user zooms in
          'fill-extrusion-height': [
              "interpolate", ["linear"], ["zoom"],
              15, 0,
              15.05, ["get", "render_height"]
          ],
          'fill-extrusion-base': [
              "interpolate", ["linear"], ["zoom"],
              15, 0,
              15.05, ["get", "render_min_height"]
          ],
          'fill-extrusion-opacity': .9
      }
    }, firstLabelLayerId);

    deckOverlay = new MapboxOverlay({
      interleaved: true,
      getTooltip: ({object}) => object && `${object.NAME}
      ${object.IDENTIFICATIVO_OGGETTO_MAPPA}`,
      layers: [gasIconLayer,
              waterIconLayer,wasteIconLayer,
              new ScatterplotLayer({
                id: 'deckgl-circle',
                data: [
                  {position: [11.12108, 46.06787], color: [0, 0, 0], radius: 100000}
                ],
                getPosition: d => d.position,
                getFillColor: d => d.color,
                getRadius: d => d.radius,
                opacity: 0.3,
                beforeId: firstLabelLayerId
              }), 
       /* new ArcLayer({
          id: 'deckgl-arc',
          data: [
            {source: [-122.3998664, 37.7883697], target: [-122.400068, 37.7900503]}
          ],
          getSourcePosition: d => d.source,
          getTargetPosition: d => d.target,
          getSourceColor: [255, 208, 0],
          getTargetColor: [0, 128, 255],
          getWidth: 8
        })*/
      ]
    });

    map.addControl(deckOverlay);
    map.addControl(new maplibregl.NavigationControl());
    map.addControl(new maplibregl.ScaleControl());

    function searchList(list){
      searchResults.innerHTML = ""

      list.forEach(item => {
            const li = document.createElement('li');
            const coordinates = [item.LONGITUDINE,item.LATITUDINE]
            li.innerHTML =  `
                <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                <label  onclick="flyTo(${coordinates})" class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">${item['NAME']}</label>
                </div>`;
            searchResults.appendChild(li);
        });
    }

    searchMarker.addEventListener('input', function(event) {
        const queryString = event.target.value.toLowerCase();
        let gas =  gasIconLayer.props.data.filter(function(item) { if (item['NAME']){return item['NAME'].toLowerCase().includes(queryString)}});
        let water =  waterIconLayer.props.data.filter(function(item) { if (item['NAME']){ return item['NAME'].toLowerCase().includes(queryString)}});                                                  
        //let water =  waterIconLayer.props.data.filter(item => item['NAME'].toLowerCase().includes(queryString));         
        const result = gas.concat(water)
        searchList(result)
    });
    
    //first render on load
    searchList(data)
    
  });

  // Function to mask a specific marker
  function maskMarker(position) {
     let lat = Math.round(position.lat * 1e8) / 1e8
     let lng = Math.round(position.lng * 1e8) / 1e8
     console.log(lng,lat,position.lng,position.lat)

      const updatedData = data.map(d => {
          if (d.LONGITUDINE == lng && d.LATITUDINE == lat) {
              console.log('result',d)
              return { ...d, color: [128, 128, 128, 128] }; // Change color to a masked color
          }
          return d;
      });

      /*deckOverlay.setProps({
          layers: [
            new IconLayer({
              ...gasIconLayer.props,
              data: updatedData
            })
          ]
        });*/
  }
  map.on('moveend', function () {
      const center = map.getCenter();
      console.log('centerxxx',center)
      maskMarker(center);
      
    });
});

/*
document.addEventListener("contextmenu",(e) => {
  if (e.srcElement.id.includes('::') ){       
      e.preventDefault();
      vals = JSON.parse(e.srcElement.getAttribute('hx-vals'))      
      htmx.ajax('GET', `/webscada/config/${e.srcElement.id}/?chartsIndex=-1`, {target:'#gis-context-menu', swap:'innerHTML'} ).then(() => {
              ctxMenu.style.top = `${e.pageY}px`;
              ctxMenu.style.left =  `${e.pageX}px`;
              ctxMenu.classList.remove('hidden');
              });
  }                              
});*/

document.addEventListener("click",(e) => {
  if (!ctxMenu.contains(e.target)) {
    ctxMenu.classList.add('hidden');
  }

  })   

function getPage(e,lon,lat){
  htmx.ajax('GET', `/webscada/gisanalytics/${lat},${lon}`, {target:'#gis-context-menu', swap:'innerHTML'} ).then(() => {
              ctxMenu.style.top = `${e.pageY}px`;
              ctxMenu.style.left =  `${e.pageX}px`;
              ctxMenu.classList.remove('hidden');
              });
}
function deleteElement(id,tag) {
  const element = document.getElementById(id);
  if (element) {
      element.remove();
  }
}
function flyTo(long,lat){
  console.log('start',long,lat)
    marker = document.getElementById("markerSearch")
    marker.classList.toggle('hidden')
    map.flyTo({center: [long,lat],
          zoom: 15,
          essential: true
      });
}
</script>
