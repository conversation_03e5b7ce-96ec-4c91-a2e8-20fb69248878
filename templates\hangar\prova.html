<html>
    <script src="static/js/graphics.js"></script>

<head>
    <style>
    canvas {
        border:1px solid #d3d3d3;
    } 
           
    </style>
    <title>Page Title</title>
</head>

<body>
    <!-- <canvas id="canvas" width="1866" height="960" > </canvas> -->
    <svg class="pt-16 size-full" viewBox="0 0 1866 964" id="ISETSynPanel" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" hx-ext="sse" hx-swap="none" sse-connect="/webscada/realtime/synoptics/page.12370.config" sse-swap="message">
        <foreignObject width="100%" height="100%">
            <body xmlns="http://www.w3.org/1999/xhtml">
                <canvas id="canvas" width="1866" height="964"></canvas>
            </body>
        </foreignObject>
        <circle cx="216" cy="267" fill="red" r="1" id="fill-IS150::72010004" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::72000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::72000004" x="216.0" y="379.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::72000005/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::72000005" x="170.0" y="379.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::72000006/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::72000006" x="123.0" y="379.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::72000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::72000003" x="68.0" y="329.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::80000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::80000001" x="87.0" y="898.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::83000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::83000001" x="749.0" y="527.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="676" cy="654" fill="red" r="1" id="fill-IS150::83010001" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::83000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::83000002" x="749.0" y="550.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="909" cy="558" fill="red" r="1" id="fill-IS150::84010007" color="red" state="true" blink="true"></circle><circle cx="1179" cy="85" fill="red" r="1" id="fill-IS150::86010035" color="red" state="true" blink="true"></circle><rect type="level" minlevel="-0.239999994635582" maxlevel="6.11999988555908" origin="103" max="47" id="level-IS150::86000001" x="1263" y="103" width="46" height="47" fill="lightblue"></rect>
        <rect type="level" minlevel="-0.239999994635582" maxlevel="6.11999988555908" origin="103" max="47" id="level-IS150::86000002" x="1214" y="103" width="46" height="47" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::86000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::86000003" x="1293.0" y="86.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::86000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::86000004" x="1227.0" y="173.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::86000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::86000001" x="1333.0" y="140.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::86000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::86000002" x="1191.0" y="140.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::72020001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::72020001" x="235.0" y="339.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="1762" cy="254" fill="red" r="1" id="fill-RVR0::1010035" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/RVR0::1000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::1000003" x="1657.0" y="359.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/RVR0::1000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::1000001" x="1773.0" y="360.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/RVR0::1000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::1000004" x="1773.0" y="382.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/RVR0::1000007/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::1000007" x="1657.0" y="382.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="0.00999999977648258" maxlevel="2.8" origin="231" max="47" id="level-TNP::101000015" x="1379" y="231" width="99" height="47" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000015/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000015" x="1502.0" y="268.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::78000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::78000003" x="1431.0" y="306.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::78000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::78000004" x="1336.0" y="242.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000017/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000017" x="1735.0" y="450.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-0.400000005960464" maxlevel="6" origin="487" max="46" id="level-TNP::101000010" x="1516" y="487" width="180" height="46" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000010/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000010" x="1720.0" y="523.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000014/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000014" x="1491.0" y="584.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000011/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000011" x="1667.0" y="562.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000013/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000013" x="1667.0" y="584.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000012/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000012" x="1336.0" y="438.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="digital" id="TNP::101010073" x="1356.0" y="532" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010071" x="1321.0" y="532" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <circle cx="1376" cy="518" fill="red" r="1" id="fill-TNP::101010048" color="yellow" state="true" blink="false"></circle><circle cx="1340" cy="518" fill="red" r="1" id="fill-TNP::101010045" color="yellow" state="true" blink="false"></circle><circle cx="1406" cy="522" fill="red" r="1" id="fill-TNP::101010063" color="red" state="true" blink="true"></circle><circle cx="1785" cy="885" fill="red" r="1" id="fill-RVR0::19010003" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::73000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::73000002" x="1690.0" y="898.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::73000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::73000001" x="1796.0" y="856.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="938" cy="170" fill="red" r="1" id="fill-TNP::101010381" color="yellow" state="true" blink="false"></circle><text type="digital" id="TNP::101030110" x="908.0" y="171" fill="green" content="mode" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::24000194/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::24000194" x="812.0" y="160.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="digital" id="TNP::101010402" x="832.0" y="236" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010400" x="797.0" y="236" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <circle cx="852" cy="224" fill="red" r="1" id="fill-TNP::101010378" color="yellow" state="true" blink="false"></circle><circle cx="817" cy="222" fill="red" r="1" id="fill-TNP::101010375" color="yellow" state="true" blink="false"></circle><circle cx="1034" cy="160" fill="red" r="1" id="fill-TNP::101010393" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::24000007/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::24000007" x="1157.0" y="222.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::24000193/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::24000193" x="868.0" y="118.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::24000008/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::24000008" x="973.0" y="298.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::24000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::24000004" x="1096.0" y="248.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-0.200000002980232" maxlevel="2.4" origin="210" max="46" id="level-TNP::101000108" x="925" y="210" width="99" height="46" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000108/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000108" x="1048.0" y="246.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="1442" cy="753" fill="red" r="1" id="fill-TNP::101010036" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/TNP::101000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000003" x="1474.0" y="687.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000004" x="1475.0" y="829.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000002" x="1475.0" y="866.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::69000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::69000002" x="668.0" y="549.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="602" cy="565" fill="red" r="1" id="fill-TNP::101010327" color="red" state="true" blink="true"></circle><rect type="level" minlevel="-0.239999994635582" maxlevel="2.4" origin="482" max="47" id="level-TNP::101000090" x="636" y="482" width="46" height="47" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000090/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000090" x="613.0" y="519.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="digital" id="TNP::101010368" x="1028.0" y="464" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010366" x="993.0" y="464" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010364" x="958.0" y="464" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <circle cx="1048" cy="452" fill="red" r="1" id="fill-TNP::101010346" color="yellow" state="true" blink="false"></circle><circle cx="1014" cy="452" fill="red" r="1" id="fill-TNP::101010343" color="yellow" state="true" blink="false"></circle><circle cx="977" cy="452" fill="red" r="1" id="fill-TNP::101010340" color="yellow" state="true" blink="false"></circle><text type="analog" content="value" hx-get="/webscada/config/TNP::101000102/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000102" x="1097.0" y="531.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::22000005/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::22000005" x="930.0" y="420.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-0.200000002980232" maxlevel="3" origin="435" max="47" id="level-TNP::101000100" x="1069" y="435" width="99" height="47" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000100/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000100" x="1192.0" y="472.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="1185" cy="520" fill="red" r="1" id="fill-TNP::101010357" color="red" state="true" blink="true"></circle><text type="digital" id="TNP::101010443" x="1255.0" y="752" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010441" x="1203.0" y="752" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010447" x="1076.0" y="750" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010445" x="1024.0" y="750" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <circle cx="1274" cy="739" fill="red" r="1" id="fill-TNP::101010420" color="yellow" state="true" blink="false"></circle><circle cx="1223" cy="739" fill="red" r="1" id="fill-TNP::101010417" color="yellow" state="true" blink="false"></circle><circle cx="1095" cy="738" fill="red" r="1" id="fill-TNP::101010414" color="yellow" state="true" blink="false"></circle><circle cx="1043" cy="739" fill="red" r="1" id="fill-TNP::101010411" color="yellow" state="true" blink="false"></circle><circle cx="1168" cy="723" fill="red" r="1" id="fill-TNP::101010432" color="red" state="true" blink="true"></circle><rect type="level" minlevel="-0.239999994635582" maxlevel="6.11999988555908" origin="572" max="46" id="level-IS150::84000001" x="815" y="572" width="99" height="46" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::84000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::84000001" x="938.0" y="608.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::21000006/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::21000006" x="1244.0" y="677.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::21000007/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::21000007" x="1225.0" y="866.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::21000008/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::21000008" x="1114.0" y="867.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::21000005/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::21000005" x="1066.0" y="677.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000001" x="534.0" y="911.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-0.239999994635582" maxlevel="3.8" origin="874" max="46" id="level-IS150::79000001" x="557" y="874" width="99" height="46" fill="lightblue"></rect>
        <circle cx="419" cy="906" fill="red" r="1" id="fill-IS150::79010088" color="yellow" state="true" blink="false"></circle><circle cx="338" cy="906" fill="red" r="1" id="fill-IS150::79010085" color="yellow" state="true" blink="false"></circle><circle cx="254" cy="906" fill="red" r="1" id="fill-IS150::79010072" color="yellow" state="true" blink="false"></circle><circle cx="174" cy="906" fill="red" r="1" id="fill-IS150::79010069" color="yellow" state="true" blink="false"></circle><text type="analog" content="value" hx-get="/webscada/config/TNP::101000179/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000179" x="743.0" y="790.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="782" cy="770" fill="red" r="1" id="fill-TNP::101010557" color="yellow" state="true" blink="false"></circle><circle cx="797" cy="889" fill="red" r="1" id="fill-IS150::79010011" color="yellow" state="true" blink="false"></circle><circle cx="760" cy="889" fill="red" r="1" id="fill-IS150::79010008" color="yellow" state="true" blink="false"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::79000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000004" x="687.0" y="899.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="613" cy="837" fill="red" r="1" id="fill-IS150::79010045" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::79000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000002" x="775.0" y="824.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000003" x="775.0" y="843.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::80000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::80000002" x="89.0" y="854.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000046/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000046" x="415.0" y="866.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000045/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000045" x="334.0" y="866.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000030/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000030" x="251.0" y="866.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000029/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000029" x="169.0" y="866.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000048/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000048" x="415.0" y="846.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000047/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000047" x="333.0" y="846.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000032/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000032" x="252.0" y="847.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::79000031/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::79000031" x="169.0" y="846.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000149/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000149" x="163.0" y="697.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000150/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000150" x="163.0" y="677.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="90" cy="626" fill="red" r="1" id="fill-TNP::101010483" color="yellow" state="true" blink="false"></circle><circle cx="55" cy="624" fill="red" r="1" id="fill-TNP::101010480" color="yellow" state="true" blink="false"></circle><text type="digital" id="TNP::101010500" x="70.0" y="638" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010498" x="36.0" y="638" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <circle cx="176" cy="593" fill="red" r="1" id="fill-TNP::101010486" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::71000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::71000004" x="68.0" y="561.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000147/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000147" x="68.0" y="582.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-0.280000001192093" maxlevel="7" origin="609" max="48" id="level-TNP::101000145" x="124" y="609" width="100" height="48" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000145/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000145" x="247.0" y="646.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-4" maxlevel="2.4" origin="305" max="45" id="level-IS150::72020001" x="114" y="305" width="99" height="45" fill="lightblue"></rect>
        <circle cx="488" cy="329" fill="red" r="1" id="fill-TNP::101010467" color="red" state="true" blink="true"></circle><circle cx="603" cy="374" fill="red" r="1" id="fill-TNP::101010457" color="yellow" state="true" blink="false"></circle><circle cx="569" cy="373" fill="red" r="1" id="fill-TNP::101010454" color="yellow" state="true" blink="false"></circle><text type="digital" id="TNP::101010474" x="584.0" y="387" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="TNP::101010472" x="550.0" y="388" fill="green" content="value" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <rect type="level" minlevel="-0.239999994635582" maxlevel="3.7" origin="360" max="46" id="level-TNP::101000134" x="386" y="360" width="144" height="46" fill="lightblue"></rect>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000134/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000134" x="363.0" y="396.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::23000008/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::23000008" x="505.0" y="471.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::23000007/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::23000007" x="405.0" y="450.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::23000005/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::23000005" x="582.0" y="313.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101000137/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101000137" x="404.0" y="312.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <rect type="level" minlevel="-0.200000002980232" maxlevel="1.85" origin="85" max="46" id="level-RVR0::54000001" x="545" y="85" width="99" height="46" fill="lightblue"></rect>
        <circle cx="509" cy="101" fill="red" r="1" id="fill-RVR0::54010010" color="yellow" state="true" blink="false"></circle><circle cx="473" cy="101" fill="red" r="1" id="fill-RVR0::54010007" color="yellow" state="true" blink="false"></circle><text type="digital" id="RVR0::54030002" x="490.0" y="113" fill="green" content="mode" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="digital" id="RVR0::54030001" x="455.0" y="113" fill="green" content="mode" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <text type="analog" content="value" hx-get="/webscada/config/RVR0::54000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::54000001" x="668.0" y="121.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="657" cy="170" fill="red" r="1" id="fill-RVR0::54010013" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::70000003/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::70000003" x="738.0" y="27.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::70000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::70000002" x="488.0" y="47.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::70000004/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::70000004" x="560.0" y="161.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::75000010/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::75000010" x="318.0" y="13.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="units" hx-get="/webscada/config/RVR0::18000011/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::18000011" x="970.0" y="78.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="units" hx-get="/webscada/config/RVR0::18000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::18000001" x="1020.0" y="79.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <circle cx="47" cy="185" fill="red" r="1" id="fill-RVR0::18010006" color="yellow" state="true" blink="false"></circle><text type="digital" id="RVR0::18030001" x="43.0" y="201" fill="green" content="mode" alarmtxt="M" normaltxt="A" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif"></text>
        <rect type="level" minlevel="0.300000011920929" maxlevel="3" origin="29" max="46" id="level-RVR0::18000001" x="140" y="29" width="46" height="46" fill="lightblue"></rect>
        <circle cx="253" cy="56" fill="red" r="1" id="fill-RVR0::18010008" color="red" state="true" blink="true"></circle><text type="analog" content="value" hx-get="/webscada/config/IS150::76000002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::76000002" x="44.0" y="138.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/IS150::75000011/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="IS150::75000011" x="160.0" y="107.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/RVR0::18000001/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="RVR0::18000001" x="210.0" y="65.5" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        <text type="analog" content="value" hx-get="/webscada/config/TNP::101020002/?chartsIndex=-1" hx-target="#context-menu" hx-swap="innerHtml" hx-trigger="contextmenu" id="TNP::101020002" x="1510.0" y="780.0" fill="lime" font-weight="bold" font-size="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
        
        <script>
        ""
            var svgimg = document.getElementsByTagName('image')
            var img = new Image();
            img.src = "img/A_Gen_Rovereto_12370.png";
            
            const canvas = document.getElementById("canvas");
            const ctx = canvas.getContext("2d", { willReadFrequently: true });
           
            var fillers = document.getElementsByTagName('circle')
            var coordinates = []
            for (let i in fillers){
                if (fillers[i].cx){
                    coordinates.push([fillers[i].cx.baseVal.value, fillers[i].cy.baseVal.value])
                }
            }

            console.log(coordinates)
            img.onload = function() {
                canvas.width = this.width;
                canvas.height =  this.height;         
                ctx.drawImage(img, 0, 0);
                img.style.display = "none";
                const svgElement = document.getElementById('ISETSynPanel');
                const viewBox = '0 0 '+this.width +' '+this.height;
                svgElement.setAttribute('viewBox', viewBox);  
                console.log(this.width, this.height);
                flicker();           
                };

                // Get the image data of the canvas
                var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                var data = imageData.data;
                const bounding = canvas.getBoundingClientRect();
                //const x = event.clientX - bounding.left;
                //const y = event.clientY - bounding.top;
                //const coordinates = [[209,260],[669,647],[1172,78],[814,722],[1275,382],[1362,707],[839,28]];
                for( let e in coordinates){
                    let data = coordinates[e]
                    try{
                        floodFill(ctx, data[0], data[1], [170,255,0,1]);
                    }catch{}
                }               
                var tag = false;
                function flicker() {
                    
                        for( let e in coordinates){
                            try{
                                let data = coordinates[e]
                                console.log('xxxx',data)
                                if (!tag){
                                    floodFill(ctx, data[0], data[1], [125,249,255]);
                                }else{
                                    floodFill(ctx, data[0], data[1], [255,0,0,1]);
                                }console.log('yyyy',data)
                             }catch(e){}
                        }
                    
                    tag = !tag;  
                }
                setInterval(flicker, 200);
                
                                
                htmx.on('htmx:sseMessage', (msg)=> {
                    const result = JSON.parse(msg.detail.data);   
                    console.log(result)         
                    for (var tag in result) {
                        const element = document.getElementById(tag);
                        const levelElement = document.getElementById('level-'+tag);
                        if (element){
                                if(result[tag].color && element.getAttribute('type') == 'analog'){
                                    element.style.fill = result[tag].color;
                                    element.innerHTML = result[tag].value;
                                }
                                if(element.getAttribute('type') == 'digital'){
                                    element.style.fill = result[tag].color;                                                          
                                    
                                    let value = result[tag].value;
                                    if (element.getAttribute('content') == 'mode'){
                                                console.log('xxxx',tag,result[tag].stato)
                                                value = result[tag].stato;
                                            }
                                    if(value == 0){
                                                element.innerHTML = element.getAttribute('normaltxt');
                                                }
                                    else if(value == 1){
                                                element.innerHTML = element.getAttribute('alarmtxt');
                                                }                                                                    
                            } 

                            }
                        if(levelElement){                                                
                            let leveltag = tag.replace('level-','');
                            let maxLevel= Math.abs(levelElement.getAttribute('maxlevel'))
                            let minLevel= Math.abs(levelElement.getAttribute('minlevel'))
                            let max = levelElement.getAttribute('max')
                            let origin = levelElement.getAttribute('origin')
                            let level = ((result[leveltag].value + minLevel)/(maxLevel+minLevel)) *  max ;
                            levelElement.setAttribute('height',level);
                            levelElement.setAttribute('y',parseFloat(origin) + (max-level));

                        }                                                                                              
                    }
            })
            document.getElementById('ISETSynPanel').addEventListener("contextmenu",(e) => {
                    e.preventDefault();
                    let left = e.pageX;
                    let top = e.pageY;
                    let ctxMenu = document.getElementById("context-menu");
                    if (e.srcElement.id.includes('::')){                                                
                        ctxMenu.style.top = `${e.pageY}px`;
                        ctxMenu.style.left =  `${e.pageX}px`;
                        ctxMenu.classList.remove('hidden');
                    }
                });

            document.getElementById('ISETSynPanel').addEventListener("click",(e) => {
                let ctxMenu = document.getElementById("context-menu");
                ctxMenu.classList.add('hidden');
            });

            htmx.on('htmx:sseError', (e)=> {
                console.log(e)
                //window.location.href = "/webscada"
            })                    
        </script>
    </svg>        
</body>
