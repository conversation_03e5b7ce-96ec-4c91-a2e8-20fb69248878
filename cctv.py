import asyncio
import nats.errors
import nats.js.errors
from fastapi import APIRouter
import json 
router = APIRouter()

async def watchers(appstate):
    print(f'watcher for cctv started')
    async def get_watcher(retry = 10):
        try:
            watcher = await appstate.cctvkv.watch('stream.>')
            return watcher
        except nats.errors.TimeoutError as e:
            if retry > 0:
                await asyncio.sleep(1)
                print('retrying cctv watcher in 1 sec...')
                return await get_watcher(retry - 1)
            else:
                raise e
    watcher = await get_watcher()
    while True:   
        try:
            async for result in watcher:
                if result.operation == 'DEL':
                    if result.key in appstate.cctv:
                        del appstate.cctv[result.key]            
                else:
                    try:
                        appstate.cctv[result.key] = json.loads(result.value.decode()) 
                    except:
                        appstate.cctv[result.key] = result.value.decode() 
        except nats.errors.TimeoutError as e:
            pass
        except Exception as e:
            print('watcher error',e)    
        """
        print(f"'appstate.cctv' len: {len(appstate.cctv)}")  
        print(f"'appstate.iset' len: {len(appstate.iset)}")  
        print(f"'appstate.isetconfig' len: {len(appstate.isetconfig)}")  
        print(f"'appstate.glogcfg' len: {len(appstate.glogcfg)}")  
        print(f"'appstate.users' len: {len(appstate.users)}")  
        print(f"'appstate.frames' len: {len(appstate.frames)}")     
        """
        await asyncio.sleep(0)
                   
async def get_plants(appstate):
    cctv_plants = dict()    
    for key,item in appstate.cctv.items():
        if 'stream.anagrafica' in key:
            plant_id = item['idImpianto']
            plant_name = item['descrizione']
            size =  item['size']
            if plant_name not in cctv_plants:
                cctv_plants[plant_name] = list()
            item_id = item['id']
            name = item['nome']
            cctv_plants[plant_name].append({'id':str(item_id),'plant_id':plant_id,'plant_name':plant_name,'name':name,'size':size})

    return cctv_plants
        
