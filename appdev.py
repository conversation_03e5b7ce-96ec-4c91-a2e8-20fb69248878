import tomllib
import gc
import json
import asyncio
from fastapi import HTTPException, FastAPI, Request, Form, Response, Depends, File, UploadFile,WebSocket
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse, HTMLResponse, JSONResponse, StreamingResponse, FileResponse
from fasthx import Jinja
import nats.errors
import uvicorn
import uuid
from datetime import datetime, timedelta
import duckdb
from iset.isetpages import read_analytics, read_synoptics, get_tree
from iset.isetwidgets import *
from iset.isetusers import get_users
from iset.iset import get_config
from messaging import component
from security import AuthHandler, RequiresLoginException
from api import *
import cctv
import nats.errors
import nats.js.errors
import fsspec
import fsspec.implementations.memory
from logger import logger
import os
from pympler import summary, muppy, asizeof
import psutil
process = psutil.Process()
def app_config(config_path = "config.toml"):    
    try:
        with open(config_path, "rb") as f:
            config = tomllib.load(f)
            isetengines = get_config(config['iset']['isetini'])
            config['isetengines'] = isetengines
            return config
    except FileNotFoundError:
        print(f"Config file not found: {config_path}")
    except tomllib.TOMLDecodeError as e:
        print(f"Invalid TOML format in {config_path}: {e}")

#Class Appstate
class Appstate():
    pass
#app state object
appstate = Appstate()
#app config
appstate.appconfig = app_config()
#Nats Object
appstate.nc = component(appstate.appconfig['nats'])
#inmemory fs  
appstate.fs = fsspec.filesystem('memory') 

# Create the app instance.
app = FastAPI(root_path='/webscada')
auth_handler = AuthHandler(appstate)
app.mount("/static", StaticFiles(directory="templates/static"), name="static")
app.mount("/img", StaticFiles(directory=f"{appstate.appconfig['iset']['isetgui']}Images"), name="img")
app.mount("/resources", StaticFiles(directory=r"iset\resources"), name="resources")

basedir = os.path.abspath(os.path.dirname(__file__))
users_path = appstate.appconfig['iset']['isetusers']

# Create a FastAPI Jinja2Templates instance. This will be used in FastHX Jinja instance.
templates = Jinja2Templates(directory=os.path.join(basedir, "templates"))
# FastHX Jinja instance is initialized with the Jinja2Templates instance.
jinja = Jinja(templates)

async def iset_last():
    print('isete last called......')
    last_dict = dict()
    while True:
        try:
            for key,items in appstate.iset.items():         
                if ("iset.config" in key and "info" not in key):
                    key_index = key.split('.')
                    engine = key_index[2]
                    if key_index[3].isdigit():
                        logical_number = int(key_index[3])
                    else:
                        continue
                    last_dict[f'{engine}{logical_number}'] = (engine,logical_number)
                    #print(engine,logical_number)
            
            for key,value in last_dict.items():
                try:
                    engine = value[0]
                    logical_number = value[1]
                    payload = {'tags':[{'engine':engine,'logical_number':logical_number}], 'request':'server', 'type': 'last','user_id':-1,'guid':str(uuid.uuid4()), 'timeout': 5000}                           
                    
                    raw_response = await appstate.nc.req('iset.producer.realtime',json.dumps(payload).encode())
                    tags = json.loads(raw_response.data.decode())
                    #iterate over values
                    for item in tags['values']:
                        key = f"iset.last.{tag_to_topic(item['tag'])}"
                        appstate.iset[key] = {'state':item['state'],'value':float(item['value'].replace(',','.')),'ts':item['ts']} 
                        if engine == 'TNR0' and logical_number == 23:
                            print(item['tag'],appstate.iset[key] )
                        #print({'state':item['state'],'value':item['value'],'ts':item['ts']} )
                except Exception as e:
                    print(e)
                    pass   
            #await asyncio.sleep(0) 
        except Exception as e:
            print(e)
        await asyncio.sleep(2)   

async def read_glog_events():
    while True:
        try:
            path = f'alarms/glog/events.parquet'
            rawalarms = await appstate.nc.get('glog.events')          
            with appstate.fs.open(path, 'wb') as file:
                file.write(rawalarms.value)
        except:
            pass 
        await asyncio.sleep(5)

async def watchers(kv):
    print(f'watcher {kv} started')
    watcher = await appstate.nc.watchkv(kv)
    result = await watcher.updates(timeout=2)
    while result is not None:
        try:
            appstate.iset[result.key] = json.loads(result.value.decode())
        except nats.errors.TimeoutError as e:
            pass 
        except:
            appstate.iset[result.key] = result.value 
        result = await watcher.updates(timeout=2) 

    while True:   
        try:
            result = await watcher.updates(timeout=2)
            if result is not None:
                try:
                    appstate.iset[result.key] = json.loads(result.value.decode()) 
                except:
                    appstate.iset[result.key] = result.value.decode() 
        except nats.errors.TimeoutError as e:
            pass
        except Exception as e:
            print('watcher error',kv,e)        
        await asyncio.sleep(0)

async def debug():
     while True:
        all_objects = muppy.get_objects()
        sum1 = summary.summarize(all_objects)
        summary.print_(sum1) 
        await asyncio.sleep(30)
        print()
        """
        await asyncio.sleep(30)

        mem = process.memory_info().rss / 1024 ** 2  
        logger.info(f"appstate.fs size {asizeof.asizeof(appstate.fs) / 1024 ** 2}")
        logger.info(f"memory size {mem} MB")
        """
async def background_loop():
    # Your code to maintain application state goes here
    async def connect():
        try:
            print('connectiong to nats...')
            await appstate.nc.connect(bucket = 'iset') 
            
            appstate.alarmskv = await appstate.nc.key_value(bucket = 'alarms')
            appstate.scadakv = await appstate.nc.key_value(bucket = 'scada')
            appstate.helpdeskv = await appstate.nc.key_value(bucket = 'helpdesk')
            appstate.scadaobj = await appstate.nc.object_store(bucket = 'scada')
            appstate.cctvobj = await appstate.nc.object_store(bucket = 'CCTV-GAS')
            appstate.cctvkv = await appstate.nc.key_value(bucket = 'CCTV-GAS')
            
            #appstate.lastwatcher = await appstate.nc.watchkv('iset.>')
            #appstate.userwatcher = await appstate.nc.watchkv('iset.user.>')
            appstate.iset = dict()
            appstate.cctv = dict()
            appstate.isetconfig = dict()
            appstate.glogcfg = dict()
            appstate.users = dict()
            appstate.frames = dict()            
        except:
            await asyncio.sleep(0.1)
            print("reconnecting....")
            await connect()
    await connect()

    #prefetch all neccessary values fro the webapp first
    async def prefetch(topic):
        print(datetime.now(),'start',topic)
        appstate.lastwatcher = await appstate.nc.watchkv(topic)
        lastresult = await appstate.lastwatcher.updates(timeout=2)
        while  lastresult is not None: 
            try:
                appstate.iset[lastresult.key] = json.loads(lastresult.value.decode()) 
            except:
                appstate.iset[lastresult.key] = lastresult.value 
            lastresult = await appstate.lastwatcher.updates(timeout=2)
        print(datetime.now(),'end',topic)

    topics = ['iset.user.>','iset.config.>']#,'iset.last.>'
    for topic in topics:
        await prefetch(topic)
        asyncio.create_task(watchers(topic))
    print(datetime.now(),'prefetch done')    
    appstate.lastwatcher = await appstate.nc.watchkv('iset.last.>')
    #asyncio.create_task(iset_last())
    asyncio.create_task(cctv.watchers(appstate))
    asyncio.create_task(read_glog_events())
    asyncio.create_task(debug())   

    while True:
        # hydrate state store     
        try:
            lastresult = await appstate.lastwatcher.updates(timeout=2)
            if lastresult is not None:
                if len(lastresult.value) > 0:
                    #appstate.iset[lastresult.key] = lastresult.value    
                    if 'iset.last.crossreference' in lastresult.key:
                        with appstate.fs.open(f'iset.last.crossreference.parquet', 'wb') as file:
                            file.write(lastresult.value)   

                    elif 'iset.last.' in lastresult.key:                   
                        with duckdb.connect() as conn:
                            # Create a memory filesystem and write the parquet data to it           
                            with appstate.fs.open(f'iset.last.parquet', 'wb') as file:
                                file.write(lastresult.value)

                            # Register the memory filesystem and create the table
                            conn.register_filesystem(appstate.fs)
                            #conn.query(f"SELECT * FROM read_parquet('memory://iset.last.parquet')").show() 
                            query =f"""SELECT * FROM read_parquet('memory://iset.last.parquet')"""
                            try:
                                queryresult = conn.query(query).fetchall()
                                #print(len(queryresult))
                                for e in queryresult:
                                    key = e[0]

                                    if key.replace('last','config') in appstate.iset:
                                        state = e[2]
                                        value = e[3]
                                        ts = e[1]

                                        if 'DIN' in key or 'DOUT' in key:
                                            value = int(e[3])
                                        appstate.iset[key] = {'state':state,'value':value,'ts':ts} 
                                        appstate.iset[key] = {'state':state,'value':value,'ts':ts} 

                                    else:
                                        continue                       
                            except Exception as e:
                                print(lastresult.key,e,lastresult.value)
                                pass
                                                        
        except nats.errors.TimeoutError as e:
            pass
        except Exception as e:
            print('loop error',e)
        
        """ GLOG ALARMS K,V PAIR"""
        for key,items in appstate.iset.items():
                       
            if ("GLOG" in key and "info" not in key):
                try:
                    rtu = items['Plant'].split(' ')[0]
                    tag = items['Nome'].split(' ')[0].split('.')[1].upper()
                    address = items['Tag']

                    if 'PSPV' in tag:
                        tag = 'PSPV'
                    elif 'PSPM' in tag or 'MED' in tag:
                        tag = 'PSPM'
                    elif 'PSPI' in tag:
                        tag = 'PSPI'

                    appstate.glogcfg[address] = {'rtu': rtu,'tag':tag}

                except Exception as e:
                    pass    
        await asyncio.sleep(0)                                       

@app.middleware("http")
async def create_auth_header(
    request: Request,
    call_next,):
    '''
    Check if there are cookies set for authorization. If so, construct the
    Authorization header and modify the request (unless the header already
    exists!)
    '''
    process = psutil.Process()
    mem_before = process.memory_info().rss / 1024 ** 2  
    #logger.info(f"Received request: {request.method} {request.url}  {request.query_params} ")
    if ("Authorization" not in request.headers 
        and "Authorization" in request.cookies
        ):
        access_token = request.cookies["Authorization"]
        
        request.headers.__dict__["_list"].append(
            (
                "authorization".encode(),
                 f"Bearer {access_token}".encode(),
            )
        )
    elif ("Authorization" not in request.headers 
        and "Authorization" not in request.cookies
        ): 
        access_token = 'empty'
        request.headers.__dict__["_list"].append(
            (
                "authorization".encode(),
                 f"Bearer {access_token}".encode(),
            )
        )
        
    mem_after = process.memory_info().rss / 1024 ** 2
    logger.info(f"Memory before: {mem_before}MB, after: {mem_after}MB")
    response = await call_next(request)
    return response  

#redirection block
@app.exception_handler(RequiresLoginException)
async def exception_handler(request: Request, exc: RequiresLoginException):
    ''' this handler allows me to route the login exception to the login page.'''
    if request.headers.get("Accept") == "text/event-stream":
        return StreamingResponse(f"data: {'reload':True}\n\n", 
                                     media_type="text/event-stream",
                                     headers={"HX-Refresh": "true"})
    if request.headers.get("Hx-Request") == "true":
        return Response("Sessione scaduta, reindirizzamento automatico...", headers={"HX-Redirect": "/webscada/login"})
    else:
        return RedirectResponse(url = "/webscada/login",headers={"HX-Redirect": "/webscada/login"}) 

@app.exception_handler(404)
async def exception_404_handler(request: Request, exc: RequiresLoginException) -> Response: 
    ''' this handler allows me to route the login exception to the login page.'''
    return RedirectResponse(url='/webscada/404')  

#deprecated
"""
@app.get("/data/{item_id}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def pagesdata(item_id : str):
        
    response = await read_synoptics(path = {item_id:""},ui_path= appstate.appconfig['iset']['isetgui'])
    result = {}
    for tag in response['tags']:        
        if len(tag)>0:
            try:
                data = await appstate.nc.get(f'opc.{tag}')
                result[tag] = json.loads(data.value.decode())
            except Exception as e:
                print(e)
    return result
"""
@app.get("/config/{tag}/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("config.html")
async def tagconfig(tag : str, chartsIndex : int):
    data = await get_tag_config(tag, appstate)
    cross = await get_crossref(appstate,tag)
    return {'data':data, 'charts':chartsIndex, 'cross':cross}

@app.get("/multiconfig/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("multiconfig.html")
async def multiconfig(tags : str):
    items = list()
    for tag in tags.split(','):
      items.append(await get_tag_config(tag, appstate))
    #print(items)
    return {'items':items,'charts':-1}

@app.get("/multistate/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("multistate.html")
async def multistate(tags : str):
    #tag_to_topic
    items = list()
    for tag in tags.split(','):
      items.append(await get_tag_config(tag, appstate))
    print(items)
    return {'items':items}
    
@app.get("/allarmi",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("allarmi.html")
async def allarmi() -> None:
    sidemenu = get_tree(ui_path= appstate.appconfig['iset']['isetgui'])[1]
    #result = await get_alarms(appstate)
    #return {'pagetype': 'synoptics','items':result, 'sidemenu':sidemenu}  
    return {'sidemenu':sidemenu}  

@app.get("/allarme/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("allarme.html")
async def allarme(tag : str):
    response = {}
    try:
        response = await get_local_alarms(appstate, tag)
    except Exception as e:
        print(e)
    return {'items' : response}

@app.get("/alarmsdata", response_class=JSONResponse, dependencies=[Depends(auth_handler.auth_wrapper)])
async def alarmsdata() -> None:
    result = await get_alarms(appstate)

@app.get("/rtalarms")
async def rt_alarms(auth_data = Depends(auth_handler.auth_wrapper)) -> StreamingResponse:
    email = auth_data['email'] 
    grant  = auth_handler.get_acl(email)['grant'] 
    response = StreamingResponse(realtime_alarms(appstate, grant), media_type="text/event-stream")
    response.headers["Cache-Control"] = "no-cache"
    response.headers["X-Accel-Buffering"] = "no"
    return response  

@app.get("/histalarms")
async def hist_alarms(request: Request, start : str, end:str, offset:int, limit:int, filter: str, auth_data = Depends(auth_handler.auth_wrapper)):
    query = dict()
    query['start'] = start
    query['end'] = end
    query['offset'] = offset
    query['limit'] = limit
    query['filter'] = json.loads(filter)
    email = auth_data['email'] 
    grant  = auth_handler.get_acl(email)['grant'] 
    response = await alarms_archive(appstate, query, grant)
    return response 

@app.get("/ackalarms")
async def ackalarms(request: Request, query : str, auth_data = Depends(auth_handler.auth_wrapper)):
    data = json.loads(query)
    email = auth_data['email'] 
    acl = auth_handler.get_acl(email)
    grant = acl['grant']
    response = await alarms_ack(appstate, data, acl)
    return response

@app.get("/page/{item_id}", response_class=HTMLResponse, dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("page.html")
async def page( item_id : str, auth_data = Depends(auth_handler.auth_request_wrapper)):

    logger.info(f"{auth_data['email']} {item_id}")
    email = auth_data['email']    
    acl  = auth_handler.get_acl(email)['grant']

    """This route can only serve HTML, because the no_data parameter is set to True."""
    if item_id == 'allarmi':
        if not acl['alarms']:
            raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this item.")    
        result = await get_alarms(appstate)
        response = {'allarmi':True, 'items':result, 'chartIndex' : 1}
        return response
    if item_id == 'archive':
        if not acl['archive']:
            raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this item.")    
        response = {'archive':True, 'chartIndex' : 1}
        return response   
    
    #if the request is related to idronetwork pages
    if not acl['webscada']:
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this item.")     
    try:
        page = await read_synoptics(path = {item_id:""}, ui_path= appstate.appconfig['iset']['isetgui'])
        response = {'svg':True,'data':page['svg'], 'chartIndex' : 1}
    except:
        page = await read_analytics(path = {item_id:""}, config_dict = appstate.iset, ui_path= appstate.appconfig['iset']['isetgui'])
        tagslist = list(page['tags'].keys())
        tagslist.sort()
        config = dict()

        for tag in tagslist:
            #tag permission check
            try:
                tag_group = appstate.iset[f"iset.config.{tag_to_topic(tag)}"].get('Gruppo',0)
                user_alarm_group = acl['alarmgroup']
                if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                    alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])
                    if not (tag_group & alarm_grant):
                        continue  
            except Exception as e:
                logger.error(e)
                continue
           
            key = f"iset.config.{tag_to_topic(tag)}"
            if key in appstate.iset:
                config[tag] = appstate.iset[key]
        #print(config.keys())
        response = {'pageid':item_id,'data':page, 'config': config, 'chartIndex' : 1}

    return response

@app.get("/hist/",dependencies=[Depends(auth_handler.auth_wrapper)])
async def histdata(request: Request,q : str):
    query = json.loads(q)
    taglist = query['tags']
    start_date = datetime.strptime(query['start_date'],'%Y-%m-%d %H:%M:%S')
    end_date = datetime.strptime(query['end_date'],'%Y-%m-%d %H:%M:%S')
    
    response = await get_hist([taglist], start_date, end_date, appstate)
    return response


@app.get("/chart/{time}/{taglist}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def chartdata(request: Request, time : str, taglist : str):
    print(time,taglist)
    #1,7,30,180,360
    start_date = datetime.now() - timedelta( days = int(time)) 
    end_date = datetime.now() 
    response = await get_hist([taglist], start_date, end_date, appstate)
    return response

@app.get("/globalchart/{time}/{taglist}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def chartdata(request: Request, time : str, taglist : str):
    #1,7,30,180,360
    start_date = datetime.now() - timedelta( days = int(time)) 
    end_date = datetime.now() 
    response = await get_hist([taglist], start_date, end_date, appstate)
    return response

@app.get("/newchart/{time}/{taglist}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def newchartdata(request: Request, time : str, taglist : str):
    #1,7,30,180,360
    start_date = datetime.now() - timedelta( days = int(time)) 
    end_date = datetime.now() 
    response = await get_hist([taglist], start_date, end_date, appstate)
    print(response)
    return response

@app.get("/addchart/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("chart.html")
async def newchart(chartIndex : int):
    """This route can only serve HTML, because the no_data parameter is set to True."""
    return {"chartIndex":chartIndex+1}

@app.get("/delete/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("chart.html")
async def deletechart(chartIndex : int):
    return {"delete": True,"chartIndex":chartIndex-1}

@app.get("/isetmap",dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("isetmap.html")
async def isetmap():
    response = {'chartIndex' : 1}
    return response

@app.get("/isetvalues",dependencies=[Depends(auth_handler.auth_wrapper)])
async def isetvalues():
    result = await get_map(appstate)
    return result

@app.get("/anagrafica",dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("anagrafica.html")
async def anagrafica():
    response = {'chartIndex' : 1}
    return response

@app.get("/anagraficavalues",dependencies=[Depends(auth_handler.auth_wrapper)])
async def anagraficavalues():
    result = await get_anagrafica(appstate)
    return result

@app.get("/anagraficamodify/{q}")
async def anagraficamodify(q: str,auth_data = Depends(auth_handler.auth_wrapper)):
    newdata = json.loads(q)
    #newdata['modified_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
     
    newdata['user'] = auth_data['email']
    anagrafica = appstate.iset.get('iset.config.anagrafica.info',{})
    if isinstance(anagrafica, bytes):
        anagrafica = {}
    key = newdata['pagina'] 
    anagrafica[key] = newdata
    await appstate.nc.put('iset.config.anagrafica.info',json.dumps(anagrafica).encode())

@app.get("/scadabi/{index}")
@jinja.page("scadabi.html")
async def scadabi(index = str, auth_data = Depends(auth_handler.auth_wrapper)):
    email = '<EMAIL>'#auth_data['email'] ##
    grant  = auth_handler.get_acl(email)['grant']     
    if index == 'page':
        plants = await scadabi_devices(appstate, grant)
        return {'plants':plants , "chartIndex":1}
    else:
        tags = await scadabi_devices(appstate, grant,index)
        return {'tags':tags}

@app.get("/gispage",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("scadagis.html")
async def gispage():
    pass

@app.get("/bivalues/{time}/{taglist}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def bivalues(request: Request, time : str, taglist : str):
    print(time,taglist)
    #1,7,30,180,360
    start_date = datetime.now() - timedelta( days = int(time)) 
    end_date = datetime.now() 
    parquet_buffer = await get_hist([taglist], start_date, end_date, appstate,True)
    response = Response(content=parquet_buffer, media_type="application/octet-stream")
    response.headers["Content-Disposition"] = f"attachment; filename=result.parquet"
    return response#FileResponse(parquet_buffer, filename="result.parquet")

@app.get("/gisanalytics/{coordinates}")
@jinja.page("gisanalytics.html")
async def gis_analytics(coordinates : str, auth_data = Depends(auth_handler.auth_request_wrapper)):
    email = auth_data['email']
    acl  = auth_handler.get_acl(email)['grant']   
 
    item_id = await gis_plant(coordinates) #page name in iset
    page = await read_analytics(path = {item_id:""}, config_dict = appstate.iset, ui_path= appstate.appconfig['iset']['isetgui'])
    print(page)
    tagslist = list(page['tags'].keys())
    tagslist.sort()
    config = dict()

    for tag in tagslist:
        #tag permission check
        try:
            tag_group = appstate.iset[f"iset.config.{tag_to_topic(tag)}"].get('Gruppo',0)
            user_alarm_group = acl['alarmgroup']
            if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])
                if not (tag_group & alarm_grant):
                    continue  
        except Exception as e:
            logger.error(e)
            continue
        
        key = f"iset.config.{tag_to_topic(tag)}"
        if key in appstate.iset:
            config[tag] = appstate.iset[key]
    #print(config.keys())
    response = {'pageid':item_id,'data':page, 'config': config, 'chartIndex' : 1}
    return response

@app.get("/gisdata",dependencies=[Depends(auth_handler.auth_wrapper)])
async def gistdata():
    result = await gis_map(appstate)
    return result
'''
@app.websocket("/ws")
async def gisrealtime(websocket: WebSocket):
    auth_data = await auth_handler.ws_auth_wrapper(websocket)
    await gis_realtime(appstate,websocket)
'''
@app.get("/radiomap",dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("radiomap.html")
async def radiomap():
    return {}

@app.get("/radiokey/{key}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def radiokey(key :str):
    radiolist = await appstate.scadakv.get(key)
    return HTMLResponse(content=radiolist.value, status_code=200) 

@app.get("/reports",dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("reports.html")
async def reports():
    return {}

@app.get("/reportvalues",dependencies=[Depends(auth_handler.auth_wrapper)])
async def isetvalues():
    result = await get_reports(appstate)
    return result

@app.get("/sendreport/{id}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def api_config(id: str):
    request = {'id':int(id)}
    print(request)
    try:
        response = await appstate.nc.req('scada.mmm.sendEmail', json.dumps(request).encode(),timeout = 60 * 2)
        #await asyncio.sleep(5)
    except:
        pass
    html = f'<label class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 rounded-lg text-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Elaborato </label>'
    return HTMLResponse(content=html, status_code=200) 
    
@app.get("/dbsync",dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("dbsync.html")
async def dbsync():
    rtus = dict()
    for key,item in appstate.iset.items():
        if 'config.GLOG' in key:
            try:
                rtu = item['Nome'].split('.')[0].upper()
                if rtu.startswith('PB') or rtu.startswith('GB'):
                    rtus[rtu] = {}      
            except:
                pass  
    try:
        msg = await appstate.nc.req('scada.jobs.dbsync', json.dumps({'status':"dbsync"}).encode())
        response = msg.data.decode().split('\n')
        print(response)

        if response[0] == 'empty':
            return {'items': rtus.keys()}
        else:
            return {'items': rtus.keys(),'response' : response}
    except:
        return {'items': rtus.keys()}

@app.get("/dbsync/run/",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("log.html")
async def dbsyncrun(query: str | None = None):
    try:
        q = json.loads(query)
        #{"rtus":["GB221","GB516"],"value":"some","override":true,"start":"2024/06/06","end":"2024/06/28"}
        start_date = datetime.strptime(q['start'],"%Y/%m/%d")
        end_date = datetime.strptime(q['end'],"%Y/%m/%d")
        
        #date validation
        print(start_date, end_date)
        if start_date > end_date:
            return {'error':'La data di inizio deve precedere quella di fine recupero'}

        rtus = list()
        if "rtus" in q:
            rtus = q['rtus']

        request = {'start_date': q['start'], 'end_date' :q['end'], 'rtus':rtus if type(rtus) is list else [], 
                'allgb':  q['value'] == "gb" ,'allpb': q['value'] == "pb", 'all': q['value'] == "all" ,
                'overwrite': q['override']}
        msg = await appstate.nc.req('scada.jobs.dbsync',json.dumps(request).encode())

        response = msg.data.decode().split('\n')
        
    except Exception as e:
        print(e)
        return {'error':e}
    
    print('response ',response)
    return {'response':response}
    
@app.get("/", dependencies=[Depends(auth_handler.auth_request_wrapper)])
async def index(request:Request, auth_data = Depends(auth_handler.auth_request_wrapper) ):
    """This route serves the syn.html template."""
    #pages = await get_pages(appstate)
    try:
        raw = await appstate.nc.get('iset.user.info')
        appstate.users = json.loads(raw.value.decode()) 
        appstate.users = get_users(users_path)    
    except:            
        appstate.users = get_users(users_path)
        await appstate.nc.put('iset.user.info',json.dumps(appstate.users).encode())    
    

    #match the webuser access control with iset idronetrowk
    email = auth_data['email']    
    acl  = auth_handler.get_acl(email)
    user = acl['grant']['user']

    rawpages, sidemenu, alarmgroup = get_tree(user = user, ui_path= appstate.appconfig['iset']['isetgui'])
    pages = list()
    appstate.iset['alarmgroup'] = alarmgroup

    for k,v  in rawpages.items():
        try:
            pages.append(Page(name=v, id=k) )#{'name':v, 'id':k}
        except:
            continue

    radiolist = await  appstate.scadakv.keys()
    radiomap = [e for e in radiolist if 'charts.' in e]

    data =  {'pagetype':'synoptics','items':pages,'htmxtarget':'hx-target=#panel','sidemenu':sidemenu, 
             'radiomap': radiomap,'user':email.split('@')[0],'email':email,
             'grant':acl['grant'],'lasturl':auth_data['lasturl']} 
    #return data
    return templates.TemplateResponse(name="index.html", context=data, request=request)
  
@app.get("/404")
@jinja.page("404.html")
async def fourofour() -> None:
    """This route serves the 404.html template."""

@app.get("/login", response_class=HTMLResponse)
#@jinja.page("login.html")
async def login(request: Request):
    print(request)
    #sprint(auth_data)
    
    """This route serves the login.html template."""
    appstate.users = get_users(users_path)
    await appstate.nc.put('iset.user.info', json.dumps(appstate.users).encode())
    return templates.TemplateResponse(name="login.html" ,request=request)

@app.post("/otp")
async def sendotp(request: Request,email: str = Form(...)):
    """This route serves the otp.html template."""
    status = 0
    if await auth_handler.validate_email(email):
        status = 1
        await auth_handler.sendotp(email)
    response = templates.TemplateResponse(name="otp.html" ,request=request, context={'status': status})
    response.set_cookie(key="email", value=f"{email}", expires=300)    
    return response

@app.get("/generateotp")
@jinja.page("toast.html")
async def getotp(query : str,  auth_data = Depends(auth_handler.auth_wrapper)):
    if auth_data['grant']['admin']:
        response = dict()
        response['info'] = await auth_handler.getotp()
        return response
    else:
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this resource.")

@app.post("/inpersona/{email}")
async def inpersona_login(request: Request, response: Response, email: str, auth_data = Depends(auth_handler.auth_wrapper)):
    """This route serves the otp.html template."""
    if auth_data['grant']['admin']:
        password = await auth_handler.getotp()
        user = {'email':email,'password':password} #User(email = email, password = password)  
        if await auth_handler.authenticate_user(user['email'], user['password']):
            atoken = auth_handler.create_access_token(user['email'])
            creds = auth_handler.get_acl(email)
            response = templates.TemplateResponse("index.html", 
              {"request": request, 'data':None,'email': creds['email'], 'grant':creds['grant']})#,"USERNAME": user['email']
            
            #response = RedirectResponse(url="/",  status_code=303)
            response.set_cookie(key="Authorization", value= f"{atoken}", httponly=True)
            response.set_cookie(key="SessionId", value= f"{uuid.uuid4()}", httponly=True)    
            response.headers["HX-Redirect"] = "/webscada/"
            return response
        else:
            raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this resource.") 
    else:
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this resource.")        
    
@app.post("/login")
async def sign_in(request: Request, response: Response,
                  code1: str = Form(...), code2: str = Form(...), code3: str = Form(...), code4: str = Form(...),code5: str = Form(...), code6: str = Form(...)):

    email = request.cookies['email'] 
    otp = f"{code1}{code2}{code3}{code4}{code5}{code6}"
    password = otp
    try:
        user = {'email':email,'password':password} #User(email = email, password = password)  
        if await auth_handler.authenticate_user(user['email'], user['password']):
            creds = auth_handler.get_acl(email)
            response = templates.TemplateResponse("index.html", 
              {"request": request, 'data':None,'email': creds['email'], 'grant':creds['grant']})#,"USERNAME": user['email']
            
            session = creds.get('session','24')
            if session.lower() == 'sessione':
                atoken = auth_handler.create_access_token(user['email'])
                response.set_cookie(key="Authorization", value= f"{atoken}", httponly=True)
                response.set_cookie(key="SessionId", value= f"{uuid.uuid4()}", httponly=True)   
            else:
                try:
                    hours = int(session)
                except:
                    hours = 24
                atoken = auth_handler.create_access_token(user['email'], expires_delta = timedelta(hours = hours))
                
                response.set_cookie(key="Authorization", value= f"{atoken}", expires= hours * 60 * 60, httponly=True)
                response.set_cookie(key="SessionId", value= f"{uuid.uuid4()}", expires= hours * 60 * 60, httponly=True)    

            response.headers["HX-Redirect"] = "/webscada/"
            return response
        else:
            status = 1
            #templates.TemplateResponse(name="otp.html" ,request=request, context={'status': status,})
            return templates.TemplateResponse("otp.html",
            {"request": request, 'status': status,'detail': 'Token errato oppure scaduto!', 'status_code': 404 })
    
    except Exception as err:
        logger.error(err)
        return templates.TemplateResponse("error.html",
            {"request": request, 'detail': 'Token errato oppure scaduto!', 'status_code': 401 })

@app.post("/logout")
async def sign_out(request: Request, response: Response):
        creds = dict()
        creds['grant']={}
        creds['email']=""
        response = templates.TemplateResponse("index.html", 
              {"request": request, 'data':None,'email': creds['email'], 'grant':creds['grant']})        
        response.delete_cookie("Authorization")
        response.delete_cookie("SessionId")    
        response.headers["HX-Redirect"] = "/webscada/"
        return response

@app.get("/users", dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("usergrid.html")
async def users():
    """This route serves the login.html template."""
    return

@app.get("/usersdata")
async def usersdata(auth_data = Depends(auth_handler.auth_wrapper)):
    """This route serves the login.html template."""
    try:
        appstate.users = get_users(users_path)
        await appstate.nc.put('iset.user.info', json.dumps(appstate.users).encode())
    except:
        pass
    #the code above is a workaround
    email = auth_data['email']
    try:
        raw = await appstate.nc.get('iset.user.info')
        appstate.users = json.loads(raw.value.decode())     
    except:            
        appstate.users = get_users(users_path)
        await appstate.nc.put('iset.user.info',json.dumps(appstate.users).encode())
    result = await get_webusers(appstate)
    return result

@app.get("/usermodify", dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("toast.html")
async def usermodify(query : str):
    response = dict()
    try:
        users = json.loads(query)
        #check and validate email
        email_regex = r"([A-Za-z0-9]+[.-_])*[A-Za-z0-9]+@[A-Za-z0-9-]+(\.[A-Z|a-z]{2,})+"
        email_validator = re.match(email_regex, users['email'])

        if email_validator and len(users['email']) > 0:
            key = f"iset.user.{users['email'].lower().replace('@','.')}"
            try:
                raw_msg = await appstate.nc.get(key)
                user_data = json.loads(raw_msg.value.decode())
                if user_data['id'] == users['id']:
                    await appstate.nc.put(key,json.dumps(users).encode())
                    response['info'] = f'Utente {users["email"]} modificato.'
                else:
                    response['error'] = "Email già utilizzato!!!"
                
            except nats.js.errors.KeyNotFoundError:
                await appstate.nc.put(key,json.dumps(users).encode())
                response['info'] = 'salvato'
        else:
           response['error'] = 'Email mal formato oppure vuoto' 
    except:
        response['error'] = 'Errore aggiornamento utente'

    return response 
    
@app.get("/save/{item}/{id}",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("edit.html")
async def save(item : str, id : int, email:str):
    if item == 'save':
        # check if the email is assigned to an existant       
        for key,value in appstate.iset.items():
            if f'iset.user.' in key and 'iset.user.info' not in key:
                if email == value['email']:
                    email = ''
        await appstate.nc.put(f'iset.user.{id}', json.dumps({'email':email}).encode())
        appstate.iset[f'iset.user.{id}'] =  {'email':email}

    if item == 'cancel':
        try:
            raw = await appstate.nc.get(f'iset.user.{id}', email)
            user = json.loads(raw.value.decode()) 
            email = user['email']
            print('cancel',email)
        except:
            pass

    if item == 'webscada':
        try:
            raw = await appstate.nc.get(f'iset.user.{id}', email)
            user = json.loads(raw.value.decode()) 
            user['webscada'] = True
            email = user['email']
        except:
            return {'item':'error', 'error': "email utente non impostata"}

    return {'item':item, 'id':id, 'email':email}

@app.get("/crossref", dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("crossref.html")
async def crossref():
    """This route serves the login.html template."""
    response = {'chartIndex' : 1}
    return response

@app.get("/crossrefdata")
async def crossrefdata(auth_data = Depends(auth_handler.auth_wrapper)):
    result = await get_crossref(appstate)
    return result

#main event loop
@app.on_event("startup")
async def main():        
    asyncio.create_task(background_loop())   
    
@app.get("/realtime/{page}/{page_id}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def realtime_data(request: Request, page : str, page_id : str) -> StreamingResponse:
    response = StreamingResponse(get_tags(request, page, page_id, appstate), media_type="text/event-stream")
    response.headers["Cache-Control"] = "no-cache"
    response.headers["X-Accel-Buffering"] = "no"
    return response   

@app.get("/write/")
async def write(tag : str, value , mode, auth_data = Depends(auth_handler.auth_wrapper)):
    email = auth_data['email']    
    acl  = auth_handler.get_acl(email)
        
    if isinstance(value,str):
         if value == 'true':
             flat_value = 1
         elif value == 'false':  
             flat_value = 0
         else:
             flat_value = float(value) 

         if mode == 'true':
             mode = 1
         if mode == 'false':  
             mode = 0
    else:    
        try:
            flat_value = int(value)
        except ValueError:
            flat_value = float(value)            
    
    await write_tag(appstate,tag,flat_value,mode,acl)
    return None   

@app.get("/updaterequest/{tag}")
async def update(tag : str, auth_data = Depends(auth_handler.auth_wrapper)):
    email = auth_data['email']    
    acl = auth_handler.get_acl(email)
    tags = [tag]
    response = await request_update(appstate, tags, acl)
    return response   

@app.get("/helpdesk")
@jinja.page("helpdesk.html")
async def helpdesk(auth_data = Depends(auth_handler.auth_request_wrapper)) -> None:
    email = auth_data['email']    
    acl  = auth_handler.get_acl(email)['grant']
    if not acl['helpdesk']:
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this item.")

@app.get("/helpform/{action}")
@jinja.page("helpform.html")
async def helpform(action: str,auth_data = Depends(auth_handler.auth_wrapper)) -> None:
    email = auth_data['email']    
    acl  = auth_handler.get_acl(email)['grant']
    if not acl['helpdesk']:
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this item.")
    return {'action':action}

@app.get("/helpdata")
async def helpdata( auth_data = Depends(auth_handler.auth_wrapper) ):
    email = auth_data['email']    
    acl  = auth_handler.get_acl(email)['grant']
    if not acl['helpdesk']:
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this item.")
    response = await  get_tickets(appstate, email)
    return response

@app.post("/addticket")
async def postticket( department: str = Form(...), object: str = Form(...), priority: str = Form(...), title: str = Form(...), cc: str = Form(""),message: str = Form(...), files: list[UploadFile] = File(None), auth_data = Depends(auth_handler.auth_wrapper)):
    data= dict()
    data['categoria'] = department
    data['oggetto'] = object
    data['priorita'] = priority
    data['richiesta'] = message
    data['titolo'] = title
    data['stato'] = 'Creato'
    data['invisione'] = cc.split(',')
    data['email'] = auth_data['email']                                                
    data["utente"] = auth_data['email']             
    data["creato"] = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")      
    data["aggiornato"] = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")                             

    #fileupload to nats obj store
    try:
        file_paths = list()
        for file in files:
            # Save the file to your desired location
            file_name = file.filename.lower().replace(' ','')
            key = f"helpdesk.attachments.{file_name}"
            await appstate.scadaobj.put(key,file.file.read())
            file_paths.append(key)

        if(len(file_paths) > 0):
            data['attachments'] = file_paths
    except:
        pass

    result = await add_tickets(appstate,data)  
    html = """
                <button hx-get="/webscada/helpform/add" hx-swap="outerHTML" class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Nuovo Ticket</button>
           """
    response = HTMLResponse(content= html, status_code=200) 
    
    #response = templates.TemplateResponse("helpform.html", {'request': request,'action' :'cancel'})  
    response.headers["HX-Trigger"] = "reload-event"
    return response

@app.get("/updateticket", dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("toast.html")
async def updateticket(query : str,auth_data = Depends(auth_handler.auth_wrapper)):
    print('update', query)
    data= json.loads(query)
    data["aggiornato"] = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")     
    response = await update_ticket(appstate, data, auth_data)  
    return response

@app.get("/helpdashboard",dependencies=[Depends(auth_handler.auth_request_wrapper)])
@jinja.page("helpdashboard.html")
async def helpdashboard() -> None:
    """This route serves the helpdesk.html template."""

@app.get("/helpdashboarddata",dependencies=[Depends(auth_handler.auth_wrapper)])
async def helpdashboarddata( auth_data = Depends(auth_handler.auth_wrapper) ):
    if auth_data['grant']['admin']:
        result = await get_tickets_dashboard(appstate, auth_data)
        return result
    else:
        return None
    
@app.get("/helpnote")
@jinja.page("helpnote.html")
async def helpnote(query : str, auth_data = Depends(auth_handler.auth_wrapper)) -> None:
    data = json.loads(query)
    data['email'] = auth_data['email']
    data['grant'] = auth_data['grant']
    result = await get_notes(appstate, data)
    return {'data':data,'info':result[0],'result':result[1]}
       
@app.post("/sendnote/{id}/{user}")
@jinja.page("helpnote.html")
async def sendnote(id: str, user: str, message: str = Form(...), files: list[UploadFile] = File(None), auth_data = Depends(auth_handler.auth_wrapper)):
    print(message, id ,user)
    data = dict()
    data['message'] = message
    data['id'] = id
    data['email'] = user
    data['from'] = auth_data['email'] 
    data['user'] = user   
    file_paths = list()
    
    #fileupload to nats obj store
    try:
        for file in files:
            # Save the file to your desired location
            file_name = file.filename.lower().replace(' ','')
            key = f"helpdesk.attachments.{id}.{file_name}"
            await appstate.scadaobj.put(key,file.file.read())
            file_paths.append(key)

        if(len(file_paths) > 0):
            data['attachments'] = file_paths
    except:
        pass

    await send_note(appstate, data)
    result = await get_notes(appstate, data)
    return {'data':data,'info':result[0],'result':result[1]}  

@app.get("/download/{filename}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def download(filename : str):
    filepath = f"downloads/{filename}"
    if not os.path.exists(filepath):
        with open(filepath,'wb') as f:
            await appstate.scadaobj.get(filename,f)

    name = filename.replace("helpdesk.attachments.","")
    return FileResponse(path= filepath, filename= name)

@app.get("/cctv/{req}",dependencies=[Depends(auth_handler.auth_wrapper)])
@jinja.page("video.html")
async def cctvpage(req : str):
    if req == 'page':
        plants = await cctv.get_plants(appstate)
        return {'data':plants}

    if req.isdigit() :
         return {'feed':req}

@app.get("/feed/{feed_id}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def feed(feed_id : str):
    return StreamingResponse(get_video(appstate,feed_id), media_type="multipart/x-mixed-replace; boundary=frame")

@app.get("/cctvthumbnail/{image_id}",dependencies=[Depends(auth_handler.auth_wrapper)])
async def get_image(image_id : str):
    try:
        image_binary = await appstate.cctvobj.get(f'CCTV-GAS.{image_id}.thumbnail.jpeg')
    except FileNotFoundError:
        return Response(content="Image not found", status_code=404)
    return Response(content=image_binary.data, media_type="image/jpeg")


#RESFUllvalue ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
@app.get("/api/v1/config/{item_id}")
async def api_config(item_id: str, q: str | None = None):
    if q:
        return {"item_id": item_id, "q": q}
    return {"item_id": item_id}

@app.get("/api/v1/last/{item_id}")
async def api_last(item_id: str, q: str | None = None):
    if q:
        return {"item_id": item_id, "q": q}
    return {"item_id": item_id}

@app.get("/api/v1/hist/{item_id}")
async def api_hist(item_id: str, q: str | None = None):
    if q:
        return {"item_id": item_id, "q": q}
    return {"item_id": item_id}
if __name__ == "__main__":    
    #asyncio.create_task(background_loop())  
    uvicorn.run("appdev:app", host="0.0.0.0", port = 8001, reload=False,reload_excludes=['*.log'], workers=1)