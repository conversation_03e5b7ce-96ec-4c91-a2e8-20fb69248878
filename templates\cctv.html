
<div id="global-cctv-modal" data-modal-target="global-cctv-modal" tabindex="-1" aria-hidden="true" 
class="hidden fixed inset-0  overflow-y-auto overflow-x-hidden flex items-center justify-center z-50">
    <!-- Modal content -->
    <div class="relative  p-4  bg-white dark:bg-gray-800 rounded-lg shadow ">
        
        <!-- Modal header -->
        <div class="flex items-center justify-between rounded-t dark:bg-gray-800  overflow-y-auto overflow-x-hidden ">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white ">Video stream</h3>
            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="global-cctv-modal">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <img id="cctv-feed" class="h-auto max-w-full focus:border focus:border-red-600 rounded-lg hover-zoom" >
    </div>
</div> 