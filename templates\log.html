{% if  error %}
    <div class="dark:bg-gray-700 dark:border-gray-600 shadow-md rounded-lg w-full max-w-lg justify-start">
        <h2 class="text-xl dark:text-gray-200 text-gray-700 font-bold mb-4" >Finestra di log</h2>
        <div class="overflow-y-auto bg-gray-200 p-2 rounded">
            <p class="text-sm text-gray-200 dark:text-gray-700 text-left"  style="color: red;">[ERROR] {{error}}</p>
        </div>
    </div>
{% else %}   

<div class="dark:bg-gray-700 dark:border-gray-600 shadow-md rounded-lg w-full max-w-lg ">
    <h2 class="text-xl dark:text-gray-200 text-gray-700 font-bold mb-4 text-center">Finestra di log</h2>
    <div class="overflow-y-auto bg-gray-200 p-2 rounded">
        {% for line in response %} 
        <p class="text-sm text-gray-200 dark:text-gray-700 text-left">{{line}}</p>
        {% endfor %}
    </div>
</div>
{% endif %}