{% if action == 'add' %}

<form hx-post=/webscada/addticket hx-swap="outerHTML"  hx-indicator="#spinner" id='help-form' enctype="multipart/form-data" class="max-w-md p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
<div class="flex pl-3 px-3 py-2"> 
<span id="reparto-label" class="flex-1 items-center pl-3 px-3 py-2  text-sm font-medium text-gray-900 dark:text-gray-400 ">Unità :</span>
<select name="department" required class="flex rounded-lg items-center w-40 pl-3 px-3 py-2 justify-start text-sm font-medium text-gray-900  bg-white border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
    <option value="">Scegli Unità</option>
    <option value="GAS">GAS</option>
    <option value="Acqua">Acqua</option>
    <option value="Catodica">Catodica</option>
    <option value="Cogenerazione">Cogenerazione</option>
    <option value="Scada">Scada</option>
    <option value="CTI">CTI</option>
    <option value="SIR">SIR</option>
    <option value="HDE">HDE</option>
    <option value="SET">SET</option>
    <option value="GDE">GDE</option>
    <option value="ICT">ICT</option>
    <option value="ECO">Impianti ecologici</option>
    <option value="AIR">AIR</option>
</select >
</div>

<!-- oggetto menu -->  
<div class="flex pl-3 px-3 py-2"> 
    <span id="reparto-label" class="flex-1 items-center pl-3 px-3 py-2  text-sm font-medium text-gray-900 dark:text-gray-400 ">Oggetto :</span>
    <select name="object" required  class="flex rounded-lg items-center w-40 pl-3 px-3 py-2 justify-start text-sm font-medium text-gray-900  bg-white border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
        <option value="">Scegli oggetto</option>
        <option value="Nuovo utente">Nuovo utente</option>
        <option value="Nuova periferica">Nuova periferica</option>
        <option value="Variazione periferica">Variazione periferica</option>
        <option value="Collaudo">Collaudo</option>
        <option value="Taratura">Taratura</option>
        <option value="Setpoint & Soglie">Setpoint & Soglie</option>
        <option value="Allarmi">Allarmi</option>
        <option value="Richiesta dati">Richiesta dati</option>
        <option value="IdroNetwork">IdroNetwork</option>
        <option value="Altro">Altro</option>
    </select >
</div>
<!-- priorità menu --> 
<div class="flex pl-3 px-3 py-2"> 
    <span id="reparto-label" class="flex-1 items-center pl-3 px-3 py-2  text-sm font-medium text-gray-900 dark:text-gray-400 ">Priorità :</span>
    <select name="priority" required class="flex rounded-lg items-center w-40 pl-3 px-3 py-2 justify-start text-sm font-medium text-gray-900  bg-white border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
        <option value="">Scegli priorità</option>
        <option value="Critica">Critica</option>
        <option value="Alta">Alta</option>
        <option value="Media">Media</option>
        <option value="Bassa">Bassa</option>
    </select >
</div>



<div class="flex pl-3 px-3 py-2"> 

    <div  class="flex ps-0 space-x-1 rtl:space-x-reverse sm:ps-2">
        <label 
        for="ticket-file-upload"
        class="flex-row justify-center text-sm p-2 text-gray-500 rounded cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-600">
            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 20">
                <path stroke="currentColor" stroke-linejoin="round" stroke-width="2" d="M1 6v8a5 5 0 1 0 10 0V4.5a3.5 3.5 0 1 0-7 0V13a2 2 0 0 0 4 0V6"/>
            </svg>
            <input id="ticket-file-upload" name="files" type="file" hidden multiple/>
            <p id="ticket-file-names"></p>
        </label>                      
    </div>
</div>
<!-- title --> 

<input name="title" id="request-title" rows="4" required type="text"
class="block mb-2 p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
placeholder="Titolo.">
</input>
<!-- cc --> 
<input name="cc" id="request-cc" multiple
    pattern="^[a-zA-Z0-9._%+-]+@example\.com$" type="email"
    class="block mb-2 p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
    placeholder="Cc email separati da virgola.">
</input>
<!-- message -->
<textarea name="message" id="request-message" rows="4" required
class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
placeholder="La tua richiesta qui..."></textarea>
    <div class="flex mt-3 justify-between">        
        <button type="submit"  class="flex justify-center px-5 py-2.5 w-full text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:outline-none  rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 ">Invia</button>
        <span class="flex w-10"></span> 
        <button  hx-get="/webscada/helpform/cancel" hx-swap="outerHTML" hx-target="#help-form"  class="flex justify-center px-5 py-2.5 w-30 text-sm font-medium text-white inline-flex items-center bg-red-700 hover:bg-red-800 focus:outline-none  rounded-lg text-center dark:bg-red-600 dark:hover:bg-red-700 ">Annulla</button>        
    </div>
</form>

<script>
  var fileInput = document.getElementById('ticket-file-upload');
  var fileNamesElement = document.getElementById('ticket-file-names');

  fileInput.addEventListener('change', () => {
    const fileNames = Array.from(fileInput.files).map(file => file.name).join(', ');
    fileNamesElement.textContent = fileNames;
  });
</script>

<script>
    const selectElement = document.getElementById('czc');
    const options = Array.from(selectElement.options);

    selectElement.addEventListener('mousedown', function(e) {
        e.preventDefault();

        const clickedOption = e.target;
        clickedOption.selected = !clickedOption.selected;

        updateCheckboxes();
    });

    function updateCheckboxes() {
        selectElement.size = options.length;
        selectElement.multiple = true;

        options.forEach(option => {
            option.innerHTML = `
                <div class="checkbox-option">
                    <input type="checkbox" ${option.selected ? 'checked' : ''}>
                    <label>${option.text}</label>
                </div>
            `;
        });
    }

    updateCheckboxes();
</script>

{% endif %}
{% if action == 'send' %}
    <label class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">inviato</label>
{% endif %}

{% if action == 'cancel' %}
    <button hx-get="/webscada/helpform/add" hx-swap="outerHTML" class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Nuovo Ticket</button>
{% endif %}