<!DOCTYPE html>
<html>
<head>
  <title>Node Graph Editor</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <style>
    body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
    canvas { border: 2px solid #333; margin-top: 15px; }
    .controls { margin-bottom: 15px; }
    button { 
      margin: 5px; 
      padding: 8px 15px; 
      background: #4CAF50; 
      color: white; 
      border: none; 
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover { background: #45a049; }
    h1 { color: #333; }
  </style>
</head>
<body>
  <h1>Node Graph Editor</h1>
  <div class="controls">
    <button id="addSquare">Add Square</button>
    <button id="addCircle">Add Circle</button>
    <button id="addTriangle">Add Triangle</button>
    <button id="clear">Clear Canvas</button>
  </div>

  <canvas id="canvas" width="800" height="600"></canvas>

  <script>
    // Initialize canvas
    const canvas = new fabric.Canvas('canvas');
    
    // Track state
    const nodes = [];
    let activeNode = null;
    let activeLine = null;
    let isConnecting = false;
    
    // Add a shape to the canvas
    function addNode(type) {
      // Random position and color
      const left = Math.random() * (canvas.width - 100) + 50;
      const top = Math.random() * (canvas.height - 100) + 50;
      const color = '#' + Math.floor(Math.random()*16777215).toString(16);
      
      // Create shape based on type
      let shape;
      switch(type) {
        case 'square':
          shape = new fabric.Rect({
            left, top,
            width: 60, height: 60,
            fill: color,
            cornerSize: 8,
            hasControls: true,
            hasBorders: true
          });
          break;
        case 'circle':
          shape = new fabric.Circle({
            left, top,
            radius: 30,
            fill: color,
            cornerSize: 8,
            hasControls: true,
            hasBorders: true
          });
          break;
        case 'triangle':
          shape = new fabric.Triangle({
            left, top,
            width: 60, height: 60,
            fill: color,
            cornerSize: 8,
            hasControls: true,
            hasBorders: true
          });
          break;
      }
      
      // Add to canvas
      canvas.add(shape);
      
      // Store node data
      const node = {
        shape: shape,
        connections: []
      };
      
      nodes.push(node);
      return node;
    }
    
    // Create a connection between nodes
    function createConnection(startNode, endNode) {
      const startPoint = startNode.shape.getCenterPoint();
      const endPoint = endNode.shape.getCenterPoint();
      
      // Create bezier curve
      const dx = endPoint.x - startPoint.x;
      const dy = endPoint.y - startPoint.y;
      
      // Control points for a nice curve
      const cp1x = startPoint.x + dx / 4;
      const cp1y = startPoint.y;
      const cp2x = endPoint.x - dx / 4;
      const cp2y = endPoint.y;
      
      // Create path with dashed line
      const path = new fabric.Path(
        `M ${startPoint.x} ${startPoint.y} ` +
        `C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${endPoint.x} ${endPoint.y}`,
        {
          fill: '',
          stroke: '#333',
          strokeWidth: 3,
          strokeDashArray: [10, 5], // Create dashed line
          selectable: false,
          evented: false
        }
      );
      
      // Add to canvas
      canvas.add(path);
      
      // Store connection data
      const connection = {
        path: path,
        start: startNode,
        end: endNode,
        dashOffset: 0 // Add property to track dash animation
      };
      
      startNode.connections.push(connection);
      endNode.connections.push(connection);
      
      // Start animating this connection
      if (!animationRunning) {
        animationRunning = true;
        animateConnections();
      }
      
      return connection;
    }
    
    // Update connection path
    function updateConnection(connection) {
      const startPoint = connection.start.shape.getCenterPoint();
      const endPoint = connection.end.shape.getCenterPoint();
      
      // Calculate control points
      const dx = endPoint.x - startPoint.x;
      const cp1x = startPoint.x + dx / 4;
      const cp1y = startPoint.y;
      const cp2x = endPoint.x - dx / 4;
      const cp2y = endPoint.y;
      
      // Update path
      connection.path.path[0][1] = startPoint.x;
      connection.path.path[0][2] = startPoint.y;
      connection.path.path[1][1] = cp1x;
      connection.path.path[1][2] = cp1y;
      connection.path.path[1][3] = cp2x;
      connection.path.path[1][4] = cp2y;
      connection.path.path[1][5] = endPoint.x;
      connection.path.path[1][6] = endPoint.y;
      
      connection.path.dirty = true;
      canvas.renderAll();
    }
    
    // Track double clicks
    let lastClickTime = 0;
    const doubleClickThreshold = 300; // ms
    
    // Event handlers
    canvas.on('mouse:down', function(options) {
      const target = options.target;
      const currentTime = new Date().getTime();
      const isDoubleClick = currentTime - lastClickTime < doubleClickThreshold;
      lastClickTime = currentTime;
      
      // If we double-clicked on a shape to start connection
      if (isDoubleClick && target && nodes.some(node => node.shape === target) && !isConnecting) {
        const node = nodes.find(node => node.shape === target);
        
        // Start connection
        isConnecting = true;
        activeNode = node;
        
        // Create temporary line
        const startPoint = node.shape.getCenterPoint();
        const pointer = canvas.getPointer(options.e);
        
        activeLine = new fabric.Path(
          `M ${startPoint.x} ${startPoint.y} C ${startPoint.x + 50} ${startPoint.y}, ${pointer.x - 50} ${pointer.y}, ${pointer.x} ${pointer.y}`,
          {
            fill: '',
            stroke: '#666',
            strokeWidth: 2,
            strokeDashArray: [5, 5],
            selectable: false
          }
        );
        
        canvas.add(activeLine);
      } 
      // If connection is active and we clicked on another shape (single click)
      else if (isConnecting && target && nodes.some(node => node.shape === target)) {
        const node = nodes.find(node => node.shape === target);
        
        // Don't connect to the same node
        if (node !== activeNode) {
          // Remove temporary line
          canvas.remove(activeLine);
          
          // Create permanent connection
          createConnection(activeNode, node);
          
          // Reset state
          isConnecting = false;
          activeNode = null;
          activeLine = null;
        }
      }
      // Cancel connection if clicked on empty space
      else if (isConnecting && !target) {
        canvas.remove(activeLine);
        isConnecting = false;
        activeNode = null;
        activeLine = null;
      }
    });
    
    canvas.on('mouse:move', function(options) {
      if (isConnecting && activeLine) {
        const pointer = canvas.getPointer(options.e);
        const startPoint = activeNode.shape.getCenterPoint();
        
        // Update temporary line
        activeLine.path[0][1] = startPoint.x;
        activeLine.path[0][2] = startPoint.y;
        activeLine.path[1][5] = pointer.x;
        activeLine.path[1][6] = pointer.y;
        
        // Update control points
        activeLine.path[1][1] = startPoint.x + (pointer.x - startPoint.x) * 0.3;
        activeLine.path[1][2] = startPoint.y;
        activeLine.path[1][3] = startPoint.x + (pointer.x - startPoint.x) * 0.7;
        activeLine.path[1][4] = pointer.y;
        
        activeLine.dirty = true;
        canvas.renderAll();
      }
    });
    
    // Update connections when objects move
    canvas.on('object:moving', function(options) {
      const target = options.target;
      
      // Find if a node is being moved
      const movedNode = nodes.find(node => node.shape === target);
      if (movedNode) {
        // Update all connections
        movedNode.connections.forEach(connection => {
          updateConnection(connection);
        });
      }
    });

    // Add object:moved event to ensure clean rendering after movement stops
    canvas.on('object:moved', function(options) {
      canvas.renderAll();
    });
    
    // Button event handlers
    document.getElementById('addSquare').addEventListener('click', () => addNode('square'));
    document.getElementById('addCircle').addEventListener('click', () => addNode('circle'));
    document.getElementById('addTriangle').addEventListener('click', () => addNode('triangle'));
    
    // Animation variables
    let animationRunning = false;

    // Animate all connections
    function animateConnections() {
      // Check if we have any connections to animate
      const hasConnections = nodes.some(node => node.connections.length > 0);
      
      if (!hasConnections) {
        animationRunning = false;
        return;
      }
      
      // Update dash offset for all connections
      nodes.forEach(node => {
        node.connections.forEach(connection => {
          // Increment dash offset (negative for source to target flow)
          connection.dashOffset -= 0.5;
          
          // Apply dash offset to path
          connection.path.strokeDashOffset = connection.dashOffset;
        });
      });
      
      // Render canvas
      canvas.renderAll();
      
      // Continue animation
      requestAnimationFrame(animateConnections);
    }

    // Update clear button to also stop animations
    document.getElementById('clear').addEventListener('click', () => {
      canvas.clear();
      nodes.length = 0;
      animationRunning = false;
    });
  </script>
</body>
</html>
