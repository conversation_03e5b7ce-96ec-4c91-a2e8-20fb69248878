import fsspec
import io
import nats

class NatsKVFileSystem(fsspec.AbstractFileSystem):
    """
    A file system interface for NATS KV stores.
    """

    protocol = "natskv"

    def __init__(self, server="nats://127.0.0.1:4222", bucket="mybucket", **kwargs):
        """
        Initializes the NatsKVFileSystem.

        Args:
            server (str): NATS server URL.
            bucket (str): NATS KV bucket name.
            **kwargs: Additional arguments.
        """
        super().__init__(**kwargs)
        self.server = server
        self.bucket_name = bucket
        self.nc = None # NATS connection

    async def _connect(self):
        """
        Connects to the NATS server and gets the KV store.
        """
        if self.nc is None:
            self.nc = await nats.connect(self.server)
        self.kv = self.nc.kv(self.bucket_name)

    async def _disconnect(self):
        """
        Disconnects from the NATS server.
        """
        if self.nc:
            await self.nc.close()
            self.nc = None

    async def ls(self, path, detail=False, **kwargs):
        """
        Lists the contents of a directory (KV keys).

        Args:
            path (str): The path (prefix) to list.
            detail (bool): If True, returns detailed information.
            **kwargs: Additional arguments.

        Returns:
            list: A list of file/directory names or detailed information.
        """
        await self._connect()
        try:
            keys = await self.kv.keys(f"{path}*")
            if detail:
                return [{"name": name, "type": "file"} for name in keys]
            return keys
        finally:
            await self._disconnect()

    async def cat_file(self, path, **kwargs):
        """
        Reads the contents of a file (KV key).

        Args:
            path (str): The path (key) to read.
            **kwargs: Additional arguments.

        Returns:
            bytes: The file contents.
        """
        await self._connect()
        try:
            entry = await self.kv.get(path)
            if entry is None:
                raise FileNotFoundError(f"No key found: {path}")
            return entry.value
        finally:
            await self._disconnect()

    async def open(self, path, mode="rb", **kwargs):
        """
        Opens a file (KV key) for reading or writing.

        Args:
            path (str): The path (key) to open.
            mode (str): The file mode ("rb" for read binary, "wb" for write binary).
            **kwargs: Additional arguments.

        Returns:
            io.BytesIO: A file-like object.
        """
        if "b" not in mode:
            raise ValueError("Only binary modes are supported.")

        await self._connect()
        try:
            if "r" in mode:
                data = await self.cat_file(path)
                return io.BytesIO(data)
            elif "w" in mode:
                return NatsKVFileWriter(self.kv, path)
            else:
                raise ValueError(f"Unsupported mode: {mode}")
        finally:
            if "r" in mode:
                await self._disconnect() # disconnect only after read.

    async def put_file(self, lpath, rpath, **kwargs):
        """
        Uploads a local file to NATS KV.

        Args:
            lpath (str): Local file path.
            rpath (str): NATS KV path (key).
            **kwargs: Additional arguments.
        """
        await self._connect()
        try:
            with open(lpath, "rb") as f:
                await self.kv.put(rpath, f.read())
        finally:
            await self._disconnect()

    async def rm_file(self, path, **kwargs):
        """
        Removes a file (KV key).

        Args:
            path (str): The path (key) to remove.
            **kwargs: Additional arguments.
        """
        await self._connect()
        try:
            await self.kv.delete(path)
        finally:
            await self._disconnect()

class NatsKVFileWriter(io.BytesIO):
    """
    A file-like object for writing to NATS KV.
    """

    def __init__(self, kv, path, **kwargs):
        super().__init__(**kwargs)
        self.kv = kv
        self.path = path

    async def close(self):
        """
        Closes the file and writes the contents to NATS KV.
        """
        await self.kv.put(self.path, self.getvalue())
        super().close()

# Register the file system
fsspec.register_implementation(NatsKVFileSystem)

import asyncio

async def main():
    # Example usage
    async with fsspec.open("natskv://my_file", "wb") as f:
        f.write(b"Hello from NATS KV!")

    async with fsspec.open("natskv://my_file", "rb") as f:
        print(f.read())

    fs = fsspec.filesystem("natskv")
    print(await fs.ls("")) # list all keys
    await fs.put_file("my_local_file.txt", "natskv://local_file")
    print(await fs.cat("natskv://local_file"))
    await fs.rm("natskv://local_file")
    print(await fs.ls(""))

if __name__ == "__main__":
    asyncio.run(main())