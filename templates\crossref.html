
<div id="context-menu" class="z-10 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
  <div class="p-3 space-y-2">
      <p> Caricamento config ....</p>
      <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
  </div>
</div>
<div id="crossRefGrid" hx-get=/webscada/crossrefdata hx-swap="none" hx-indicator="#spinner" hx-trigger="load" class="ag-theme-quartz pb-4" style="height:85vh;">
</div>  
<style>
.ag-header-cell-label {
  justify-content: center;
}
.ag-cell {
  justify-content: center;
}
.level-0 {
  text-align: center;
  font-weight: bold;
  background-color:#14ce71;
}
.level-1 {
  text-align: center;
  font-weight: bold;
  background-color: #a696ee;
}
.level-2 {
  text-align: center;
  font-weight: bold;
  background-color: #00b0f5;
}
.level-3 {
  font-weight: bold;
  background-color: #f436b5;
}
.level-4 {
  font-weight: bold;
  background-color: #91e7ce;
}
</style>
<script>
    //all pages series will be contained here 
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    ackButton.classList.add('hidden');
    
    var gridElement = document.querySelector('#crossRefGrid');
    //grid init method
    function gridInit() {
      
      document.addEventListener('dark-mode', function (e) {
        if (gridElement.classList.contains('ag-theme-quartz-dark')) {
          // If dark theme is applied, switch to light theme
          gridElement.classList.remove('ag-theme-quartz-dark');
          gridElement.classList.add('ag-theme-quartz');
        } else {
          // If light theme is applied, switch to dark theme
          gridElement.classList.remove('ag-theme-quartz');
          gridElement.classList.add('ag-theme-quartz-dark');
        }
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        gridElement.classList.remove('ag-theme-quartz');
        gridElement.classList.add('ag-theme-quartz-dark');
      } else {
        gridElement.classList.remove('ag-theme-quartz-dark');
        gridElement.classList.add('ag-theme-quartz');
      }
      var localeText = JSON.parse(localStorage.getItem('grid-it'));
      var lastCellClicked;
      var gridOptions = {
        // Row Data: The data to be displayed.
        rowData: [],
        // Column Definitions: Defines the columns to be displayed.
        columnDefs: [],
        defaultColDef: {
          flex: 1,          
        },
        localeText: localeText,
        suppressHorizontalScroll: false,
        suppressContextMenu: true,
        rowSelection: "multiple",
        
        onCellClicked: (e) => {
          /*console.log('Cell clicked:', e);
          console.log('Cell value:', e.value);
          console.log('Row index:', e.rowIndex);
          console.log('Column index:', e.column.getId());*/
          let clicked = e.rowIndex.toString() + e.value;
          let ctxMenu = document.getElementById("context-menu");                                        
            if (e.column.getId() == 'tag'){       
              if(lastCellClicked == clicked){
                  ctxMenu.classList.toggle('hidden');
                }else{
                  htmx.ajax('GET', `/webscada/config/${e.data.numero}/?chartsIndex=-1`, {target:'#context-menu', swap:'innerHTML'}).then(() => {
                            ctxMenu.style.top = `${e.event.pageY}px`;
                            ctxMenu.style.left =  `${e.event.pageX}px`;
                  });
                  ctxMenu.classList.remove('hidden');
                  lastCellClicked = clicked;
                }                                                                                                
            }else{
                ctxMenu.classList.add('hidden');
            }
      }
        /*,
        rowClassRules: {
          "level-0": params => params.api.getValue("tipo", params.node) == 'AIN',
          "level-1": params => params.api.getValue("tipo", params.node) == 'AOUT',
          "level-2": params => params.api.getValue("tipo", params.node) == 'DIN',
          "level-3": params => params.api.getValue("tipo", params.node) == 'DOUT',
          "level-4": params => params.api.getValue("tipo", params.node) == 'SP'
        }*/
      };
      

      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      }

      function presenceRenderer(params) {
          if (params.value.length == 0)
            return
          const date = new Date(params.value); // Assuming value is a date string
          const currentDate = new Date();
          currentDate.setHours(currentDate.getHours() - 1); // Minus 1 hour

          const fourHoursAgo = new Date();
          fourHoursAgo.setHours(fourHoursAgo.getHours() - 4); // Minus 4 hours

          let iconColor;
          if (date >= currentDate) {
            iconColor = 'green';
          } else if (date >= fourHoursAgo) {
            iconColor = 'yellow';
          } else {
            iconColor = 'gray';
          }

          const svgString = `
            <svg width="16" height="16" viewBox="0 0 16 16">
              <circle cx="8" cy="8" r="8" fill="${iconColor}" />
            </svg>
          `;
          const svg = ` 
          <div class="px-5 py-2.5 w-full items-center p-4 text-center text-sm text-${iconColor}-800 rounded-lg bg-${iconColor}-300 font-medium" role="alert">
            ${params.value}
          </div>`
          return svg;
        }
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      var cols = [                
                { field: "pagina" , filter: true,minWidth:100, cellStyle: { textAlign: 'center' },
                cellRenderer: params => {  
                                    const eBtnx = document.createElement('button');
                                    eBtnx.setAttribute('hx-get', `/webscada/page/${params.data.indice}`);
                                    eBtnx.setAttribute('hx-swap', "outerHTML");
                                    eBtnx.setAttribute('hx-target', "#crossRefGrid");
                                    eBtnx.setAttribute('hx-indicator', "#spinner");
                                    //eBtnx.setAttribute('hx-vals', `${params.data}`);
                                    //eBtnx.setAttribute('class', "px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800");
                                    eBtnx.innerText = params.data.pagina;                                    
                                    htmx.process(eBtnx);
                                    return eBtnx;
                                  }
                                },
                //{ field: "indice" ,  filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "tag", headerName :'Nome', editable : true ,filter: true, minWidth:100, cellStyle: { textAlign: 'center' }},
                //{ field: "tipo" , filter: true, maxWidth:100, cellStyle: { textAlign: 'center' }},                
                { field: "numero", headerName :'Tag', filter: true,minWidth:120, cellStyle: { textAlign: 'center' }},
                //{ field: "impianto" , filter: true, minWidth:100, cellStyle: { textAlign: 'center' }},
                //{ field: "motore" , filter: true, maxWidth:100, cellStyle: { textAlign: 'center' }},
                //{ field: "valore" , filter: true, maxWidth:100, cellStyle: { textAlign: 'center' }},
                /*{ field: "timestamp", filter: true,minWidth:100, filter: 'agDateColumnFilter', filterParams: { 
                  comparator: (filterLocalDateAtMidnight, cellValue) => {
                        const cellDate = new Date(cellValue);
                        if (cellDate < filterLocalDateAtMidnight) {
                            return -1;
                        } else if (cellDate > filterLocalDateAtMidnight) {
                            return 1;
                        }
                        return 0;
                    },
                  filterOptions : ['greaterThan','lessThan','inRange'],maxNumConditions:1},cellStyle: { textAlign: 'center' }},                                          
                    */                
            ]
      var gridApi = agGrid.createGrid(gridElement, gridOptions);
      // Transform the object to row data          
      //let gridRowData = transformToRowData(seriesData)
      gridApi.setGridOption("columnDefs", cols);
      return gridApi;
    }    
    var gridApi = gridInit()    
    function onBtnExport() {
      gridApi.exportDataAsCsv();
    }

    htmx.on('htmx:afterRequest', (msg)=> {     
      if  (msg.detail.pathInfo.responsePath.includes("/crossrefdata")){  
        const result = JSON.parse(msg.detail.xhr.response);        
        let seriesData = []
        for (let e in result) {
          seriesData[e] = result[e]
        }
        // Grid handeling
        gridApi.setGridOption("rowData", seriesData);       
      } 
    }); 

    /*gridElement.addEventListener('htmx:beforeRequest', function(event) {
      event.target.textContent = 'In corso...';
    });*/
</script>