import json
import asyncio
import math
from fastapi.requests import Request
from fastapi import WebSocketDisconnect, HTTPException
from typing import Iterator, AsyncIterator
from iset.isetpages import read_synoptics, read_analytics
from iset.isetpages import get_tree
from iset.iset import read_hist_parquet
import uuid
import duckdb
import math
from models import Page
from datetime import datetime , date, timedelta
from fastapi.responses import RedirectResponse, HTMLResponse, JSONResponse, StreamingResponse
import nats.js.errors
from logger import logger
import fsspec
import fsspec.implementations.memory
import concurrent.futures
import nats.errors
import nats.js.errors
async def json_default(obj):
    if isinstance(obj, (date, datetime)):
        return obj.isoformat()
    elif isinstance(obj, float): 
        #print(obj.decode('latin1')) 
        return 0
    
def tag_to_topic(tag):
    try:
        engine = tag.split("::")[0]
        tag_adr = tag.split("::")[1]

        NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
        types =  ["AIN","DIN","AOUT","DOUT","SP"]
        type_idx = int(tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)])
        type = types[type_idx]
        index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])

        topic = f"{engine}.{NLogico}.{type}.{index}"
    except:
        topic = ""
    
    return topic

async def get_pages(appstate):
    """
    method to cache pages in Nats, to avoid reading the files
    """
    try:
        rawpages = await appstate.nc.get('pages')
        pages = json.loads(rawpages.value.decode())
        pages =  get_tree(ui_path= appstate.appconfig['iset']['isetgui'])[0]
    except:
        user = dict()
        pages = get_tree(ui_path= appstate.appconfig['iset']['isetgui'])[0]
        await appstate.nc.put('pages',json.dumps(pages).encode())
    data = list()
    for k,v  in pages.items():
        try:
            data.append(Page(name=v, id=k) )#{'name':v, 'id':k}
        except:
            continue
    return data

async def request_update(appstate,tags, acl):
            if not acl['grant']['update']:
                logger.info(f"{acl['email']} non autorizzato alla richiesta di aggiornamento")
                raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this resource.")  

            #request last by logical number
            tag_list = []
            for tag in tags:
                topic = tag_to_topic(tag).split('.')
                tag_list.append({'engine':topic[0],'logical_number':int(topic[1])})

            try:
                payload = {'tags':tag_list,'request':'source', 'type': 'update', 'user_id':int(acl['id']),'guid':str(uuid.uuid4()),'timeout': 30000}
                raw_response = await appstate.nc.req('iset.consumer.realtime',json.dumps(payload).encode())
                print(raw_response.data.decode())
                #{"engine":"TNR0", "logical_number":[23],"request":"source", "type": "update", "user_id":-1,"guid":"unique id")
            except:
                pass

async def get_tags(request: Request, page :str ,page_id : str, appstate) -> AsyncIterator[str]:
    resources_to_cleanup = []
    #print(page_id)
    if page == 'synoptics':
        key = f'iset.pages.synoptics.{page_id}'
        try:
            #pagesraw = await appstate.nc.get(key) 
            #response =  json.loads(pagesraw.value.decode())
            response = appstate.iset[key]
        except Exception as e:    
            #print('error synoptics',e)
            response = await read_synoptics(path = {page_id:""}, ui_path= appstate.appconfig['iset']['isetgui'])
            try:
                await appstate.nc.put(key,json.dumps(response).encode()) 
            except Exception as e:
                print('Nats kv synoptics put error',e)                    

    if page == 'analytics':
        key = f'iset.pages.analytics.{page_id}'
        try:
            #pagesraw = await appstate.nc.get(key) 
            #response =  json.loads(pagesraw.value.decode())
            response = appstate.iset[key]
        except:    
            response = await read_analytics(path = {page_id:""}, config_dict = appstate.iset, ui_path= appstate.appconfig['iset']['isetgui'])
            try:
                await appstate.nc.put(key,json.dumps(response).encode()) 
            except Exception as e:
                print('Nats kv analytics put error',e)

    client_ip = request.client.host
    logger.info(f"Client {client_ip} connected")
    #mapkv = await appstate.nc.key_value(bucket = 'iset')
    #request last by logical number
    result = {}
    
    tagslist = list(response['tags'].keys())
    tagslist.sort()    
    tags = dict()   
    try:
        while  not await request.is_disconnected():

            for tag in tagslist:
                key = f"iset.last.{tag_to_topic(tag)}"
                if len(tag_to_topic(tag))> 0:
                    if key in appstate.iset:
                        try:
                            tags[tag] = appstate.iset[key]
                            #tags[tag] = json.loads(rawtag.value.decode())
                        except:
                            tags[tag] = {'state':0,'value': 9999}
                    """
                    else:
                        try:
                            rawtag = await appstate.nc.get(key) 
                            appstate.iset[key] = json.loads(rawtag.value.decode())
                        except:
                            pass
                    """
            description = ''               
            try:
                for tag,values in tags.items():
                    if  not math.isnan(values['value']):
                        topic = f"iset.config.{tag_to_topic(tag)}"                                     
                        if len(topic) > 0:
                            try:
                                #rawtag = await appstate.nc.get(topic)                        
                                #tagobj = json.loads(rawtag.value.decode())
                                
                                if topic not in appstate.iset:
                                    rawtag = await appstate.nc.get(topic) 
                                    appstate.iset[topic] = json.loads(rawtag.value.decode())
                                else:
                                    tagobj = appstate.iset[topic]
                            except Exception as e:
                                print(tag,'key not found')
                                continue

                            if tagobj:
                                #print(tagobj)
                                state = 'normale'                            
                                if page == 'analytics':
                                    color = '#0E9F6E'
                                else:
                                    color = 'lime'
                                value = '####'
                                #analog inputs/outputs state handle
                                if tagobj['Type'] == 'AIN' or tagobj['Type'] == 'AOUT':
                                    
                                    if values['value'] < tagobj['AllarmeInf']['Valore']:
                                        state = 'Allarme inferiore'
                                        color = '#E3A008'
                                    if values['value'] > tagobj['AllarmeSup']['Valore']:
                                        state = 'Allarme superiore'
                                        color = '#F05252'   

                                    if len(tagobj['UMisura']) > 0 and page == 'analytics':
                                        val = floatformat(values['value'],tagobj['Formato'])
                                        value = f"{val} {tagobj['UMisura']}"                                            
                                    else:   
                                        value = values['value']       

                                #digital inputs/ouputs state handle
                                if tagobj['Type'] == 'DIN' or tagobj['Type'] == 'DOUT':
                                    if values['value'] == tagobj['SNormale']:
                                        value = tagobj['DNormale'] 
                                        if page == 'analytics':
                                                color = '#0E9F6E'
                                        else:
                                                color = 'lime'
                                    else:
                                        value = tagobj['DAllarme']  
                                        color = '#F05252'
                                    
                                    if page == 'synoptics':
                                        #value = values['value']
                                        #this condition is very important for the consistancy of the ui and the state
                                        if values['value'] == tagobj['SNormale']:
                                            value = 0
                                            description = tagobj["DNormale"]
                                            color = 'lime'
                                        else:
                                            description = tagobj["DAllarme"]
                                            value = 1
                                            color = '#F05252'
                                        
                                        if tagobj["ModoOp"] == "Fuori Scansione":
                                            description = 'F.Sc.' 

                                        #in this case the state indicates auto/man
                                        if tagobj['Type'] == 'AOUT' or  tagobj['Type'] == 'DOUT':
                                            if 'auto' in values['state'] :
                                                color = 'lime'
                                                state = 0
                                            elif 'man' in values['state'] :
                                                color = '#F05252'
                                                state = 1 
                                            #else:
                                            #    state = values['value']

                                    #print(tagobj)

                                #in this case the state indicates auto/man for the outputs for the analytics page
                                if page == 'analytics' and (tagobj['Type'] == 'AOUT' or  tagobj['Type'] == 'DOUT'):
                                    if 'auto' in values['state']:
                                        state = 'automatico'
                                    elif 'man' in values['state']:
                                        state = 'manuale'   

                                if tagobj["ModoOp"] == "Fuori Scansione":
                                    if page == 'analytics':
                                        state = 'Fuori Scansione'
                                    else:
                                        state = 'F.Sc.'
                                    value = state
                                    color = 'grey'

                                if 'anomalia' in values['state']:
                                    if page == 'analytics':
                                        state = 'Anomalia'
                                        color = '#F05252'
                                    else:
                                        state = 'Anom.'
                                        color = '#3F83F8'
                                    value = state
                                try:
                                    ts = datetime.utcfromtimestamp(values['ts']) + datetime.now().astimezone().utcoffset() 
                                except:
                                    ts = datetime(1970,1,1)
                            
                                result[tag] = {'value': value,
                                                'stato': state,
                                                'color': color,
                                                'ts': ts.strftime("%Y-%m-%d %H:%M:%S"),
                                                'description': description,
                                                'unit': tagobj["UMisura"]}                                

                            else:
                                result[tag] = {'value':values['value']}
                                            
            except  asyncio.CancelledError: 
                    if not await request.is_disconnected():
                        print("Cancelled future error")
            except Exception as e:
                    logger.warning(e)
    
            
            '''
            for tag in response['tags']:        
                if len(tag)>0:
                    try:
                        raw = await nc.get(f'opc.{tag}')
                        data = json.loads(raw.value.decode())
                        if data['Value']['value'] is not None:
                            result[tag] = data['Value']['value']
                    except Exception as e:
                        print(tag,e)
            '''    
            
            yield f"data:{json.dumps(result)}\n\n"
            #await asyncio.sleep(2)
            # Use wait_for with shield to make sleep interruptible
            try:
                await asyncio.wait_for(
                    asyncio.shield(asyncio.sleep(2)),
                    timeout=2.1
                )
            except asyncio.TimeoutError:
                # This is expected
                pass
                
            # Check disconnection immediately after sleep
            if await request.is_disconnected():
                break

    except asyncio.CancelledError:
        # Handle cancellation gracefully
        logger.info("Tags realtime stream cancelled")
        raise
    except Exception as e:
        logger.error(f"Error in tags stream: {e}")
        raise
    finally:
        # Clean up any resources
        logger.info("Tags realtime Disconnected - cleaning up resources")
        for resource in resources_to_cleanup:
            # Close/cleanup each resource
            pass

async def get_hist_deprecated(taglist: list, start : datetime, end : datetime, appstate, parquet = False):
    payload = {'tags':taglist, 'start_date':start.strftime("%Y-%m-%d %H:%M:%S"), 'end_date':end.strftime("%Y-%m-%d %H:%M:%S"), 'parquet': True}
    #result = await appstate.nc.req('iset.api.histchart',json.dumps(payload).encode(),timeout = 120)
    if parquet:
        result = await asyncio.to_thread(read_hist_parquet,taglist,start,end)
        print(result)
        return result
        """
        result = read_hist_parquet(taglist,start,end)
        return result
        #return result.data

        """
        with concurrent.futures.ProcessPoolExecutor() as executor:
            future = executor.submit(read_hist_parquet,taglist,start,end)   
            result = future.result()
            print(result)
            return result

    result = await appstate.nc.req('iset.api.histchart',json.dumps(payload).encode(),timeout = 120) 
    tag_values = dict()
    with duckdb.connect() as conn:
        # Create a memory filesystem and write the parquet data to it 
        fs = fsspec.filesystem('memory')          
        with fs.open(f'hist.parquet', 'wb') as file:
            file.write(result.data)
        # Register the memory filesystem and create the table
        conn.register_filesystem(fs)

        for tag in taglist:
            query =f""" SELECT ts,value FROM read_parquet('memory://hist.parquet') where tag = '{tag}' """
            #query =f""" SELECT strftime(ts, '%Y-%m-%d %H:%M:%S') AS ts,value FROM read_parquet('memory://hist.parquet') where tag = '{tag}' """
            tag_values[tag] = conn.query(query).fetchall()

    tags_description = dict()
    datatypes = dict()

    for tag in taglist:
        config = await get_tag_config(tag, appstate)
        tags_description[tag] = config['Nome']
        datatypes[tag] = config['Type']
        

    return {'tags':tag_values,'description':tags_description,'type':datatypes}

async def get_hist_v1(taglist: list, start : datetime, end : datetime, appstate, parquet = False):
    result = await asyncio.to_thread(read_hist_parquet,taglist,start,end)    
    if parquet:
        return result

    tag_values = dict()
    with duckdb.connect() as conn:
        # Create a memory filesystem and write the parquet data to it 
        fs = fsspec.filesystem('memory')          
        with fs.open(f'hist.parquet', 'wb') as file:
            file.write(result)
        # Register the memory filesystem and create the table
        conn.register_filesystem(fs)

        for tag in taglist:
            query =f""" SELECT ts,value FROM read_parquet('memory://hist.parquet') where tag = '{tag}' and value is not null """
            #query =f""" SELECT strftime(ts, '%Y-%m-%d %H:%M:%S') AS ts,value FROM read_parquet('memory://hist.parquet') where tag = '{tag}' """
            tag_values[tag] = conn.query(query).fetchall()

    tags_description = dict()
    datatypes = dict()

    for tag in taglist:
        config = await get_tag_config(tag, appstate)
        tags_description[tag] = config['Nome']
        datatypes[tag] = config['Type']        
        
    return {'tags':tag_values,'description':tags_description,'type':datatypes}

async def get_hist(taglist: list, start: datetime, end: datetime, appstate, parquet=False):
    try:
        result = await asyncio.to_thread(read_hist_parquet, taglist, start, end)    
        if parquet:
            return result

        tag_values = dict()
        # Create memory filesystem without context manager
        fs = fsspec.filesystem('memory')
        try:
            # Write to memory filesystem with proper error handling
            with fs.open(f'hist.parquet', 'wb') as file:
                file.write(result)
            
            # Use a separate DuckDB connection with proper error handling
            with duckdb.connect() as conn:
                try:
                    # Register the memory filesystem and create the table
                    conn.register_filesystem(fs)
                    
                    for tag in taglist:
                        try:
                            # Add error handling for the query
                            query = f"""SELECT ts, value FROM read_parquet('memory://hist.parquet') 
                                       WHERE tag = '{tag}' AND value IS NOT NULL"""
                            tag_values[tag] = conn.query(query).fetchall()
                        except Exception as e:
                            logger.error(f"Error querying tag {tag}: {str(e)}")
                            tag_values[tag] = []  # Provide empty result on error
                except Exception as e:
                    logger.error(f"DuckDB error: {str(e)}")
        except Exception as e:
            logger.error(f"Memory filesystem error: {str(e)}")
        finally:
            # Explicitly clean up resources to prevent memory leaks
            try:
                # Remove the temporary file
                if fs.exists('hist.parquet'):
                    fs.rm('hist.parquet')
                # Clear the filesystem cache
                fs.clear_instance_cache()
            except Exception as e:
                logger.error(f"Error cleaning up memory filesystem: {str(e)}")
        
        # Process tag descriptions outside the filesystem context
        tags_description = dict()
        datatypes = dict()

        for tag in taglist:
            try:
                config = await get_tag_config(tag, appstate)
                tags_description[tag] = config['Nome']
                datatypes[tag] = config['Type']
            except Exception as e:
                logger.error(f"Error getting config for tag {tag}: {str(e)}")
                tags_description[tag] = tag  # Use tag as fallback description
                datatypes[tag] = "Unknown"
                
        return {'tags': tag_values, 'description': tags_description, 'type': datatypes}
    
    except Exception as e:
        logger.error(f"Error in get_hist: {str(e)}")
        # Return empty results on error
        return {'tags': {tag: [] for tag in taglist}, 
                'description': {tag: tag for tag in taglist}, 
                'type': {tag: "Unknown" for tag in taglist}}
    
async def get_alarms(appstate):
    today = datetime.now()
    key = f"iset.alarms.{today.year}.{today.month}"
    rawalarms= await appstate.alarmskv.get(key)
    result = []
    try:
        with duckdb.connect() as conn:
            # Create a memory filesystem and write the parquet data to it            
            with appstate.fs.open(f'alarms/iset/year={today.year}/month={today.month}/{key}.parquet', 'wb') as file:
                file.write(rawalarms.value)
            # Register the memory filesystem and create the table
            conn.register_filesystem(appstate.fs)
            data = conn.query(f"""SELECT timestamp,livello, tag, messaggio, periferica FROM read_parquet('memory://alarms/iset/**/*.parquet') 
                              where ack_timestamp = '0001-01-01 00:00:00' 
                              and year = {today.year} AND month = {today.month}
                              order by timestamp desc""").fetchmany(1000)           

            for col in data:
                row = {}
                row['timestamp'] = col[0]#.strftime("%Y-%m-%dT%H:%M:%S")  
                row['livello'] = col[1]
                row['name'] = col[2]
                row['messaggio'] = col[3]
                row['periferica'] = col[4]
                result.append(row)        
        return result 
    except Exception as e:
        print(e)

async def get_local_alarms(appstate, tag, grant):
    
    #for GLOG alarms
    if tag in appstate.glogcfg:
        path = f'alarms/glog/events.parquet'
        try:            
            read = appstate.fs.info(path)            
        except:
            try:
                rawalarms = await appstate.nc.get('glog.events')          
                with appstate.fs.open(path, 'wb') as file:
                    file.write(rawalarms.value)
            except:
                pass 

        #rawalarms = await appstate.nc.get('glog.events')
        rtu = appstate.glogcfg[tag]['rtu']
        tag = appstate.glogcfg[tag]['tag']
        data = None
        #if rawalarms:            
        try:
            with duckdb.connect() as conn:
                # Register the memory filesystem and create the table
                conn.register_filesystem(appstate.fs)
                query = f"SELECT dataora,rtu,tag,valore,ROUND(param1,2) FROM read_parquet('memory://alarms/glog/events.parquet') where rtu = '{rtu}' and tag like '%{tag}%' order by dataora desc"
                data = conn.query(query).fetchmany(500)         
                #print(data)                       
        except Exception as e:
            print(e)            
        return {'items' : data}
    
    else:
        params = {'start': '', 'end': '', 'offset': 0, 'limit': 100, 'filter': {}, 'tag':tag}
        result = await alarms_archive(appstate, params, grant)
        return {'iset' : result}

    
async def alarms_archive(appstate, param, grant ):
    #get users by their IDs 
    iset_user_ids = dict()
    for item in appstate.users:     
        id = int(item['usr_id']) + 1
        iset_user_ids[id] = item['login']
        
    end_date = datetime.now() 
    start_date = end_date + timedelta(days=-60)
    """ Deprecated filter"""
    if len(param['start'])>0:
        start_date = datetime.strptime(param['start'], "%Y/%m/%d")

    if len(param['end'])>0:
        end_date = datetime.strptime(param['end'], "%Y/%m/%d")
        end_date = end_date.replace(hour=23,minute=59,second=59)
    startdate = start_date.strftime('%Y-%m-%d %H:%M:%S')
    enddate = end_date.strftime('%Y-%m-%d %H:%M:%S')

    offset = param.get('offset',0)
    limit = param.get('limit',500)
    result = []
    filter = []
    condition =""
    for name, value in param['filter'].items():
        if value['filterType'] == 'text':
            if value['type'] == 'contains':    
                filter.append(f"{name} ILIKE '%{value['filter']}%'")  
            if value['type'] == 'equals':    
                filter.append(f"{name} = '{value['filter']}'" )                  
     
        if value['filterType'] == 'number':
            if value['type'] == 'equals':    
                filter.append(f"{name} = '{value['filter']}'")  
            if value['type'] == 'greaterThan':    
                filter.append(f"{name} > '{value['filter']}'" )
            if value['type'] == 'lessThan':    
                filter.append(f"{name} < '{value['filter']}'" )  

        if value['filterType'] == 'date':
            if value['type'] == 'equals':    
                filter.append(f" CAST({name} AS DATE) = CAST('{value['dateFrom']}' AS DATE) ")  
            if value['type'] == 'greaterThan':    
                filter.append(f"{name} > '{value['dateFrom']}'" )
            if value['type'] == 'lessThan':    
                filter.append(f"{name} < '{value['dateFrom']}'" )      
            if value['type'] == 'inRange':    
                filter.append(f"{name} BETWEEN '{value['dateFrom']}' AND '{value['dateTo']}'" )  
                end_date = datetime.strptime(value['dateTo'], "%Y-%m-%d %H:%M:%S") 

            #extract iset indexes for files (year, month/yearsliced)
            start_date = datetime.strptime(value['dateFrom'], "%Y-%m-%d %H:%M:%S")
        
        condition = 'WHERE ' + ' AND '.join(filter)
    if 'tag' in param:
        if len(condition) == 0:
            condition = 'WHERE '
        else:
            condition += ' AND '
        condition += f"address = '{param['tag']}'"

    #iset indexing matter        
    year_difference = end_date.year - start_date.year
    month_difference = end_date.month - start_date.month    
    total_months = (year_difference * 12) + month_difference    
    year_month_list = [
        (start_date.year + (month // 12),(month % 12) + 1)
        for month in range(start_date.month - 1, start_date.month  + total_months)
    ]
    #print(year_month_list)
    for element in year_month_list:
        key = f"iset.alarms.{element[0]}.{element[1]}"
        path = f'alarms/iset/year={element[0]}/month={element[1]}/{key}.parquet'
        try:            
            read = appstate.fs.info(path)            
        except:
            try:
                rawalarms= await appstate.alarmskv.get(key)            
                with appstate.fs.open(path, 'wb') as file:
                    file.write(rawalarms.value)
            except:
                    pass   

    #get permission data table in duckdb
    taglist = list()
    user_alarm_group = grant['alarmgroup']
    for key,config in appstate.iset.items():
        try:
            if ("iset.config" not in key or "info" in key):
                continue
            tag_group = config.get('Gruppo',0)
            if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])

                if  (tag_group & alarm_grant):
                    taglist.append({'address':config['Tag']})  
        except:
            pass       
    try:
        with duckdb.connect() as conn:
            conn.register_filesystem(appstate.fs)
            query = f"""SELECT timestamp,livello, tag as name, messaggio, periferica, address, ackid FROM read_parquet('memory://alarms/iset/**/*.parquet', union_by_name = true) 
                        {condition} ORDER BY timestamp DESC LIMIT {limit} OFFSET {offset}""" 
            if (len(user_alarm_group)>0): #it belongs to some alarm group in ISET idronetwork 
                path = f'alarms/iset/{user_alarm_group}permission.json'
                #fs = fsspec.filesystem('memory')
                with appstate.fs.open(path, 'w') as file:
                    file.write(json.dumps(taglist))
                # Register the memory filesystem and create the table
                conn.execute(f"CREATE TABLE IF NOT EXISTS PERMISSION AS SELECT * FROM read_json_auto('memory://{path}')")
                #conn.query(f'select * from PERMISSION').show()                
                query = f"""SELECT * FROM (SELECT timestamp,livello, tag as name, messaggio, periferica, address, ackid FROM read_parquet('memory://alarms/iset/**/*.parquet', union_by_name = true) 
                            {condition}) as T1 INNER JOIN PERMISSION ON T1.address = PERMISSION.address ORDER BY timestamp DESC LIMIT {limit} OFFSET {offset}"""  

            print(query)
            #conn.query(query).show()
            data = conn.query(query).fetchall()    
            for col in data:
                #alarm acl old style -- deprecated
                """
                tag_group = appstate.iset[f"iset.config.{tag_to_topic(col[5])}"].get('Gruppo',0)
                user_alarm_group = grant['alarmgroup']

                if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                    alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])
                    if not (tag_group & alarm_grant):
                        continue  
                """
                row = {}
                row['timestamp'] = col[0] #.strftime("%Y-%m-%d %H:%M:%S")  
                row['livello'] = col[1]
                row['name'] = col[2]
                row['messaggio'] = col[3]
                row['periferica'] = col[4]
                row['tag'] = col[5]
                id = col[6]
                row['utente'] = iset_user_ids.get(id,'')
                result.append(row)  

    except Exception as e:
        print(e)

    return result
              
async def realtime_alarms(appstate,grant):
    lastalarm = None
    #get permission data table in duckdb
    taglist = list()
    user_alarm_group = grant['alarmgroup']
    for key,config in appstate.iset.items():
        try:
            if ("iset.config" not in key or "info" in key):
                continue
            tag_group = config.get('Gruppo',0)
            if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])

                if  (tag_group & alarm_grant):
                    taglist.append({'address':config['Tag']})  
        except:
            pass     
    try:
        logger.info("Rt alarms connected")         
        while True:

            today = datetime.now()
            pastdays = today + timedelta(days=-15)
            date_clause = pastdays.strftime("%Y-%m-%d %H:%M:%S")
            key = f"iset.alarms.{today.year}.{today.month}"
            try:
                rawalarms= await appstate.alarmskv.get(key)

                with appstate.fs.open(f'alarms/iset/year={today.year}/month={today.month}/{key}.parquet', 'wb') as file:
                    file.write(rawalarms.value)
            except:
                pass   
            dateclause = f"year = {today.year} AND month = {today.month}"
            if pastdays.year != today.year or pastdays.month != today.month:
                key = f"iset.alarms.{pastdays.year}.{pastdays.month}"
                try:
                    rawalarms= await appstate.alarmskv.get(key)
                    with appstate.fs.open(f'alarms/iset/year={pastdays.year}/month={pastdays.month}/{key}.parquet', 'wb') as file:
                        file.write(rawalarms.value) 
                except:
                    pass
                dateclause = f"((year = {today.year} AND month = {today.month}) OR (year = {pastdays.year} AND month = {pastdays.month}))"
            
            result = []
            with duckdb.connect() as conn:
                # Create a memory filesystem and write the parquet data to it           

                # Register the memory filesystem and create the table
                conn.register_filesystem(appstate.fs)
        
                query =f"""SELECT timestamp,livello, tag, messaggio, periferica, address, record_number FROM read_parquet('memory://alarms/iset/**/*.parquet', union_by_name = true) 
                            where ack_timestamp = '0001-01-01 00:00:00' and tipo_allarme >= 0 and ackid = 0 and timestamp > '0001-01-01 00:00:00' 
                            and {dateclause}
                            and not (address like '%IS150%' and tag like '%Comunicazione%') order by timestamp desc
                        """                 
                #conn.query(query).show() 
                if (len(user_alarm_group)>0): #it belongs to some alarm group in ISET idronetwork 
                    path = f'alarms/iset/{user_alarm_group}permission.json'

                    with appstate.fs.open(path, 'w') as file:
                        file.write(json.dumps(taglist))
                    # Register the memory filesystem and create the table
                    conn.execute(f"CREATE TABLE IF NOT EXISTS PERMISSION AS SELECT * FROM read_json_auto('memory://{path}')")
                    #conn.query(f'select * from PERMISSION').show()                
                    query = f"""SELECT * FROM (SELECT timestamp,livello, tag, messaggio, periferica, address, record_number FROM read_parquet('memory://alarms/iset/**/*.parquet', union_by_name = true) 
                            where ack_timestamp = '0001-01-01 00:00:00' and tipo_allarme >= 0 and ackid = 0 and timestamp > '0001-01-01 00:00:00' 
                            and {dateclause}
                            and not (address like '%IS150%' and tag like '%Comunicazione%')) as T1 INNER JOIN PERMISSION ON T1.address = PERMISSION.address ORDER BY timestamp DESC"""  

                #print(query)        
                data = conn.query(query).fetchmany(500)   
                for col in data:
                    #alarm acl
                    """
                    tag_group = appstate.iset[f"iset.config.{tag_to_topic(col[5])}"].get('Gruppo',0)
                    user_alarm_group = grant['alarmgroup']

                    if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                        alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])
                        if not (tag_group & alarm_grant):
                            continue    
                    """            
                    row = {}
                    row['timestamp'] = col[0].strftime("%Y-%m-%d %H:%M:%S")  
                    row['livello'] = col[1]
                    row['name'] = col[2]
                    row['messaggio'] = col[3]
                    row['periferica'] = col[4]
                    row['tag'] = col[5]
                    row['record_number'] = col[6]

                    result.append(row)     
            if len(result) > 0:    
                if lastalarm != result[0] or len(result) != alarmcount:
                    alarmcount = len(result)
                    lastalarm = result[0]
                    yield f"data:{json.dumps(result)}\n\n"         
            await asyncio.sleep(2) 
    finally:
        logger.info("Rt alarms diconnected")  
async def alarms_ack(appstate, query, acl):

    if acl['grant']['ack'] == False:
        logger.info(f"{acl['email']} non autorizzato all'ack degli allarmi")
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this resource.")  

    tags = list()
    for data in query:
        tags.append(f"{data['tag']};{data['timestamp']};{data['record_number']}")

    data = {'guid' : str(uuid.uuid4()),
        'request': 'ack',
        'user_id': int(acl['id']),
        'payload' : tags
    }        
    response = await appstate.nc.req('iset.consumer.ack', json.dumps(data).encode(), timeout = 20)
    print(response.data.decode())

def floatformat(value,formato):
    if len(formato) > 0:
        decimal_part = formato.split('.')
        if len(decimal_part) == 2:
            decimals = len(decimal_part)
            format_string = "{:." + str(decimals) + "f}" #Create the format string
            formatted_number = format_string.format(value)
            return float(formatted_number)
        else:
            format_string = "{:.1f}" #Create the format string
            formatted_number = format_string.format(value)
            return float(formatted_number)
    else:
        return value

async def get_tag_config(tag: str, appstate):         
    try:
        tagconfig = appstate.iset[f"iset.config.{tag_to_topic(tag)}"]
        tagconfig['cross'] = await get_crossref(appstate,tag)
        if tagconfig['Type'] == 'DIN' or tagconfig['Type'] == 'DOUT':
            values = appstate.iset[f"iset.last.{tag_to_topic(tag)}"]        
            if values['value'] == tagconfig['SNormale']:
                value = 0
                description = tagconfig["DNormale"]
                color = 'lime'
            else:
                description = tagconfig["DAllarme"]
                value = 1
                color = '#F05252'
            
            if tagconfig["ModoOp"] == "Fuori Scansione":
                description = 'F.Sc.' 
                color = 'grey'

            tagconfig['description'] = description
            tagconfig['value'] = value
            tagconfig['color'] = color
            tagconfig['ts'] = datetime.fromtimestamp(values['ts']).strftime('%Y-%m-%d %H:%M:%S')
        else:
            values = appstate.iset[f"iset.last.{tag_to_topic(tag)}"] 
            tagconfig['ts'] = datetime.fromtimestamp(values['ts']).strftime('%Y-%m-%d %H:%M:%S')
            tagconfig['value'] = values['value']    
            #decimals according to iset
            tagconfig['value'] = floatformat(values['value'] ,tagconfig['Formato'])       
            tagconfig["AllarmeInf"]["Valore"] = floatformat(tagconfig["AllarmeInf"]["Valore"] ,tagconfig['Formato'])    
            tagconfig["AllarmeSup"]["Valore"] = floatformat(tagconfig["AllarmeSup"]["Valore"] ,tagconfig['Formato']) 
            tagconfig["PAllarmeInf"]["Valore"] = floatformat(tagconfig["PAllarmeInf"]["Valore"] ,tagconfig['Formato']) 
            tagconfig["PAllarmeSup"]["Valore"] = floatformat(tagconfig["PAllarmeSup"]["Valore"] ,tagconfig['Formato']) 
        return tagconfig
    except Exception as e:
        print(tag, e)

async def get_reports(appstate,):
    result = await appstate.scadakv.get("mmm.tasks.anagrafica")
    reports = json.loads(result.value.decode())
    obj_reports = json.loads(reports)
    data = list()
    for report in obj_reports:
        row = dict()
        row['id'] = report['id']
        row['nome'] = report['name']
        row['cron'] = report['cron']
        row['destinatario'] =""
        for dest in report['to']:
            row['destinatario'] += dest['text'] + '\n'
        row['abilitato'] = 'Sì' if report['is_enable'] == 1 else 'No'
        row['test'] = 'Sì' if report['is_test'] == 1 else 'No'
        row['azioni'] = report['id']
        data.append(row)
    return data

async def get_map(appstate):
    result = list()
    CH_MODE = {"0":"Fuori Scansione", "1":"In Scansione","2": "Scansione e limiti","3": "Trasmiss. evento",     
                "4":"Allarme", "5":"Allarme inferiore","6":"Allarme superiore","7":"Non Valido","8":"Canale esportato",
                "17":"Importato Modo 1", "18":"Importato Modo 2","19":"Importato Modo 3",
                "20":"Importato Modo 4","21":"Importato Modo 5","22":"Importato Modo 6"}
    
    CH_TYPE = { "0":"0-10 volt","1":"0-2.5 volt","2":"4-20 mA","3":"0-20 mA","4":"4-24 mA","5":"0-24 mA",
               "6":"Linearizzazione","7":"Parametri da formula","8":"Non condizionato"}
    
    for key,item in appstate.iset.items():
        try:
            if 'iset.config' in key:
                row  = dict()
                row["tag"] = item['Tag']
                row["nome"] = item['Nome'] 
                row["numero"] = item['Number']                    
                row["impianto"] = item['Plant']                 
                row["motore"] = item['Engine']                  
                row["tipo"] = item['Type']
                try:
                    index = str(item["ModoOp"])
                    row["modo"] = CH_MODE[index]
                except:
                    row["modo"] = item["ModoOp"]
                try:
                    index = str(item["TipoSegnale"] )
                    row["tipo_canale"] = CH_TYPE[index]
                except:
                    row["tipo_canale"] = item["TipoSegnale"]                
                row["inizio_scala"] = round(item["InizioScala"],3)
                row["fondo_scala"] = round(item["FondoScala"] ,3)
                row["umisura"] = item["UMisura"] 
                row["stato_riposo"] = item["DNormale"] 
                row["stato_eccitato"] = item["DAllarme"]  
                row["exp_address"] = item["ExpAddress"]  

                try:
                    row["codice_sonoro_inf"] = item["AllarmeInf"]["MessaggioIn"] 
                    row["codice_sonoro_sup"] = item["AllarmeSup"]["MessaggioIn"] 
                except Exception as e:
                    print(e)

                try:
                    last = appstate.iset[key.replace('config','last')] 
                    if math.isnan(last["value"]):                        
                        row["valore"] = 0            
                    else: 
                        row["valore"] = last["value"]      
                    row["timestamp"] = datetime.fromtimestamp(last['ts']).strftime('%Y-%m-%d %H:%M:%S')                                           
                except Exception as e:
                    row["valore"] =  None
                    row["timestamp"] = datetime.fromtimestamp(0).strftime('%Y-%m-%d %H:%M:%S')    
                result.append(row)
        except:
            pass
    return result

async def get_tickets(appstate, email):
    tickets = dict()
    result= list()
    key = f"tickets.{email.replace('.','').replace('@','')}.*"
    watcher = await appstate.helpdeskv.watch(key)
    try:
        async for raw in watcher:
            try:
                tickets[raw.key] = json.loads(raw.value.decode())
                result.append(tickets[raw.key])
            except:
                tickets[raw.key]  = raw.value 
    except nats.errors.TimeoutError as e:
            pass 

    invisione = list()
    try:
        with duckdb.connect('webscada.db') as conn:
            query = f""" select id,aggiornato, categoria, creato, email, event,invisione,
                            oggetto,priorita,richiesta,stato,tecnico,titolo,ts,utente
                        from helpdesk where '{email}' in invisione """
            invisione = conn.sql(query).fetchall()
    except Exception as e:
        print(e)

    for ticket in invisione:
        row = dict()
        row['id'] = ticket[0]
        row['aggiornato'] = ticket[1]
        row['categoria'] = ticket[2]
        row['creato'] = ticket[3]
        row['email'] = ticket[4]
        row['event'] = ticket[5]
        row['invisione'] = ticket[6]
        row['oggetto'] = ticket[7]
        row['priorita'] = ticket[8]
        row['richiesta'] = ticket[9]
        row['stato'] = ticket[10]
        row['tecnico'] = ticket[11]
        row['titolo'] = ticket[12]
        row['ts'] = ticket[13]
        row['utente'] = ticket[14]
        row['owner'] = False

        result.append(row)        
    return result

async def get_tickets_dashboard(appstate, auth_data):
    try:
        tickets = dict()
        result= list()
        key = f"tickets.*.*"
        watcher = await appstate.helpdeskv.watch(key)
        try:
            async for raw in watcher:
                try:
                    tickets[raw.key] = json.loads(raw.value.decode())
                    result.append(tickets[raw.key])
                except:
                    tickets[raw.key] = raw.value 
        except nats.errors.TimeoutError as e:
            pass   
        logger.info(f'Dashboard helpdesk letto da {auth_data["email"]}')       
        #--------------------------------------
        path = f'helpdesk/tickets.json'

        with appstate.fs.open(path, 'w') as file:
            file.write(json.dumps(result))
        
        with duckdb.connect('webscada.db') as conn:
            conn.register_filesystem(appstate.fs)  
            try:
                conn.execute(f"CREATE TABLE IF NOT EXISTS HELPDESK AS SELECT * FROM read_json_auto('memory://{path}')")
                conn.execute("ALTER TABLE HELPDESK ADD PRIMARY KEY (id)")
            except:
                pass
            conn.execute(f"INSERT INTO  HELPDESK SELECT * FROM read_json_auto('memory://{path}') ON CONFLICT DO NOTHING")
            conn.sql('select * from HELPDESK').show()
        #--------------------------------------

        return result

    except Exception as e:
        logger.error(f'Dashboard helpdesk : {e}')
    
async def add_tickets(appstate, data):
    result = dict()
    try:
        raw = await appstate.helpdeskv.get('tickets.index')
        rev = raw.revision
        index = int(raw.value.decode()) + 1
        email = data['email'].replace('.','').replace('@','')
        key = f'tickets.{email}.{index}'
        data['id'] = index
        data['event'] = ['Ticket aggiunto']
        await appstate.helpdeskv.put(key=key,value=json.dumps(data).encode())
        await appstate.helpdeskv.update(key='tickets.index',value=str(index).encode(), last=rev)
        #email object
        await appstate.nc.put('helpdesk.email',json.dumps(data).encode())
        #email component
        email = dict()
        email['id'] = data['id']
        email['object'] = 'Ticket n°{id} aggiunto'
        email['operazione'] = data['oggetto']
        email['titolo'] = data.get('titolo','')
        email['priorita'] = data['priorita']
        email['message'] = data['richiesta']
        email['categoria'] = data['categoria']
        email['user'] = data['email']
        email['invisione'] = data.get('invisione',[])
        email['attachments'] = data.get('attachments',[])
        email['tecnico'] = data.get('tecnico','')
        email['stato'] = data.get('stato','')
        email['creato'] = data['creato']
        email['aggiornato'] = data['aggiornato']
        email['event'] = data.get('event',[])

        await appstate.nc.pub('helpdesk.email',json.dumps(email).encode())        
        result['info'] = 'ticket aggiunto'        
    except nats.js.errors.KeyNotFoundError as e:
        await appstate.helpdeskv.put('tickets.index','0'.encode())
        await add_tickets(appstate, data)

    except nats.js.errors.KeyWrongLastSequenceError as e:
        await add_tickets(appstate, data)

    except Exception as e:
        result['error'] = str(e)
        logger.error(f'Creazione ticket fallita: {e}')

    logger.info(f"Ticket {data['id']} creato da {data['email']}")
    return result  

async def get_notes(appstate, data):
    try:
        notes = dict()
        result = list()
        email = data['user'].replace('.','').replace('@','')

        #get ticket info
        key = f"tickets.{email}.{data['id']}"
        raw = await appstate.helpdeskv.get(key)
        info = json.loads(raw.value.decode())

        #get notes for the ticket
        #key = f"tickets.{email}.{data['id']}.notes.>"
        key = f"tickets.*.{data['id']}.notes.>"

        watcher = await appstate.helpdeskv.watch(key)
        try:
            async for raw in watcher:
                try:
                    payload = json.loads(raw.value.decode())
                    datekey = payload['ts']
                    notes[datekey] = payload
                    result.append(notes[datekey])
                except:
                    notes[raw.key]  = raw.value 
        except nats.js.errors.FetchTimeoutError as e:
            pass     
        
        logger.info(f"Lettura note per ticket {data['id']} richiesta da {data['email']}")
          
        return (info,result)
    
    except Exception as e:
        logger.error(f"Lettura note per utente {data['user']} fallito: {e}")
        await get_notes(appstate, data)

async def send_note(appstate, data):
    try:
        email = data['email'].replace('.','').replace('@','')
        index = int(datetime.now().timestamp()*100)
        key = f"tickets.{email}.{data['id']}.notes.{index}"
        data['ts'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        result = await appstate.helpdeskv.put(key=key,value=json.dumps(data).encode())   

        #get ticket info
        key = f"tickets.{email}.{data['id']}"
        raw = await appstate.helpdeskv.get(key)
        info = json.loads(raw.value.decode())
        #email component
        email = dict()
        email['id'] = data['id']
        email['object'] = 'Nuova nota per ticket n°{id}'
        email['from'] = data.get('from','')
        email['to'] = [data['email']]
        email['message'] = data.get('message','')
        email['stato'] = info['stato']
        email['attachments'] = data.get('attachments',[])
        email['ts'] = data['ts']
        email['invisione'] = info['invisione']
        email['titolo'] = info.get('titolo','')
        email['event'] = data.get('event',[])
        await appstate.nc.pub('helpdesk.email',json.dumps(email).encode()) 
        logger.info(f"Nota per ticket {data['id']} inviata da {data['from']} a {data['email']}")
        return result
    
    except Exception as e:
        logger.error(f"invio nota per ticket {data['id']} fallito: {e}")

async def update_ticket(appstate, data, auth_data):
    result = dict()
    try:
        if len(data['tecnico']) == 0:
            result['error'] = f"errore ticket  {id} : tecnico non assegnato"
            logger.info(f"Aggiornamento ticket {data['id']} da {auth_data['email']} fallito: tecnico non assegnato")
            return result 
        
        if data['stato'].lower() == 'creato':
            result['error'] = f"errore ticket  {id} : stato ticket non variato"
            logger.info(f"Aggiornamento ticket {data['id']} da {auth_data['email']} fallito: stato ticket non variato")  
            return result 
                
        email = data['email'].replace('.','').replace('@','')
        id = data['id']
        key = f'tickets.{email}.{id}'
        #fetch ticket info
        raw = await appstate.helpdeskv.get(key)
        info = json.loads(raw.value.decode())
        
        tecnico = info.get('tecnico','')
        stato = info.get('stato','')
        
        priority = info.get('priorita','')
        event = list()

        #check if technician changed
        if tecnico != data['tecnico']:
            event.append(f"Assegnato a {data['tecnico']}")
        
        #check if state changed
        if stato != data['stato']:
            event.append(f"Ticket {data['stato']}")

        #check if priotrity changed
        if priority != data['priorita']:
            event.append(f"Priorità {data['priorita']}")

        if len(event)>0:
            data['event'] = event
            #await send_note(appstate,data) dont' send any note 
        #check if status changed

        #update the ticket
        await appstate.helpdeskv.put(key=key,value=json.dumps(data).encode())
        result['info'] = f'ticket {id} aggiornato'
        #email component
        email = dict()
        email['id'] = data['id']
        email['object'] = 'Aggiornamento ticket n°{id}'
        email['operazione'] = data['oggetto']
        email['titolo'] = data.get('titolo','')
        email['priorita'] = data['priorita']
        email['message'] = data['richiesta']
        email['categoria'] = data['categoria']
        email['user'] = data['email']
        email['invisione'] = data.get('invisione',[])
        email['attachments'] = data.get('attachments',[])
        email['tecnico'] = data.get('tecnico','')
        email['stato'] = data.get('stato','')
        email['creato'] = data['creato']
        email['aggiornato'] = data['aggiornato']
        email['event'] = data.get('event',[])

        await appstate.nc.pub('helpdesk.email',json.dumps(email).encode())  
        logger.info(f"Ticket {data['id']} aggiornato da {auth_data['email']}")
    except Exception as e:
        result['error'] = f"errore ticket  {id} : {str(e)}"
        logger.info(f"Aggiornamento ticket {data['id']} da {auth_data['email']} fallito: {e}")

    return result  

async def get_webusers(appstate):
    #return {'items': appstate.users, 'users': appstate.iset}
    data = list()

    #get users with an email config
    user_emails = dict()
    #user_presence = dict()
    for key, item in appstate.iset.items():
        if "iset.user." in key :            
            try:
                key_index = f"iset.user.{item['email'].lower().replace('@','.')}"
                if key == key_index :
                    webuser = item
                    user_emails[item['id']] = item
                    #user_presence[item['id']] =  appstate.iset.get(f"{key_index}.lastseen","")
            except:
                continue

    for user in appstate.users:
        row = dict()
        row['id'] = user['usr_id']
        row['nome'] = user['login']
   
        if user['usr_id'] in user_emails:
            webuser = user_emails[user['usr_id']]
            row['email'] = webuser["email"]
            row['webscada']  = webuser["webscada"]
            row['helpdesk']  = webuser["helpdesk"]
            row['cctv']  = webuser.get('cctv',False)
            row['group']  = webuser.get('group','')
            row['sessione']  = webuser.get('sessione','24')
            email_index = f"iset.user.{webuser['email'].replace('@','').replace('.','')}"
            row['presence']  = appstate.iset.get(f"{email_index}.lastseen","")
        else:
            row['email'] = ''
            row['webscada']  = False
            row['helpdesk']  = False
            row['cctv'] = False
            row['presence'] = ""

        row['password'] =  '*****'
        if user['deleted'] == '0':
            row['abilitato'] = 'Sì'
        else:
            row['abilitato'] = 'No'

        data.append(row)
    return data
       
async def write_tag(appstate,tag,value,mode,acl):

    if not acl['grant']['write']:
        logger.info(f"{acl['email']} non autorizzato alla richiesta di scrittura")
        raise HTTPException(status_code=403, detail="Forbidden: You don't have access to this resource.")  

    topic = tag_to_topic(tag)
    if '.DOUT.' in topic:
        data_type ='DO'
    elif '.AOUT.' in topic:
        data_type ='AO'
    elif '.SP.' in topic:
        data_type = 'SPN'

    if data_type != 'SPN':
        data = {'guid' : str(uuid.uuid4()),
                'timestamp': datetime.now().timestamp(),
                'user_id': int(acl['id']),
                'datatype' : data_type,
                'type'  : 'write',
                'payload' : {
                    'tags':[tag],
                    'value':[value],
                    'mode' : [mode]
                }
            }
    else:
        data = {'guid' : str(uuid.uuid4()),
                'timestamp': datetime.now().timestamp(),
                'user_id': int(acl['id']),
                'datatype' : data_type,
                'type'  : 'write',
                'payload' : {
                    'tags':[tag],
                    'value':[value],
                }
            }        
    await appstate.nc.pub('iset.consumer', json.dumps(data).encode())
    if 'TNP::' in tag:
        #only for plc tags
        data = {'tag':tag,'value':value}
        response = await appstate.nc.req('opcua.write', json.dumps(data).encode(), timeout = 10) 
        feedback = json.loads(response.data.decode())   
        key = f"iset.last.{tag_to_topic(tag)}"        
        appstate.iset[key]['value'] = feedback['value']
        appstate.iset[key]['ts'] = datetime.now().timestamp()

    else:
        response = await appstate.nc.req('iset.consumer.write', json.dumps(data).encode(), timeout = 20)
    """
    {
    guid: "string",
    timestamp:"double",
    utente:"string",
    type: 'AO,DO,SPN SPS'
    payload: {      //digitale
        tags : [],
        value : [], 0/1/-1 (on off invariato)
        mode : [], 0/1/-1 ( auto/man/invariato)
    },
    payload: {      //analog
        tags : [],
        value : [], float
        mode : [], 0/1/ ( auto/man)
    }    
    payload: {      //setpoint 
        tags : [],
        value : [], float/string
    }     
    """  

async def get_video(appstate,video):
    key = f"cctv_{video}"
    appstate.frames[video] = b''
    async def cb(msg):
        try:
            appstate.frames[video] = msg.data                  
        except Exception as e:
            print('camera error', e)
        #await asyncio.sleep(0.1) 

    await appstate.nc.sub(f'CCTV-GAS.{video}.frames', cb = cb)
    print(f'listening on scada.cctv.{video}')
    try:
        raw = await appstate.cctvobj.get(f'CCTV-GAS.{video}.thumbnail.jpeg')
        frame = raw.data
    except:
        pass

    while True:
        #length = str(len(appstate.frame)).encode() # convert to bytes
        #yield (b'--frame\r\n'
        #    b'Content-Type: image/jpeg\r\n\r\n' + appstate.frame + b'\r\n')  
        if len(appstate.frames[video]) > 0 and isinstance(appstate.frames[video], bytes):
            frame = appstate.frames[video]
            yield (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + appstate.frames[video] + b'\r\n')       
        else:
            yield (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')    
        await asyncio.sleep(0.033)  

async def get_crossref(appstate, tag = None):
    result = list()
    try:
        with duckdb.connect('webscada.db') as conn:
            conn.register_filesystem(appstate.fs)            
            query =f"""SELECT * FROM read_parquet('memory://iset.last.crossreference.parquet')"""
            conn.execute(f"DROP TABLE IF EXISTS  CROSSREF;CREATE TABLE IF NOT EXISTS CROSSREF AS {query}")
            if tag is not None:
                query =f"""SELECT *  FROM read_parquet('memory://iset.last.crossreference.parquet') where tag = '{tag}'"""
            #queryresult = conn.query(query).show()
            queryresult = conn.query(query).fetchall()
            for e in queryresult:
                row = dict()
                row['pagina'] = e[0]
                row['indice']  = e[1]
                row['tag']  = e[2]   
                row["numero"]  = e[2]
                key = f"iset.config.{tag_to_topic(row['tag'])}"     
 
                if key in appstate.iset:   
                    row['tag'] = appstate.iset[key]['Nome']
                    """
                    row["numero"] = appstate.iset[key]['Number']                    
                    row["impianto"] = appstate.iset[key]['Plant']                 
                    row["motore"] = appstate.iset[key]['Engine']                  
                    row["tipo"] = appstate.iset[key]['Type']
                    try:
                        last = appstate.iset[key.replace('config','last')] 
                        if math.isnan(last["value"]):                        
                            row["valore"] = 0            
                        else: 
                            row["valore"] = last["value"]      
                        row["timestamp"] = datetime.fromtimestamp(last['ts']).strftime('%Y-%m-%d %H:%M:%S')                                           
                    except Exception as e:
                        row["valore"] =  None
                        row["timestamp"] = datetime.fromtimestamp(0).strftime('%Y-%m-%d %H:%M:%S') 
                    """
                result.append(row)   
    except Exception as e:
        print(e)

    return result

async def scadabi_devices_deprecated(appstate, grant,plant = None):
    result = list()
    #get users by their IDs 
    iset_user_ids = dict()
    for item in appstate.users:     
        id = int(item['usr_id']) + 1
        iset_user_ids[id] = item['login']

    #get permission data table in duckdb
    taglist = list()
    user_alarm_group = grant['alarmgroup']
    print(grant)
    isadmin = grant.get('admin',False)
    for key,config in appstate.iset.items():
        try:
            if ("iset.config" not in key or "info" in key):
                continue
            if isadmin:
                taglist.append({'address':config['Tag'],'name':config['Nome'],'impianto':config['Plant'],'engine':key.split('.')[3]}) 
                user_alarm_group = 'admin'
            else:
                tag_group = config.get('Gruppo',0)
                if user_alarm_group in appstate.iset['alarmgroup'] and len(user_alarm_group) > 0 :
                    alarm_grant = int(appstate.iset['alarmgroup'][user_alarm_group]['group'])

                    if  (tag_group & alarm_grant):
                        taglist.append({'address':config['Tag'],'name':config['Nome'],'impianto':config['Plant'],'engine':key.split('.')[3]})  
        except:
            pass       
    
    try:
        with duckdb.connect() as conn:
            conn.register_filesystem(appstate.fs)

            if (len(user_alarm_group)>0): #it belongs to some alarm group in ISET idronetwork 
                path = f'scadabi/{user_alarm_group}permission.json'

                with appstate.fs.open(path, 'w') as file:
                    file.write(json.dumps(taglist))
                
                conn.execute(f"CREATE TABLE IF NOT EXISTS PERMISSION AS SELECT * FROM read_json_auto('memory://{path}')")
                if plant:
                    query = f"""SELECT name,address from permission where engine = '{plant}'"""
                    conn.query(query).show()    
                    result = conn.query(query).fetchall()
                else:
                    query = f"""SELECT impianto,ANY_VALUE(engine) from permission group by impianto"""
                    conn.query(query).show()    
                    result = conn.query(query).fetchall()
            
        #for element in data:
        #    result.append(element[0])
            #conn.query(query).show()

    except Exception as e:
        print(e)
    return result

async def scadabi_devices(appstate, grant,plant = None):
    isadmin = grant.get('admin',False)   
    user_grant = None
    for access in grant['user']['profile']['DR']['group']:
        if access['name'] == 'LAY':
            user_grant = access['right']    
    result = list()
    try:
        with duckdb.connect('webscada.db') as conn:
            conn.register_filesystem(appstate.fs)
            if isadmin:
                q1 = "select * from crossref"
            else:
                q1 = f"""select * from crossref where ("grant" & {user_grant}) > 0"""

            conn.query(q1).show()    

            #q1 = "SELECT page from crossref group by page"
            q2 = """ select * from anagrafica_impianti where coordinate is not null"""
            conn.query(q2).show()    

            query = f""" select T2.descrizione,T1.index,T1.tag from ({q1}) T1 inner join ({q2}) T2 on T1.page = T2.pagina """
            conn.query(query).show()    
            queryresult = conn.query(query).fetchall()
            
            for e in queryresult:
                row = dict()
                row['pagina'] = e[0]
                row['indice']  = e[1]
                row['tag']  = e[2]   
                row["numero"]  = e[2]
                key = f"iset.config.{tag_to_topic(row['tag'])}"     
 
                if key in appstate.iset:   
                    row['tag'] = appstate.iset[key]['Nome']

                result.append(row)
    except Exception as e:
        logger.error(e)
    return result

async def get_anagrafica(appstate):
    result = list()
    anagrafica_impianti = appstate.iset.get('iset.config.anagrafica.info',{})
    if isinstance(anagrafica_impianti, bytes):
        anagrafica_impianti = {}
   
   #manual map setting
    anagrafica = list()
    for k,v in anagrafica_impianti.items():
        #if 'modified_at' not in v:
        #    v['modified_at'] = '1970-01-01 00:00:00'
        row = {'impianto':v['impianto'],'pagina':v['pagina'],'descrizione':v['descrizione'],
               'coordinate':v['coordinate'],'modificato':v['modificato'],'user':v['user']}
        anagrafica.append(row)

    #--------------------------------------------------------        
    path = 'iset/anagrafica_impianti.json'
    with appstate.fs.open(path, 'w') as file:
        file.write(json.dumps(anagrafica)) 

    #INSERT INTO anagrafica_web SELECT * FROM read_json_auto('memory://{path}')""")        
    with duckdb.connect('webscada.db') as conn:   
        conn.register_filesystem(appstate.fs) 
        conn.execute(f"""DROP TABLE IF EXISTS  anagrafica_web;
                    CREATE TABLE IF NOT EXISTS anagrafica_web (impianto VARCHAR,pagina VARCHAR,descrizione VARCHAR,coordinate VARCHAR,modificato TIMESTAMP,user VARCHAR)""")
        if len(anagrafica) > 0:
            conn.execute(f"INSERT INTO anagrafica_web SELECT * FROM read_json_auto('memory://{path}')")
            #conn.executemany('INSERT INTO anagrafica_web VALUES (?, ?, ?, ?, ?, ?)', anagrafica) 
        conn.sql('select * from anagrafica_web').show()

        qcross = " select distinct split_part(TRIM(split_part(page, '-', 2)),' ',1) as plant, REPLACE(page,'/','|') as page  from CROSSREF"

        qmapw = f"""select  TCROSS.plant,TCROSS.page,
            WATER.NAME as watermapname, WATER.ID_MISURA,WATER.LATITUDINE, WATER.LONGITUDINE
            from  ({qcross}) as TCROSS left JOIN WATER ON TCROSS.plant LIKE '%' || WATER.ID_MISURA || '%'  """

        qmapg = f"""select  TCROSS.plant,TCROSS.page,
                split_part(TCROSS.page, '-', 2) || '🔶' ||GAS.IDENTIFICATIVO_OGGETTO_MAPPA as gasmapname,
                GAS.ID_PERIFERICA,GAS.LATITUDINE, GAS.LONGITUDINE
                from  ({qcross}) as TCROSS left JOIN GAS ON GAS.ID_PERIFERICA  LIKE '%' || TCROSS.plant || '%'  """
    
        query = f"""DROP TABLE IF EXISTS  anagrafica_impianti;CREATE TABLE IF NOT EXISTS anagrafica_impianti AS
                select DISTINCT ON (TWATER.page)  TWATER.plant as impianto,TWATER.page as pagina,
                COALESCE(TWATER.watermapname,TGAS.gasmapname ) as descrizione,
                COALESCE(TWATER.LATITUDINE,CAST(TGAS.LATITUDINE as VARCHAR)) ||','||
                COALESCE(TWATER.LONGITUDINE,CAST(TGAS.LONGITUDINE as VARCHAR)) as coordinate,
                '1970-01-01 00:00:00' as modified_at
                from ({qmapw}) as TWATER 
                JOIN ({qmapg}) as TGAS ON TWATER.page = TGAS.page;
                
            """ # ALTER TABLE anagrafica_impianti ADD COLUMN modified_at TIMESTAMP DEFAULT '1970-01-01 00:00:00'
        conn.sql(query)
        query = """
            UPDATE anagrafica_impianti
            SET
                impianto = anagrafica_web.impianto,
                pagina = anagrafica_web.pagina,
                descrizione = anagrafica_web.descrizione,
                coordinate = anagrafica_web.coordinate,
                modified_at =  anagrafica_web.modificato
            FROM anagrafica_web
            WHERE anagrafica_impianti.pagina = anagrafica_web.pagina;
            """
        conn.sql(query)

        data = conn.sql('select distinct * from anagrafica_impianti').show()
        data = conn.sql('select distinct * from anagrafica_impianti').fetchall()
        for e in data:
            row = dict()
            """
            anagrafica_data = anagrafica_impianti.get(e[1],{})
            
            if len(anagrafica_data) > 0:
                row['impianto'] = anagrafica_data['impianto'] 
                row['pagina'] = anagrafica_data['pagina']
                row['descrizione'] = anagrafica_data['descrizione']
                row['coordinate'] = anagrafica_data['coordinate']
            else:
            """
            row['impianto'] = e[0]
            row['pagina'] = e[1]
            row['descrizione'] = e[2]
            row['coordinate'] = e[3]
            if e[4] == '1970-01-01 00:00:00':
                row['modificato'] = ''
            else:
                row['modificato'] = e[4]
                #if e[3] and e[4] :
                #    row['coordinate'] = [e[3],e[4]]
            result.append(row)

    return result       

async def gis_map_old(appstate):
    path = f'iset/isetmap.json'
    
    try:
        read = appstate.fs.info(path) 
    except:
        isetmap = await get_map(appstate)
        with appstate.fs.open(path, 'w') as file:
            file.write(json.dumps(isetmap))

    paths = ['gis.anagrafica.gas','gis.anagrafica.water']
    for path in paths:
        try:            
            read = appstate.fs.info(f"gis/{path}")            
        except:
            try:
                rawpayload = await appstate.nc.get(f'iset.{path}')          
                with appstate.fs.open(f"gis/{path}.parquet", 'wb') as file:
                    file.write(rawpayload.value)
            except:
                pass 
    with duckdb.connect('webscada.db') as conn:
        conn.register_filesystem(appstate.fs)  
        #global iset map
        path = f'iset/isetmap.json'
        conn.execute(f"CREATE TABLE IF NOT EXISTS ISETMAP AS SELECT tag, nome, numero, impianto, motore, tipo FROM read_json_auto('memory://{path}')")

        #water/wastewater map through flatened excel from Hydro
        path = 'gis/gis.anagrafica.water.parquet'
        query = f"""  CREATE TABLE IF NOT EXISTS WATER AS
                      SELECT "CODICE TLC" as ID_MISURA,        
                      "DESCRIZIONE IMPIANTO" as ID_PERIFERICA,
                      1 as ID_TIPOLOGIA_MISURA,
                      IMPUTAZIONE as COMMODITY,
                      POD as 'IDENTIFICATIVO_OGGETTO_MAPPA',
                      split_part(COORDINATE, ',', 1) AS LATITUDINE,
                      split_part(COORDINATE, ',', 2) AS LONGITUDINE,
                      "CODICE TLC" || '🔷' || "DESCRIZIONE IMPIANTO" || '🔷' || UBICAZIONE as NAME,
                      NULL as TAG,
                      NULL AS VALUE,
                      FROM read_parquet('memory://{path}') WHERE "CODICE TLC" != '#N/A'
                """
        conn.execute(query)

        query = """ select WATER.*,ISETMAP.nome,ISETMAP.tag FROM WATER LEFT JOIN ISETMAP 
        ON WATER.ID_PERIFERICA LIKE ISETMAP.nome || '%'
        """

        #page result
        query = "select * from WATER"
        #conn.query(query).show()   
    
        waterdata = conn.query(query).fetchall()   
        #gas mapped trough SIR platform
        path = 'gis/gis.anagrafica.gas.parquet'       

        query = f"""SELECT T1.*,ISETMAP.nome,ISETMAP.tag,
            T1.ID_PERIFERICA || '🔶' || ISETMAP.nome || '🔶' || T1.IDENTIFICATIVO_OGGETTO_MAPPA as FINALNAME,
            FROM (SELECT  REPLACE(ID_MISURA, '.UI', '') as ID_MISURA, ID_PERIFERICA,ID_TIPOLOGIA_MISURA,COMMODITY, IDENTIFICATIVO_OGGETTO_MAPPA, LATITUDINE,LONGITUDINE
                from read_parquet('memory://{path}')) as T1 INNER JOIN ISETMAP ON ISETMAP.nome LIKE  T1.ID_MISURA  || '%' """  
        
        #conn.query(query).show()    
        gasdata = conn.query(query).fetchall()

        #final join-----------------------------

        # write GASMAP read from SIR table to the local db
        q = f"""  CREATE TABLE IF NOT EXISTS GAS AS SELECT  REPLACE(ID_MISURA, '.UI', '') as ID_MISURA, ID_PERIFERICA,ID_TIPOLOGIA_MISURA,COMMODITY, IDENTIFICATIVO_OGGETTO_MAPPA, LATITUDINE,LONGITUDINE
                from read_parquet('memory://{path}') """        
        conn.query(q)               

        # write CROSSREF table to the local db
        q = f"""
            CREATE TABLE IF NOT EXISTS CROSSREF AS SELECT * FROM read_parquet('memory://iset.last.crossreference.parquet')"""
        conn.query(q)               
        


    result = list()
    for row in waterdata:
        line = dict()
        line['ID_MISURA'] = row[0]
        line['ID_PERIFERICA'] = row[1]
        line['ID_TIPOLOGIA_MISURA'] = row[2]
        line['COMMODITY'] = row[3]
        line['IDENTIFICATIVO_OGGETTO_MAPPA'] = row[4]
        line['LATITUDINE'] = row[5]
        line['LONGITUDINE'] = row[6]
        line['NAME'] = row[7]
        line['TAG'] = row[8]
        line['VALUE'] = ''
        result.append(line)

    for row in gasdata:
        line = dict()
        line['ID_MISURA'] = row[0]
        line['ID_PERIFERICA'] = row[1]
        line['ID_TIPOLOGIA_MISURA'] = row[2]
        line['COMMODITY'] = row[3]
        line['IDENTIFICATIVO_OGGETTO_MAPPA'] = row[4]
        line['LATITUDINE'] = row[5]
        line['LONGITUDINE'] = row[6]
        line['NAME'] = row[9]
        line['TAG'] = row[8]
        line['VALUE'] = ''

        result.append(line)
    return result     

async def gis_map(appstate,grant):
    result = list()
    isadmin = grant.get('admin',False)
    user_grant = None
    for access in grant['user']['profile']['DR']['group']:
        if access['name'] == 'LAY':
            user_grant = access['right']    
    
    try:
        with duckdb.connect('webscada.db',read_only=True) as conn:
            conn.register_filesystem(appstate.fs)
            if isadmin:
                q1 = "select * from crossref"
            else:
                q1 = f"""select * from crossref where ("grant" & {user_grant}) > 0"""
            q2 = """ select * from anagrafica_impianti where coordinate is not null""" 
            query = f""" select DISTINCT ON (T2.descrizione) T2.* from ({q1}) T1 inner join ({q2}) T2 on T1.page = T2.pagina """
            conn.query(query).show()    
            map = conn.query(query).fetchall()
            
        def extract_coordinate(coordinate):
            cleaned_string = coordinate.replace("[", "").replace("]", "").replace("(", "").replace(")", "")
            coordinate_pairs = cleaned_string.split(",")
            coordinates = []
            try:
                for i in range(0, len(coordinate_pairs), 2):
                    latitude = float(coordinate_pairs[i])
                    longitude = float(coordinate_pairs[i+1])
                    coordinates.append((latitude, longitude))
            except:
                pass
            return coordinates
                
        for e in map:
            coordinates = extract_coordinate(e[3])
            for lat_long in coordinates:
                row = dict()
                row['PAGE'] = e[1]
                temp_page = row['PAGE'].split('-')[0].strip()
                if len(temp_page) <= 3:
                    temp_page = f"0{temp_page}"            
                row['INDEX'] = f"page.{temp_page}.config"
                row['NAME'] = e[2]
                row['allarm'] = False
                if '🔷' in row['NAME']:
                    row['COMMODITY'] = 'Acqua'
                elif '🔶' in row['NAME']:
                    row['COMMODITY'] = 'GAS'
                elif '🟢' in row['NAME']:
                    row['COMMODITY'] = 'Fognatura'
                elif '🟤' in row['NAME']:
                    row['COMMODITY'] = 'Igiene Urbana'
                elif '♦️' in row['NAME']:
                    row['COMMODITY'] = 'Produzione'
                elif '🔺' in row['NAME']:
                    row['COMMODITY'] = 'cogenerazione'
                else:
                    row['COMMODITY'] = 'Altro'

                if 'AIR' in row['NAME']:
                    row['COMMODITY'] += ' AIR'

                row['LATITUDINE'] = lat_long[0]
                row['LONGITUDINE'] = lat_long[1]
                
                result.append(row)

    except Exception as e:
        logger.error(e)
    
    return result

async def delete_tags(appstate, data):
    """Delete selected tags from the system"""
    try:
        logger.info(f"Deleting tags: {data}")
        
        # Extract selected tags
        selected_tags = data.get('selectedTags', '[]')
        
        if not selected_tags:
            return JSONResponse(
                status_code=400,
                content={"message": "Non ci sono tag selezionati"}
            )
        
        if len(selected_tags) > 10:
            return JSONResponse(
                status_code=400,
                content={"message": "Non puoi eliminare più di 10 tag contemporaneamente"}
            )
        
        # Process each tag for deletion
        deleted_tags = []
        failed_tags = []
        
        for tag in selected_tags:
            try:
                # Convert tag to topic format
                topic = tag_to_topic(tag)
                
                if not topic:
                    failed_tags.append(tag)
                    continue
                
                # Delete from NATS KV store
                key = f"iset.config.{topic}"
                try:
                    pass#await appstate.nc.delete(key)
                except Exception as e:
                    logger.error(f"Failed to delete tag {tag} from NATS KV: {str(e)}")
                
                # Delete from in-memory dictionary
                if key in appstate.iset:
                    del appstate.iset[key]
                
                # Also delete last value if it exists
                last_key = f"iset.last.{topic}"
                try:
                    pass#await appstate.nc.delete(last_key)
                except Exception:
                    pass
                
                if last_key in appstate.iset:
                    del appstate.iset[last_key]
                
                deleted_tags.append(tag)
                logger.info(f"Tag {tag} sono state eliminati")
                
            except Exception as e:
                logger.error(f"Error deleting tag {tag}: {str(e)}")
                failed_tags.append(tag)
        
        # Prepare response message
        if len(deleted_tags) == len(selected_tags):
            message = f"Tutte le eliminazioni sono state eseguite con successo. Eliminati {len(deleted_tags)} tag(s)"
        elif len(deleted_tags) > 0:
            message = f"Eliminati {len(deleted_tags)} tag(s), fallita eliminazione di {len(failed_tags)} tag(s)"
        else:
            message = f"Errore eliminazione tag"
        
        return JSONResponse(
            content={
                "message": message,
                "deleted": deleted_tags,
                "failed": failed_tags
            }
        )
        
    except json.JSONDecodeError:
        return JSONResponse(
            status_code=400,
            content={"message": "Invalid JSON data"}
        )
    except Exception as e:
        logger.error(f"Error in delete_tags: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"Server error: {str(e)}"}
        )

async def gis_alarms(appstate,grant):
    lastalarm = None
    alarmcount = 0
    isadmin = grant.get('admin',False)   
    user_grant = None
    for access in grant['user']['profile']['DR']['group']:
        if access['name'] == 'LAY':
            user_grant = access['right']    
    try:
        logger.info("Gis Rt alarms connected")     
        await asyncio.sleep(3)   
        while True:

            today = datetime.now()
            pastdays = today + timedelta(days=-15)
            date_3days = today + timedelta(days=-3)
            date_3days_clause = date_3days.strftime("%Y-%m-%d %H:%M:%S")
            key = f"iset.alarms.{today.year}.{today.month}"
            try:
                rawalarms= await appstate.alarmskv.get(key)

                with appstate.fs.open(f'alarms/iset/year={today.year}/month={today.month}/{key}.parquet', 'wb') as file:
                    file.write(rawalarms.value)
            except:
                pass   
            dateclause = f"year = {today.year} AND month = {today.month}"
            if pastdays.year != today.year or pastdays.month != today.month:
                key = f"iset.alarms.{pastdays.year}.{pastdays.month}"
                try:
                    rawalarms= await appstate.alarmskv.get(key)
                    with appstate.fs.open(f'alarms/iset/year={pastdays.year}/month={pastdays.month}/{key}.parquet', 'wb') as file:
                        file.write(rawalarms.value) 
                except:
                    pass
                dateclause = f"((year = {today.year} AND month = {today.month}) OR (year = {pastdays.year} AND month = {pastdays.month}))"
            
            result = []
            with duckdb.connect('webscada.db') as conn:
                # Create a memory filesystem and write the parquet data to it           

                # Register the memory filesystem and create the table
                conn.register_filesystem(appstate.fs)
                if isadmin:
                    q1 = "select * from crossref"
                else:
                    q1 = f"""select * from crossref where ("grant" & {user_grant}) > 0"""
                q2 = """ select * from anagrafica_impianti where coordinate is not null""" 
                q3 = f""" select  T2.descrizione,T1.tag from ({q1}) T1 inner join ({q2}) T2 on T1.page = T2.pagina """

                query = f"""SELECT DISTINCT ON (TA2.descrizione) TA2.descrizione FROM (SELECT timestamp,livello, tag, messaggio, periferica, address, record_number FROM read_parquet('memory://alarms/iset/**/*.parquet', union_by_name = true) 
                        where ack_timestamp = '0001-01-01 00:00:00' and tipo_allarme >= 0 and ackid = 0 and timestamp > '{date_3days_clause}' 
                        and {dateclause}
                        and not (address like '%IS150%' and tag like '%Comunicazione%')) as TA1 INNER JOIN ({q3}) TA2 ON TA1.address = TA2.tag ORDER BY timestamp DESC"""  

                #query = f""" select T2.descrizione,T1.index,T1.tag from ({q1}) T1 inner join ({q2}) T2 on T1.page = T2.pagina """
                #print(query)       
                data = conn.query(query).fetchmany(500)   
                for col in data:      
                    row = {}
                    row['name'] = col[0]
                    result.append(row['name'])                 
            
            if len(result) > 0:    
                if  len(result) != alarmcount: #lastalarm != result[0] or
                    alarmcount = len(result)
                    lastalarm = result[0]
                    yield f"data:{json.dumps(result)}\n\n"
                         
            await asyncio.sleep(5) 
    finally:
        logger.info("Gis Rt alarms diconnected")  

    #appstate.iset['alarmgroup'] = await appstate.isetkv.get('iset.alarmgroup')

async def gis_plant(coordinate):
    with duckdb.connect('webscada.db') as conn: 
        data = conn.sql(f"select trim(split_part(pagina, '-', 1)) from anagrafica_impianti where coordinate = '{coordinate}'").fetchone()
        page = data[0]
        page= f"page.{page.split('-')[0]}.config"
        print(page)
        return page

async def gis_realtime(appstate, websocket):
    await websocket.accept()
    old_values = dict()
    realtime_values = dict()
    try:
        while True:
            try:
                data = await asyncio.wait_for(websocket.receive_json(), timeout=1.0)   
                print(data)        
                add = data.get('add',True)
                tags = data['tags']
                if not add:
                    for tag in tags:
                        key = f"iset.last.{tag_to_topic(tag)}"                     
                        if key in realtime_values:
                            try:
                                del realtime_values[key]
                                del old_values[key]
                            except:
                                pass
                else:      
                    result = list()
                    CH_MODE = {"0":"Fuori Scansione", "1":"In Scansione","2": "Scansione e limiti","3": "Trasmiss. evento",     
                                "4":"Allarme", "5":"Allarme inferiore","6":"Allarme superiore","7":"Non Valido","8":"Canale esportato",
                                "17":"Importato Modo 1", "18":"Importato Modo 2","19":"Importato Modo 3",
                                "20":"Importato Modo 4","21":"Importato Modo 5","22":"Importato Modo 6"}
                    
                    CH_TYPE = { "0":"0-10 volt","1":"0-2.5 volt","2":"4-20 mA","3":"0-20 mA","4":"4-24 mA","5":"0-24 mA",
                            "6":"Linearizzazione","7":"Parametri da formula","8":"Non condizionato"}
                    
                    for tag in tags:
                        try:
                            key = f"iset.config.{tag_to_topic(tag)}"
                            item = appstate.iset.get(key,'')
                            if len(item)>0 :
                                row  = dict()

                                row["tag"] = item['Tag']
                                row["nome"] = item['Nome']                  
                                row["impianto"] = item['Plant']      
                                row["umisura"] = item["UMisura"] 
                                try:
                                    last = appstate.iset[key.replace('config','last')] 
                                    if math.isnan(last["value"]):                        
                                        row["value"] = f'0 {row["umisura"]}'        
                                    else: 
                                        row["value"] = f'{last["value"]} {row["umisura"]}'
                                    row["timestamp"] = datetime.fromtimestamp(last['ts']).strftime('%Y-%m-%d %H:%M:%S')                                           
                                except Exception as e:
                                    row["value"] =  None
                                    row["timestamp"] = datetime.fromtimestamp(0).strftime('%Y-%m-%d %H:%M:%S')    
                                #result.append(row)
                                await websocket.send_json(row)
                                realtime_values[key.replace('config','last')] = {'tag':row['tag'],'value':row["value"]}
                        except:
                            pass
                    """
                    if len(result) > 0 :
                        await websocket.send_json(result)
                    """
            except Exception as e:
                pass

            if len(realtime_values) > 0:
                for key,item in realtime_values.items():

                    row = dict()
                    try:
                        last = appstate.iset[key] 
                        if math.isnan(last["value"]):                        
                            row["value"] = 0            
                        else: 
                            row["value"] = last["value"]      
                        row["timestamp"] = datetime.fromtimestamp(last['ts']).strftime('%Y-%m-%d %H:%M:%S')                                           
                    except Exception as e:
                        row["value"] =  None
                        row["timestamp"] = datetime.fromtimestamp(0).strftime('%Y-%m-%d %H:%M:%S') 
                    
                    row['tag'] = item['tag']                  

                    if key not in old_values:
                        old_values[key] = {}

                    if row["value"] != old_values[key].get("value",None):
                        await websocket.send_json(row)
                        old_values[key] = row

    except WebSocketDisconnect:
        logger.info("Client disconnected")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await websocket.close() 
