{% extends 'base.html' %}
{% block header %}
<title>Webscada Novareti</title>
<style>
    .htmx-indicator{
        opacity:0;
        transition: opacity 500ms ease-in;
    }
    .htmx-request .htmx-indicator{
        opacity:1;
    }
    .htmx-request.htmx-indicator{
        opacity:1;
    }
</style>

{% endblock header %}
{% block content %}
{% include 'common/nav.html' %}  
{% include 'globalchart.html' %}

<div id="spinner" class="htmx-indicator justify-center items-center text-center z-50 fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white p-4 shadow-lg">
    <img  class="self-center justify-center" src="/webscada/static/img/dotspinner.svg"/>
</div>
{% if lasturl %}
<div id='panel' hx-get="/webscada/{{lasturl}}" hx-trigger="load" hx-ext="sse-cleanup" class="pt-16"></div>
{% else %}
<div id='panel' hx-get="/webscada/helpdesk" hx-trigger="load" hx-ext="sse-cleanup" class="pt-16"></div>
{% endif %}
{% include 'common/footer.html' %}
<script>

    var datepicker = document.getElementById('datepicker-form');
    var downloadGrid = document.getElementById('download-grid');    
    var dropdownRadioButton = document.getElementById('dropdownRadioButton');
    var ackButton = document.getElementById('ack-button');
    var resetFilters = document.getElementById('reset-filters');    
    //var search = document.getElementById('searchbox');

    // back navigation
    window.history.pushState({ customUrl: '/' }, '', window.location.href);

    window.addEventListener('popstate', function(event) {

    if (event.state && event.state.customUrl) {
        // Prevent default back navigation
        event.preventDefault();

        // Navigate to your custom URL
        window.location.href = event.state.customUrl;  // Or use a routing library
    } else {
        // Handle normal back navigation (if needed)
        // For example, if there's no customUrl in the state, do nothing
    }
    });    
    htmx.on('htmx:sseError', (e)=> {
              console.warn(e);
              window.location.href = "/webscada";
          });         
    htmx.onLoad(function(content) {
            initFlowbite();
        });
    //************* realtime sse events for pages
    htmx.on('htmx:sseMessage', (msg)=> {   
            if (msg.detail.target.url.includes("/gis_alarms")){
                sseScadagis(msg);      
                return
            }
            //sse for the rest of realtime pages
            const result = JSON.parse(msg.detail.data);           
            for (var tag in result) {
                    try{
                    const element = document.getElementById(tag);
                    if (element){
                        element.innerHTML = result[tag].value;
                        element.style.color = result[tag].color;
                    }

                    //state 
                    const state = document.getElementById('state-'+tag);
                    if(state){
                        state.innerHTML = result[tag].stato;
                        state.style.color = result[tag].color;     
                    }
                    
                    //ts 
                    const ts = document.getElementById('ts-'+tag);
                    if (ts){
                        ts.innerHTML = result[tag].ts;
                    }
                    }catch{}

            }
        })
    //************** event listner for json response 
    htmx.on('htmx:afterRequest', (msg)=> {    
            //update chartsindex for the UI
            if (msg.detail.pathInfo.responsePath.includes("/addchart")){    
                chartIndex = getQueryParameters(msg.detail.pathInfo.responsePath)           
            }

            if (msg.detail.pathInfo.responsePath.includes("/chart") | msg.detail.pathInfo.responsePath.includes("/hist/")){ 
                    const index = getQueryParameters(msg.detail.pathInfo.responsePath) - 1;
                    const result = JSON.parse(msg.detail.xhr.response);                 
                    var data = []

                    if (!seriesData.hasOwnProperty(index)) {
                        seriesData[index] = {};
                        dataTypes[index] = {};
                    }
                    
                    for (let e in result.tags) {
                        seriesData[index][e] = result.tags[e];
                        dataTypes[index][e] = result.type[e];
                    } 

                    for(let key in seriesData[index]){
                        let keyName = document.getElementsByName(key)[0];
                        let dataType = dataTypes[index][key];
                        
                        if(dataType == 'DIN' | dataType == 'DOUT')
                            data.push({tag:key ,name: keyName.innerText ,dataType: dataType, data: seriesData[index][key], type: 'line',sampling: 'lttb',smooth: true,symbol: 'none',step: 'start'/*, areaStyle: {}*/})
                        else
                            data.push({tag:key ,name: keyName.innerText ,dataType: dataType, data: seriesData[index][key], type: 'line',sampling: 'lttb',smooth: true,symbol: 'none' /*,areaStyle: {}*/}) //, "yAxisIndex": 1
                        
                        
                    }     

                    myChart = chartInit('chart-timeline-'+index);
                    /*myChart.on('dataZoom', function(params) {
                        console.log(params); // Log the parameters of the dataZoom event
                        //alert('DataZoom event triggered: ' + JSON.stringify(params));
                    });*/
                    myChart.setOption({
                        series: data
                        })
                    
                    //check if multiple axis are enabled
                    let toggleYaxis = document.getElementById('toggle-yaxis-'+index)
                    if (toggleYaxis.checked)
                        toggleYAxis(toggleYaxis,index)
                    /*const localChart = digital_chart('chart-timeline-'+index,data);                  
                    var option = localChart.getOption();
                    console.log(option)*/
                    /*option.series[1] = data
                    barChart.setOption(option,true)*/                        
                    // Grid handeling
                    gridHandle()
               } 
            })      

    document.addEventListener('htmx:beforeSwap', function(event) {
      if (event.detail.pathInfo.finalRequestPath.includes("/gispage")) {
        //Gismap Call cleanup when the page is about to be swapped by HTMX
        try{
            cleanupMapResources();
        }catch{
            ;
        }
        
      }
    });   

    //Close radiomap eventsource
    function closeEventSource() {
    try{       
        if (evtSource && evtSource.readyState !== EventSource.CLOSED) {
        console.log('Closing EventSource connection');
        evtSource.close();
        return true;
        }
    } catch{
        return false;    
    } 

    }
    htmx.on('htmx:beforeSwap', function(event) {
        if (event.detail.target.id === 'panel') {
            closeEventSource() ;
        }
    });
    
    // Add this to handle navigation between pages
    htmx.on('htmx:beforeRequest', function(event) {
        if (event.detail.target.id === 'panel') {
            closeEventSource() ;
      
            if(!event.detail.pathInfo.finalRequestPath.includes("/scadabi/page")){
                try{
                    if (window._db) {
                        window._db.terminate()
                        window._db = null;
                        console.log("In-memory DuckDB instance terminated.");
                    }
                }catch{
                    ;
                }
            }            
        }
    });  
</script>
{% endblock content %}
