<div class="flex-1 p-3 ">
    <label for="input-group-search" class="sr-only">Search</label>
    <div class="relative">
      <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
        </svg>
      </div>   
      <input data-dropdown-toggle="dropdownSearch" data-dropdown-placement="bottom" type="text" id="input-group-search" oninput="group_search()"  class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Trova pagina">
  </div>
  </div>   
    <!-- Dropdown menu -->
    <div id="dropdownSearch" class="absolute hidden bg-white rounded-lg shadow w-60 dark:bg-gray-700">
        <ul id="input-group-ul" class="h-96 px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownSearchButton">
            {% for page in items %}
             <li>
                <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                <label for={{page.id}} hx-get=/webscada/page/{{page.id}} data-drawer-hide="drawer-navigation" {{htmxtarget}} class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">{{page.name}}</label>
                </div>
            </li>
            {% endfor %}
        </ul>
    </div>
 
    <script>
        function group_search(){
            var input, filter, ul, li, a, i, txtValue;
            input = document.getElementById('input-group-search');
            filter = input.value.toUpperCase();
            ul = document.getElementById("input-group-ul");
            li = ul.getElementsByTagName('li');
            for (i = 0; i < li.length; i++) {
                label = li[i].getElementsByTagName("label")[0];
                txtValue = label.textContent || label.innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    li[i].style.display = "";
                } else {
                    li[i].style.display = "none";
                }
            }
        }
    </script>
