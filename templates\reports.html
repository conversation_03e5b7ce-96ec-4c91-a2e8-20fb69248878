<!--
<style>
    .htmx-indicator{
        opacity:0;
        transition: opacity 500ms ease-in;
    }
    .htmx-request .htmx-indicator{
        opacity:1;
        display: inline-block;
    }
    .htmx-request.htmx-indicator{
        opacity:1;
        display: inline-block;
    }
    .hide-text {
      font-size: 0; /* This will effectively hide the text */
    }
</style>
<button hx-get="/webscada/sendreport/3" hx-trigger="click add .hide-text" indicator="#my-spinner" class="pt-16" > PREEEEEEEEEEEEESSSSSSSSSSSSS
  <img  id="my-spinner" class="htmx-indicator"  src="static/img/3-dots-rotate.svg"/>
</button>
-->
<div id="reportGrid" hx-get=/webscada/reportvalues hx-swap="none" hx-trigger="load" class="ag-theme-quartz pb-4" style="height:85vh;">
</div>  
<style>
.ag-header-cell-label {
  justify-content: center;
}
.ag-cell {
  justify-content: center;
}
.level-0 {
  text-align: center;
  font-weight: bold;
  background-color:#14ce71;
}
.level-1 {
  text-align: center;
  font-weight: bold;
  background-color: #a696ee;
}
.level-2 {
  text-align: center;
  font-weight: bold;
  background-color: #00b0f5;
}
.level-3 {
  font-weight: bold;
  background-color: #f436b5;
}
.level-4 {
  font-weight: bold;
  background-color: #91e7ce;
}
</style>
<script>
    //all pages series will be contained here 
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    ackButton.classList.add('hidden');

    var gridElement = document.querySelector('#reportGrid');
    
    //grid init method
    function gridInit() {
      
      document.addEventListener('dark-mode', function (e) {
        if (gridElement.classList.contains('ag-theme-quartz-dark')) {
          // If dark theme is applied, switch to light theme
          gridElement.classList.remove('ag-theme-quartz-dark');
          gridElement.classList.add('ag-theme-quartz');
        } else {
          // If light theme is applied, switch to dark theme
          gridElement.classList.remove('ag-theme-quartz');
          gridElement.classList.add('ag-theme-quartz-dark');
        }
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        gridElement.classList.remove('ag-theme-quartz');
        gridElement.classList.add('ag-theme-quartz-dark');
      } else {
        gridElement.classList.remove('ag-theme-quartz-dark');
        gridElement.classList.add('ag-theme-quartz');
      }
      var localeText = JSON.parse(localStorage.getItem('grid-it'));

      var gridOptions = {
        // Row Data: The data to be displayed.
        rowData: [],
        // Column Definitions: Defines the columns to be displayed.
        columnDefs: [],
        defaultColDef: {
          flex: 1,          
        },
        localeText: localeText,
        suppressHorizontalScroll: false,
        rowSelection: "multiple"/*,
        rowClassRules: {
          "level-0": params => params.api.getValue("tipo", params.node) == 'AIN',
          "level-1": params => params.api.getValue("tipo", params.node) == 'AOUT',
          "level-2": params => params.api.getValue("tipo", params.node) == 'DIN',
          "level-3": params => params.api.getValue("tipo", params.node) == 'DOUT',
          "level-4": params => params.api.getValue("tipo", params.node) == 'SP'
        }*/
      };
      

      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      }

      class CustomButtonComponent {
          eGui;
          eButton;
          eventListener;

          init() {
              let eButton = document.createElement('button');
              eButton.className = 'btn-simple';
              eButton.innerText = 'Launch!';
              this.eventListener = () => alert('Software Launched');
              eButton.addEventListener('click', this.eventListener);
          }

          getGui() {
              return this.eButton;
          }

          refresh() {
              return true;
          }

          destroy() {
              if (this.eButton) {
                  this.eButton.removeEventListener('click', this.eventListener);
              }
          }
      }

      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      var cols = [                
                { field: "id" , filter: true,maxWidth:100, cellStyle: { textAlign: 'center' }},
                { field: "nome" ,  filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "cron" , filter: true,maxWidth:120, cellStyle: { textAlign: 'center' }},
                { field: "destinatario" , filter: true, minWidth:200,autoHeight: true, cellStyle: { textAlign: 'center' }},
                { field: "abilitato" , filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "test" , filter: true, maxWidth:200, cellStyle: { textAlign: 'center' }},
                { field:  "azioni", cellStyle: { textAlign: 'center' },//cellRenderer: CustomButtonComponent
                                
                cellRenderer: params => {    
                    const eBtnx = document.createElement('button');
                    eBtnx.setAttribute('hx-get', `/webscada/sendreport/${params.value}`);
                    eBtnx.setAttribute('hx-swap', "outerHTML");
                    eBtnx.setAttribute('class', "px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800");
                    eBtnx.innerText = 'invia'    
                                            
                  // Add an event listener for the button                  
                   /* eBtnx.addEventListener('click', function(event) {
                                        url =  `/webscada/sendreport/${params.value}`
                                          // execute some code after the content has been inserted into the DOM
                                          htmx.ajax('GET', url , 'none').then(() => {
                                            // this code will be executed after the 'htmx:afterOnLoad' event,
                                            // and before the 'htmx:xhr:loadend' event
                                            console.log('Content',url);
                                          });
                        });*/
                    //let btn = `<button hx-get=/webscada/sendreport/${params.value} hx-swap="none" hx-trigger="click" class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">invia</button>`
                    htmx.process(eBtnx);
                    return eBtnx;
                  }
                }
             ]
      var gridApi = agGrid.createGrid(gridElement, gridOptions);
      // Transform the object to row data          
      //let gridRowData = transformToRowData(seriesData)
      gridApi.setGridOption("columnDefs", cols);
      return gridApi;
    }    
    var gridApi = gridInit()    
    function onBtnExport() {
      gridApi.exportDataAsCsv();
    }
    
    htmx.on('htmx:afterRequest', (msg)=> {     
      if  (msg.detail.pathInfo.responsePath.includes("/reportvalues")){  
        const result = JSON.parse(msg.detail.xhr.response);        
        let seriesData = []
        for (let e in result) {
          seriesData[e] = result[e]
        }
        // Grid handeling
        gridApi.setGridOption("rowData", seriesData);       
      } 
    }); 

    gridElement.addEventListener('htmx:beforeRequest', function(event) {
      event.target.textContent = 'In corso...';
    });
</script>