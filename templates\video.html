
{% if feed %}  
    <img id="cctv-feed" class="h-auto max-w-full focus:border focus:border-red-600 rounded-lg" src="cctvthumbnail/{{feed}}" >
{% else  %} 
<style>
.hover-zoom:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}
</style>
{% include 'cctv.html' %}
{% for key, item in data.items() %}    
    <button  class="block w-full p-2 ps-10 text-sm text-blue-50 border border-blue-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-blue-600 dark:border-blue-500 dark:placeholder-blue-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
        {{key}}
    </button>
    <div class="p-4 grid grid-cols-2 gap-2">
        {% for camera in item %}
            <!--
            <img  data-modal-target="global-cctv-modal" data-modal-toggle="global-cctv-modal"  
            hx-get="/webscada/cctv/{{camera.id}}" hx-swap="outerHTML" hx-target="#cctv-feed"
            class="h-auto max-w-full hover:border border-red-600 rounded-lg hover-zoom" src="cctvthumbnail/{{camera.id}}" alt="{{key}}-{{camera.name}}">
            -->
            <a href="maskedfeed/{{camera.id}}" target="_blank">   
                <img class="h-auto max-w-full hover:border border-red-600 rounded-lg hover-zoom" src="cctvthumbnail/{{camera.id}}" alt="{{key}}-{{camera.name}}">
                </a> 
        {% endfor %}
    </div>
{% endfor %}    
{% endif %}