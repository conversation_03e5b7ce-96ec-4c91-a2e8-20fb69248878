/**
 * SSE Cleanup Extension for HTMX
 * 
 * This extension ensures that SSE connections are properly closed when elements
 * are removed from the DOM or when content is swapped.
 */
(function() {
  // Store a reference to the internal API
  let internalApi;
  
  // Register the extension with HTMX
  htmx.defineExtension('sse-cleanup', {
    init: function(api) {
      // Store the internal API reference
      internalApi = api;
    },
    
    onEvent: function(name, evt) {
      // Handle events that might require SSE cleanup
      if (name === 'htmx:beforeSwap') {
        // Get the target element that will be swapped
        const target = evt.detail.target;
        
        // Find all SSE-connected elements within the target
        cleanupSSEConnections(target);
      }
      
      // Also handle element removal
      if (name === 'htmx:beforeCleanupElement') {
        const element = evt.detail.elts ? evt.detail.elts[0] : evt.target;
        cleanupSSEConnections(element);
      }
    }
  });
  
  /**
   * Find and cleanup all SSE connections within an element
   * @param {HTMLElement} element - The container element to search within
   */
  function cleanupSSEConnections(element) {
    if (!element) return;
    
    try {
      // Find all elements with sse-connect attribute
      const sseElements = element.querySelectorAll('[sse-connect]');
      const sseArray = Array.from(sseElements);
      
      // Also check if the element itself has an SSE connection
      if (element.hasAttribute && element.hasAttribute('sse-connect')) {
        sseArray.push(element);
      }
      
      // Close each SSE connection
      sseArray.forEach(function(el) {
        try {
          // Access the element's data using the correct method
          const internalData = el._htmx ? el._htmx : {};
          
          if (internalData && internalData.sseEventSource) {
            console.log('Closing SSE connection for:', el);
            internalData.sseEventSource.close();
            
            // Remove event listeners
            if (internalData.sseEventListener) {
              const sseSwapAttr = el.getAttribute('sse-swap');
              if (sseSwapAttr) {
                const eventNames = sseSwapAttr.split(',');
                eventNames.forEach(function(eventName) {
                  internalData.sseEventSource.removeEventListener(
                    eventName.trim(), 
                    internalData.sseEventListener
                  );
                });
              }
            }
            
            // Clear the internal data
            internalData.sseEventSource = null;
            internalData.sseEventListener = null;
          }
        } catch (err) {
          console.error('Error cleaning up SSE for element:', el, err);
        }
      });
    } catch (err) {
      console.error('Error in cleanupSSEConnections:', err);
    }
  }
  
  /**
   * Helper function to force cleanup of all SSE connections
   * Can be called manually if needed
   */
  window.cleanupAllSSEConnections = function() {
    try {
      // Find all SSE elements in the document
      const allSseElements = document.querySelectorAll('[sse-connect]');
      console.log('Found', allSseElements.length, 'SSE connections to clean up');
      
      // Clean up each element
      allSseElements.forEach(function(el) {
        try {
          // Access the element's data directly
          if (el._htmx && el._htmx.sseEventSource) {
            console.log('Closing SSE connection for:', el);
            el._htmx.sseEventSource.close();
            el._htmx.sseEventSource = null;
            el._htmx.sseEventListener = null;
          }
        } catch (err) {
          console.error('Error cleaning up SSE for element:', el, err);
        }
      });
    } catch (err) {
      console.error('Error in cleanupAllSSEConnections:', err);
    }
  };
  
  // Add a global event handler for page navigation
  window.addEventListener('beforeunload', function() {
    window.cleanupAllSSEConnections();
  });
})();
