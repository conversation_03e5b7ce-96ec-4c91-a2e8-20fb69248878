
<td  id="email-container-{{id}}" class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  {{color}}">
    {% if item == 'email' %}
        <div id='email-container-{{id}}' class="relative "></div>
            <input name="email"  type="email" class="rounded-lg bg-gray-50 border text-gray-900 focus:ring-blue-500 focus:border-blue-500  text-sm border-gray-300 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="<EMAIL>">
            <button hx-get="/webscada/save/save/{{id}}"   hx-include="[name='email']" hx-swap="outerHTML" hx-target="#email-container-{{id}}" type="button" class="text-white  bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Salva</button>
            <button hx-get="/webscada/save/cancel/{{id}}"  hx-include="[name='email']"  type="button" hx-target="#email-container-{{id}}" hx-swap="outerHTML" class="text-white  bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800">Annulla</button>
        </div>
    {% endif %}
    {% if item == 'save' %}
        {{email}}
        <button hx-get="/webscada/edit/email/{{id}}" hx-swap="outerHTML"  hx-target="#email-container-{{id}}" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">Modifica</button>
    {% endif %}
    {% if item == 'cancel' %}
        {{email}}
        <button hx-get="/webscada/edit/email/{{id}}" hx-swap="outerHTML" hx-target="#email-container-{{id}}"  type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">Modifica</button>
    {% endif %}

    {% if item == 'error' %}
    <label  class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">Error</label>
    {% endif %}
</td>
