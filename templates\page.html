<style>
    .chart-timeline {
      position: relative;
      height: 350px;
      overflow: hidden;
    }
</style>

{% if allarmi %}
  {% include 'alarmsgrid.html' %} 
{% elif archive %}
  {% include 'archivegrid.html' %}   
{% elif  svg %}    
    <div id="context-menu" class="z-10 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
        <div class="p-3 space-y-2">
            <p> Caricamento config ....</p>
            <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
        </div>
    </div>
    {{data|safe}}
    <script>
            datepicker.classList.add('hidden');   
            dropdownRadioButton.classList.add('hidden');
            downloadGrid.classList.add('hidden');     
            ackButton.classList.add('hidden');
    </script>
{% else %}

<div class="flex grid md:grid-cols-2 gap-4" >
    <div id='analytics-container' class="basis-1/2  shadow-xl">
        {% include 'analyticspanel.html' %}
    </div>
    <div class="basis-1/2  shadow-xl grid grid-rows-subgrid gap-4 md:grid-rows-2">
        <div id="charts-container"> 
            {% include 'gridchart.html'%}
        </div>   
    </div>
</div>
<script>
    htmx.on('htmx:sseMessage', (msg)=> {         
        const result = JSON.parse(msg.detail.data);           
        for (var tag in result) {
                try{
                const element = document.getElementById(tag);
                if (element){
                    element.innerHTML = result[tag].value;
                    element.style.color = result[tag].color;
                }

                //state 
                const state = document.getElementById('state-'+tag);
                if(state){
                    state.innerHTML = result[tag].stato;
                    state.style.color = result[tag].color;     
                }
                
                //ts 
                const ts = document.getElementById('ts-'+tag);
                if (ts){
                    ts.innerHTML = result[tag].ts;
                }
                }catch{}

        }
    })
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    ackButton.classList.add('hidden');
    function clamp(sender) {
        if (parseFloat(sender.value) > sender.max) {
            sender.value = sender.max;
        } else if (parseFloat(sender.value) < sender.min) {
            sender.value = sender.min;
        }
    }
</script>  
{% endif %}
