<div id='notes-container' class="fixed bottom-6 end-6 z-30 w-96 group" ></div>
<div class="flex"></div>
<button hx-get="/webscada/helpform/add" hx-swap="outerHTML" class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Nuovo Ticket</button>
</div>
<div id="helpGrid" hx-get=/webscada/helpdata hx-swap="none" hx-trigger="load, reload-event from:body" class="ag-theme-quartz pb-4 pt-3" style="height:80vh;">
</div>  
<style>
.ag-header-cell-label {
  justify-content: center;
}
.ag-cell {
  justify-content: center;
}
.level-0 {
  text-align: center;
  font-weight: bold;
  background-color:#ce1414;
}
.level-1 {
  text-align: center;
  font-weight: bold;
  background-color: #ff8102;
}
.level-2 {
  text-align: center;
  font-weight: bold;
  background-color: #888d00;
}
.level-3 {
  font-weight: bold;
  background-color: #07a40f;
}
.level-4 {
  font-weight: bold;
  background-color: #91e7ce;
}
.row-blue {
    background-color: #777777 !important;
}
</style>
<script>
    //all pages series will be contained here 
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    ackButton.classList.add('hidden');
    var notesContainer = document.getElementById('notes-container');
    notesContainer.classList.add('hidden');
    resetFilters.classList.remove('hidden');

    //grid init method
    function gridInit() {
      var gridElement = document.querySelector('#helpGrid');
      document.addEventListener('dark-mode', function (e) {
        if (gridElement.classList.contains('ag-theme-quartz-dark')) {
          // If dark theme is applied, switch to light theme
          gridElement.classList.remove('ag-theme-quartz-dark');
          gridElement.classList.add('ag-theme-quartz');
        } else {
          // If light theme is applied, switch to dark theme
          gridElement.classList.remove('ag-theme-quartz');
          gridElement.classList.add('ag-theme-quartz-dark');
        }
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        gridElement.classList.remove('ag-theme-quartz');
        gridElement.classList.add('ag-theme-quartz-dark');
      } else {
        gridElement.classList.remove('ag-theme-quartz-dark');
        gridElement.classList.add('ag-theme-quartz');
      }
      var localeText = JSON.parse(localStorage.getItem('grid-it'));

      var gridOptions = {
        // Row Data: The data to be displayed.
        rowData: [],
        suppressNoRowsOverlay : true,
        // Column Definitions: Defines the columns to be displayed.
        columnDefs: [],
        defaultColDef: {
          flex: 1,          
        },
        localeText: localeText,
        suppressHorizontalScroll: false,
        rowSelection: "multiple",
        rowClassRules: {
          "row-blue": params => params.data.owner == false,
        }
      };
      
      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      }
      
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      const cellClassRules = {
        "level-0": params => params.api.getValue("priorita", params.node) == 'Critica',
        "level-1": params => params.api.getValue("priorita", params.node) == 'Alta',
        "level-2": params => params.api.getValue("priorita", params.node) == 'Media',
        "level-3": params => params.api.getValue("priorita", params.node) == 'Bassa'
      };
      const svg = `<svg class="w-6 h-6 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.403 5H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-6.403a3.01 3.01 0 0 1-1.743-1.612l-3.025 3.025A3 3 0 1 1 9.99 9.768l3.025-3.025A3.01 3.01 0 0 1 11.403 5Z" clip-rule="evenodd"/>
                    <path fill-rule="evenodd" d="M13.232 4a1 1 0 0 1 1-1H20a1 1 0 0 1 1 1v5.768a1 1 0 1 1-2 0V6.414l-6.182 6.182a1 1 0 0 1-1.414-1.414L17.586 5h-3.354a1 1 0 0 1-1-1Z" clip-rule="evenodd"/>
                  </svg>`       
      var cols = [                
                { field: "id" , filter: true,maxWidth:100, cellStyle: { textAlign: 'center' },checkboxSelection: true,headerCheckboxSelection: true },
                { field: "oggetto" ,  filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "titolo" , filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "richiesta" ,  filter: true,minWidth:200, cellStyle: { textAlign: 'center'}, 
                editable: true,       
                cellEditor: 'agLargeTextCellEditor',
                  cellEditorPopup: true,
                  cellEditorParams: {
                      maxLength: 100
                  }},//{ field: "stato" , filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                  { field: "invisione" ,  headerName :'In visione',  filter: true,minWidth:100, cellStyle: { textAlign: 'center'}, 
                editable: true,       
                cellEditor: 'agLargeTextCellEditor',
                  cellEditorPopup: true,
                  cellEditorParams: {
                      maxLength: 100
                  }},                
                { field: "categoria" , headerName :'Unità', filter: true, minWidth:100, cellStyle: { textAlign: 'center' }},
                { field: "priorita" , headerName :'Priorità',cellClassRules: cellClassRules ,filter: true, minWidth:100, cellStyle: { textAlign: 'center' }},
                { field: "utente" , filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "tecnico" , filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "stato" , filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "creato",filterParams: {
                  comparator: (filterLocalDateAtMidnight, cellValue) => {
                          const cellDate = new Date(cellValue);
                          if (cellDate < filterLocalDateAtMidnight) {
                              return -1;
                          } else if (cellDate > filterLocalDateAtMidnight) {
                              return 1;
                          }
                          return 0;
                      },
                 filterOptions : ['greaterThan','lessThan','inRange'],maxNumConditions:1}, filter: true,minWidth:200, filter: 'agDateColumnFilter', cellStyle: { textAlign: 'center' }}, 
                { field: "aggiornato",filterParams: {
                  comparator: (filterLocalDateAtMidnight, cellValue) => {
                          const cellDate = new Date(cellValue);
                          if (cellDate < filterLocalDateAtMidnight) {
                              return -1;
                          } else if (cellDate > filterLocalDateAtMidnight) {
                              return 1;
                          }
                          return 0;
                      },
                  filterOptions : ['greaterThan','lessThan','inRange'],maxNumConditions:1}, filter: true,minWidth:200, filter: 'agDateColumnFilter', cellStyle: { textAlign: 'center' }},     
                { field: "note" , minWidth:100, cellStyle: { textAlign: 'center' },
                                              
                  cellRenderer: params => {    
                                      const eBtnx = document.createElement('button');
                                      eBtnx.setAttribute('hx-get', `/webscada/helpnote`);
                                      eBtnx.setAttribute('hx-swap', "scroll:bottom");
                                      eBtnx.setAttribute('hx-target', "#notes-container");
                                      eBtnx.setAttribute('hx-indicator', "#spinner");
                                      //eBtnx.setAttribute('hx-vals', `${params.data}`);
                                      //eBtnx.setAttribute('class', "px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800");
                                      eBtnx.innerHTML = svg;    
                                      eBtnx.addEventListener('click',() => {
                                          eBtnx.setAttribute('hx-vals', JSON.stringify({"query": {'id':params.data['id'],'user':params.data['utente'],'created':params.data['creato']}}));                                                
                                          showNotes(true);                                         
                                      });                                      
                                      htmx.process(eBtnx);
                                      return eBtnx;
                                    }
                    },                      

             ]
      var gridApi = agGrid.createGrid(gridElement, gridOptions);
      // Transform the object to row data          
      //let gridRowData = transformToRowData(seriesData)
      gridApi.setGridOption("columnDefs", cols);
      return gridApi;
    }    
    var gridApi = gridInit()    
    function onBtnExport() {
      gridApi.exportDataAsCsv();
    }
    function clearFilters() {
          gridApi.setFilterModel(null);
      }  
    htmx.on('htmx:afterRequest', (msg)=> {     
      if  (msg.detail.pathInfo.responsePath.includes("/helpdata")){  
        const result = JSON.parse(msg.detail.xhr.response);        
        let seriesData = []
        for (let e in result) {
          seriesData[e] = result[e]
        }
        
        const order = ["Creato","Aperto", "In Lavorazione", "Sospeso", "Chiuso", "Annullato"];
        seriesData.sort((a, b) => {
            const indexA = order.indexOf(a.stato);
            const indexB = order.indexOf(b.stato);
            return indexA - indexB;
        });

        /*const filterModel = {          
          stato: {
                "filterType": 'text',
                  "type": 'notContains',
                  "filter": 'Chiuso'
            }            
          }*/
        const filterModel = {          
          stato: {
                conditions: [
                  {
                    filterType: 'text',
                    type: "notContains",
                    filter: "Chiuso",
                  },
                  {
                    filterType: 'text',
                    type: "notContains",
                    filter: "Annullato",
                  },
                ],
                operator: "AND",
                "filterType": 'text',
              }         
          }          
          gridApi.setGridOption("rowData", seriesData);            
          gridApi.setFilterModel(filterModel);      
        } 
    });   

    function showNotes(condition){
          if (condition){
            notesContainer.classList.remove('hidden');
          }
          else{
            notesContainer = document.getElementById('notes-container');
            notesContainer.classList.add('hidden');
          }
    }    
</script>