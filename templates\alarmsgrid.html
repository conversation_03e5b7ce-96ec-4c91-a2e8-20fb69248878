<div id="context-menu" class="z-10 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
  <div class="p-3 space-y-2">
      <p> Caricamento config ....</p>
      <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
  </div>
</div>
<div id="alarmsGrid" hx-ext="sse"  sse-connect="/webscada/rtalarms" sse-swap="message" hx-swap="none" class="ag-theme-quartz pb-4" style="height:85vh;">
</div>    
<style>
.ag-header-cell-label {
  justify-content: center;
}
.ag-cell {
  justify-content: center;
}
.level-1 {
  text-align: center;
  font-weight: bold;
  background-color: #f44336;
}
.level-2 {
  text-align: center;
  font-weight: bold;
  background-color: #f5a700;
}
.level-3 {
  font-weight: bold;
  background-color: #f5a700;
}
.level-4 {
  font-weight: bold;
  background-color: #f44336;
}
</style>
<script>
    //all pages series will be contained here 
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');
    var selectedData;
    //grid init method
    function gridInit() {
      var gridElement = document.querySelector('#alarmsGrid');
      document.addEventListener('dark-mode', function (e) {
        if (gridElement.classList.contains('ag-theme-quartz-dark')) {
          // If dark theme is applied, switch to light theme
          gridElement.classList.remove('ag-theme-quartz-dark');
          gridElement.classList.add('ag-theme-quartz');
        } else {
          // If light theme is applied, switch to dark theme
          gridElement.classList.remove('ag-theme-quartz');
          gridElement.classList.add('ag-theme-quartz-dark');
        }
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        gridElement.classList.remove('ag-theme-quartz');
        gridElement.classList.add('ag-theme-quartz-dark');
      } else {
        gridElement.classList.remove('ag-theme-quartz-dark');
        gridElement.classList.add('ag-theme-quartz');
      }
      var localeText = JSON.parse(localStorage.getItem('grid-it'));
      var lastCellClicked;
      var gridOptions = {
        // Row Data: The data to be displayed.
        rowData: [],
        // Column Definitions: Defines the columns to be displayed.
        columnDefs: [],
        defaultColDef: {
          flex: 1,          
        },
        localeText: localeText,
        suppressHorizontalScroll: false,
        rowSelection: "multiple",
        onSelectionChanged: () => {
          selectedData = gridApi.getSelectedRows();
          console.log(selectedData,selectedData.length)
          if(selectedData.length > 0){
            ackButton.classList.remove('hidden');
          }else{ackButton.classList.add('hidden');}
          
        },        
        rowClassRules: {
          //"level-0": params => params.api.getValue("livello", params.node) == 0,
          "level-1": params => params.api.getValue("livello", params.node) == 1,
          "level-2": params => params.api.getValue("livello", params.node) == 2,
          "level-3": params => params.api.getValue("livello", params.node) == 3,
          "level-4": params => params.api.getValue("livello", params.node) == 4
        },
        onCellClicked: (e) => {

          let clicked = e.rowIndex.toString() + e.value;
          let ctxMenu = document.getElementById("context-menu");                                        
            if (e.column.getId() == 'name'){       
              if(lastCellClicked == clicked){
                  ctxMenu.classList.toggle('hidden');
                }else{
                  htmx.ajax('GET', `/webscada/config/${e.data.tag}/?chartsIndex=-1`, {target:'#context-menu', swap:'innerHTML'}).then(() => {
                            ctxMenu.style.top = `${e.event.pageY}px`;
                            ctxMenu.style.left =  `${e.event.pageX}px`;
                  });
                  ctxMenu.classList.remove('hidden');
                  lastCellClicked = clicked;
                }                                                                                                
            }else{
                ctxMenu.classList.add('hidden');
            }
      }
      };     

      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      }
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      var cols = [
                { field: "record_number" ,  headerName: 'Id', filter: true,maxWidth:100, cellStyle: { textAlign: 'center' }, checkboxSelection: true,headerCheckboxSelection: true},
                { field: "timestamp", filter: true,minWidth:200, filter: 'agDateColumnFilter', filterParams: { 
                  comparator: (filterLocalDateAtMidnight, cellValue) => {
                        const cellDate = new Date(cellValue);
                        if (cellDate < filterLocalDateAtMidnight) {
                            return -1;
                        } else if (cellDate > filterLocalDateAtMidnight) {
                            return 1;
                        }
                        return 0;
                    },
                  filterOptions : ['greaterThan','lessThan','inRange'],maxNumConditions:1},cellStyle: { textAlign: 'center' }},
                { field: "livello" , filter: true,filter: 'agNumberColumnFilter', filterParams: { filterOptions : ['equals', 'greaterThan','lessThan'],maxNumConditions:1}, maxWidth:100, cellStyle: { textAlign: 'center' }},
                { field: "name" , headerName: 'Canale', filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "messaggio" , filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "periferica" , filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }}
              ]
      var gridApi = agGrid.createGrid(gridElement, gridOptions);
      // Transform the object to row data          
      //let gridRowData = transformToRowData(seriesData)
      gridApi.setGridOption("columnDefs", cols);
      return gridApi;
    }    
    var gridApi = gridInit()    

    //realtime alarms panel
    htmx.on('htmx:sseMessage', (msg) => {
      if (!msg.detail.target.url.includes("/rtalarms"))
          return

        const result = JSON.parse(msg.detail.data);
        let seriesData = []
        for (let e in result) {
          seriesData[e] = result[e]
        }
        // Grid handeling
        gridApi.setGridOption("rowData", seriesData);        
    })   ;   

    function getAckTags(){
      console.log(selectedData)
      let data = JSON.stringify(selectedData)
      console.log(data)
      return  data;
    }
</script>