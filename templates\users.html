
<div class="table-wrp block max-h-96 " >
    <table id="alarmsTable" class="w-full sticky top-0 text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4">
                    <div class="flex items-center">
                        <input id="checkbox-all" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-all" class="sr-only">checkbox</label>
                    </div>
                </th>
                <th scope="col" class="px-6 py-3 text-center">id</th>
                <th scope="col" class="px-6 py-3 text-center">nome</th>
                <th scope="col" class="px-6 py-3 text-center">email</th>
                <th scope="col" class="px-6 py-3 text-center">password</th>
                <th scope="col" class="px-6 py-3 text-center">abilitato</th>
                <th scope="col" class="px-6 py-3 text-center">webscada</th>
                <th scope="col" class="px-6 py-3 text-center">helpdesk</th>
                <th scope="col" class="px-6 py-3 text-center">profilo</th>                
            </tr>
        </thead>
        <tbody h-96 overflow-y-auto>
            {%- for value in items -%}
            {% if value['deleted'] != '00' %}
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="w-4 p-4">
                    <div class="flex items-center">
                        <input id="checkbox-table-1" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-table-1" class="sr-only">checkbox</label>
                    </div>
                </td>
                                                            
                <th scope="row" class=" font-medium text-center text-gray-900 whitespace-nowrap dark:text-white">
                    {{value['usr_id']}}
                </th>
                <td class=" font-medium text-center">
                    {{value['login']}}
                </td>
                <td  id="email-container-{{value['usr_id']}}" class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  {{color}}">
                    {% set key = "iset.user." + value['usr_id'] %}
                    {% if key in users %}
                        {{ users[key]["email"]}}
                    {% endif %}
                    <button hx-get="/webscada/edit/email/{{value['usr_id']}}" hx-swap="outerHTML"  hx-target="#email-container-{{value['usr_id']}}" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">Modifica</button>
                </td>
                <td class=" font-medium text-center">
                    *******
                </td>
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 {{color}}">
                    {% if value['deleted'] == '0' %}
                        Sì
                    {% endif %}
                    {% if value['deleted'] == '1' %}
                        No
                    {% endif %}                    
                </td>  
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 {{color}}">
                    {% if value['deleted'] == '0' %}
                    <input checked id="checked-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">    
                    {% endif %}
                    {% if value['deleted'] == '1' %}
                    <input id="checked-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">    
                    {% endif %}                    
                </td>  

                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 {{color}}">
                    {% if value['deleted'] == '0' %}
                    <input checked id="checked-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">    
                    {% endif %}
                    {% if value['deleted'] == '1' %}
                    <input id="checked-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">    
                    {% endif %}                       
                </td>                           
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 {{color}}">
                    <button hx-get="/webscada/user/{{value['usr_id']}}"   hx-trigger="click" hx-swap="outerhtml" hx-target="#user-{{value['usr_id']}}" data-popover-trigger="click" data-popover-target="user-{{value['usr_id']}}" data-popover-placement="bottom-end" type="button">
                        <svg class="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                        </svg><span class="sr-only">Show information</span>
                    </button>
                    <div    data-popover="" id="user-{{value['usr_id']}}" role="tooltip" class="absolute z-10 inline-block text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 opacity-0 invisible" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(221px, 731px);" data-popper-placement="bottom-start">
                        <div class="p-3 space-y-2">
                            <p> Caricamento config ....</p>
                            <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
                        </div>
                        <div data-popper-arrow="" style="position: absolute; left: 0px; transform: translate(7px, 0px);"></div>
                    </div>                      
                </td>                      
            </tr>
            {% endif %}
        {%- endfor -%}            
        </tbody>
    </table>
</div>
<script>
    datepicker.classList.add('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.add('hidden');  
    ackButton.classList.add('hidden');  
</script>