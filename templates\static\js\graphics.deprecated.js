function colorNameToRGBA(colorName) {
    // Create a new option element to apply the color to
    const optionElement = new Option();
    optionElement.style.color = colorName;
  
    // Append the option element to the body to ensure the style is computed
    document.body.appendChild(optionElement);
  
    // Get the computed color style
    const rgbColor = window.getComputedStyle(optionElement).color;
  
    // Remove the option element from the body
    document.body.removeChild(optionElement);
  
    // Extract the numeric values of the RGB color
    let rgbValues = rgbColor.match(/\d+/g).map(Number);
  
    rgbValues.push(1);

    return rgbValues;
  }
  
function getPixel(imageData, x, y) {
  if (x < 0 || y < 0 || x >= imageData.width || y >= imageData.height) {
    return [-1, -1, -1, -1];  // impossible color
  } else {
    const offset = (y * imageData.width + x) * 4;
    return imageData.data.slice(offset, offset + 4);
  }
}
  
function setPixel(imageData, x, y, color) {
  const offset = (y * imageData.width + x) * 4;
  imageData.data[offset + 0] = color[0];
  imageData.data[offset + 1] = color[1];
  imageData.data[offset + 2] = color[2];
  //imageData.data[offset + 3] = color[0];
}
  
function colorsMatch(a, b) {
  let thres = 25;//20
  return a[0] >= b[0]-thres && a[0] <= b[0]+thres
          && a[1] >= b[1]-thres && a[1] <= b[1]+thres
          && a[2] >= b[2]-thres && a[2] <= b[2]+thres
          //&& a[3] >= b[3]-thres && a[3] <= b[3]+thres
}
  
function floodFill(ctx, x, y, fillColor) {
  // read the pixels in the canvas
  const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
  
  // get the color we're filling
  const targetColor = getPixel(imageData, x, y);
  
  // check we are actually filling a different color
  if (!colorsMatch(targetColor, fillColor)) {
  
    fillPixel(imageData, x, y, targetColor, fillColor);
    
    // put the data back
    ctx.putImageData(imageData, 0, 0);
  }
}
  
function fillPixel(imageData, x, y, targetColor, fillColor) {
  const currentColor = getPixel(imageData, x, y);
  if (colorsMatch(currentColor, targetColor)) {
    setPixel(imageData, x, y, fillColor);
    fillPixel(imageData, x + 1, y, targetColor, fillColor);
    fillPixel(imageData, x - 1, y, targetColor, fillColor);
    fillPixel(imageData, x, y + 1, targetColor, fillColor);
    fillPixel(imageData, x, y - 1, targetColor, fillColor);
  }
}

function fill(ctx,result,coordinates) {  
  for( let e in coordinates){ 
      let data = coordinates[e];
      let states = JSON.parse(data[2]);                                                                                          
      
      for (let tag in states){
          //console.log(data[0],data[1],tag)
          if(result[tag]){  
              for (let i in states[tag]){
                  let boolState = 0;
                  if(states[tag][i].state == 'true')
                      boolState = 1;
                  if(result[tag].value == boolState){
                      //console.log('xxx',result[tag].value,states[tag][i]);
                      //console.log('xxx',result[tag].value,states[tag]);

                      try{  
                              //if(previous[tag] && (previous[tag] !=result[tag].value)){
                                  floodFill(ctx, data[0], data[1],  colorNameToRGBA(states[tag][i].color));
                                  if(states[tag].blink == 'true'){
                                      blink[tag] = {'x':data[0],'y':data[1],'on':[255,0,0,1], 'off':[0,255,0,1] }
                                  }
                                  previous[tag] = result[tag].value;
                              
                              //console.log('done---------------',tag,'x',data[0],'y',data[1])
                              //}
                          }catch(e){console.log(e)}
                  }
              }
          }
          
        }   
    }                               
} 

self.addEventListener('message', function(e) {
  var data = e.data;
  console.log('Message from worker: ',data);
  //fill(data.ctx, data.result, data.coordinates);
  floodFill(data.ctx, data.coordinates[0], data.coordinates[1], data.colors);


}, false);

