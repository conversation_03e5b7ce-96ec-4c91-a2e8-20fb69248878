<link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
<body class="size-full overflow-auto bg-gray-50 dark:bg-gray-800">
<div class="flex pt-16 justify-center items-center min-h-screen">
  <div class="video-container relative">
    <!-- Hidden image that receives the stream -->
    <img id="source-feed" class="hidden" src="/webscada/feed/{{feed}}">
    
    <!-- Hidden overlay image -->
    <img id="overlay-image" class="hidden" src="/webscada/static/img/cctv/overlay-{{feed}}.png">
    
    <!-- Canvas where we'll draw the video with overlay -->
    <canvas id="masked-feed" class="focus:border focus:border-red-600 rounded-lg"></canvas>
    
    <script>
      // Get references to our elements
      var sourceImg = document.getElementById('source-feed');
      var overlayImg = document.getElementById('overlay-image');
      var canvas = document.getElementById('masked-feed');
      var ctx = canvas.getContext('2d');
      
      // Set fixed overlay opacity
      var overlayOpacity = 1; // 70% opacity
      
      // Initialize canvas size when image loads
      sourceImg.onload = function() {
        // Set canvas dimensions to match the source image (MPEG stream)
        canvas.width = sourceImg.naturalWidth;
        canvas.height = sourceImg.naturalHeight;
        
        // Log the dimensions for debugging
        console.log(`Stream dimensions: ${canvas.width}x${canvas.height}`);
      };
      
      // Function to draw the frame with overlay
      function drawFrame() {
        if (sourceImg.complete && sourceImg.naturalWidth > 0) {
          // Clear canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // Draw the video frame
          ctx.drawImage(sourceImg, 0, 0, canvas.width, canvas.height);
          
          // Draw the overlay if it's loaded
          if (overlayImg.complete && overlayImg.naturalWidth > 0) {
            // Set global alpha for transparency
            ctx.globalAlpha = overlayOpacity;
            
            // Draw the overlay at full canvas size
            ctx.drawImage(overlayImg, 0, 0, canvas.width, canvas.height);
            
            // Reset global alpha
            ctx.globalAlpha = 1.0;
          }
        }
        
        // Request next frame
        requestAnimationFrame(drawFrame);
      }
      
      // Start the drawing loop
      drawFrame();
      
      // Handle overlay image loading errors
      overlayImg.onerror = function() {
        console.error('Failed to load overlay image');
        // Provide a fallback or notification
        const fallbackOverlay = document.createElement('div');
        fallbackOverlay.textContent = 'Overlay image not found. Please place an image at /webscada/static/img/overlay.png';
        fallbackOverlay.style.position = 'absolute';
        fallbackOverlay.style.top = '10px';
        fallbackOverlay.style.left = '10px';
        fallbackOverlay.style.backgroundColor = 'rgba(255,0,0,0.7)';
        fallbackOverlay.style.color = 'white';
        fallbackOverlay.style.padding = '5px';
        fallbackOverlay.style.borderRadius = '5px';
        fallbackOverlay.style.zIndex = '100';
        document.querySelector('.video-container').appendChild(fallbackOverlay);
      };
    </script>
  </div>
</div>
</body>