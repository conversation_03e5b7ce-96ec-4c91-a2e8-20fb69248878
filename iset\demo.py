from messaging import component
import nats.js.errors
import asyncio
async def run():
    nc = component()
    await nc.connect(bucket = 'scada')
    watcher =await nc.watchkv('reports.reportGruppiRiduzione.anagrafica.>')
    dic = dict()
    result = await watcher.updates(timeout=2) 
    while result is not None:

        try:              
           print(result)     
           dic[result.key] = 'key'
           result = await watcher.updates(timeout=2)    
        except nats.errors.TimeoutError as e:
            print('t',e) 
        except Exception as e:
            print(e)
        #await asyncio.sleep(0.001)

    while True:
        print(len(dic))
        await asyncio.sleep(2)

asyncio.run(run())

