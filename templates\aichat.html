{% for msg in session %}
    {% if msg.role == 'model' %}
    <div id="msg-{{msg['timestamp']}}" class="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg dark:text-white">
        <div class="flex justify-between items-center mb-1">
        <div class="font-bold">Scada Agent:</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">{{msg['timestamp']}}</div>
        </div>
        <div class="content">{{msg['content']}}</div>
    </div>
    {% else %}
    <div id="msg-{{msg['timestamp']}}" class="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg dark:text-white">
        <div class="flex justify-between items-center mb-1">
        <div class="font-bold">Tu:</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">{{msg['timestamp']}}</div>
        </div>
        <div class="content">{{msg['content']}}</div>
    </div>
    {% endif %}
{% endfor %}