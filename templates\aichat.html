<div  id="conversation" class="flex flex-col h-full dark:bg-gray-800">
<div id="chat-element" class="mb-4 space-y-4 flex-1 overflow-y-auto">
{% set ns = namespace(full=0) %}
{% for msg in session %}
    {% if msg.role == 'model' %}
    <div id="msg-{{msg['timestamp']}}" class="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg dark:text-white">
        <div class="flex justify-between items-center mb-1">
        <div class="font-bold">Scada Agent:</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">{{msg['timestamp']}}</div>
        </div>
        <div class="content">{{msg['content']}}</div>
    </div>
    {% else %}
    <div id="msg-{{msg['timestamp']}}" class="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg dark:text-white">
        <div class="flex justify-between items-center mb-1">
        <div class="font-bold">Tu:</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">{{msg['timestamp']}}</div>
        </div>
        <div class="content">{{msg['content']}}</div>
    </div>
    {% endif %}
    {% if msg.full %}
        {% set ns.full = 1 %}
    {% else %}
        {% set ns.full = 0 %}           
    {% endif %}
  {% endfor %}
</div>

    <div id="stop-element"
      class="hidden flex items-center justify-center text-center text-xs text-gray-400 dark:text-gray-500 mb-3">
      <div class="px-2 py-1 text-xs font-medium leading-none text-center text-blue-800 bg-blue-200 rounded-full animate-pulse dark:bg-blue-900 dark:text-blue-200">
        Generazione risposta... </div>
      <button id="stop-button" class="mx-2 px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none">
        Stop
      </button>
    </div>
    {% if ns.full == 0 %}
    <form  id="{{session_id}}" method="post" class="space-y-2 ">
      <input id="prompt-input" name="prompt"
        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        placeholder="Scrivi il tuo messaggio qui..."></input>
        
        <div class="grid grid-cols-5 gap-2 mb-1">
          <div
            class="border-2 text-center border-dashed border-gray-300 rounded-lg dark:border-gray-600 p-2 h-32 md:h-32 text-gray-600 dark:text-gray-400">
            Aggiornami sui valori
          </div>
          <div
            class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-2 h-32 md:h-32 text-gray-600 dark:text-gray-400">
            Dammi gli allarmi
          </div>
          <div
            class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-2 h-32 md:h-32 text-gray-600 dark:text-gray-400">
            Info sugli impianti
          </div>
          <div
            class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-2 h-32 md:h-64 text-gray-600 dark:text-gray-400">
            Chiedimi qualsiasi info sullo Scada
          </div>          
          <button
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-4 focus:ring-blue-300">
          Invia
        </button>

        </div>   

      <div class="flex justify-end">
     

      </div>
    </form>
    {% else %}
    <form id="{{session_id}}" method="post" class="space-y-2">
      <input id="prompt-input" name="prompt" 
        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        placeholder="La conversazione è terminata."></input>
      <div class="flex justify-end">
        <button disabled
          class="px-4 py-2  bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-4 focus:ring-blue-300">
          Invia
        </button>
      </div>
    </form>
    {% endif %}
</div>
