{% extends 'base.html' %}
{% block header %}
<title>Webscada Novareti</title>
{% endblock header %}

{% block content %}
<style>
    #send-otp:after {
      content: 'Invia Token';
    }
  
    #send-otp:focus:after {
      content: 'Invio in corso...';
      animation: pulse 2s infinite;
    }
  
    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }
</style>
<!-- Card -->
<div class="grid  justify-center pt-16">
    <svg class="novasvg" viewBox="0 0 694.4 239.4" xmlns="http://www.w3.org/2000/svg"> <path  class="st0" d="M96.1 78.6v98.6H80.6l-43.3-74.5v74.5H24.1V78.6h15.6L83.1 155V78.6H96.1z" stroke-dasharray="1200 1000" fill="#1941a5"> </path> <path class="st0" d="M209 127.6c0 27.2-20.7 49.5-48 49.5 -25.9 0-46.8-22.3-46.8-49.5 0-27.1 20.9-49.2 46.8-49.2C188.3 78.5 209 100.6 209 127.6zM195.5 127.6c0-19.6-14.8-35.5-34.5-35.5 -18.5 0-33.3 15.9-33.3 35.5 0 19.9 14.8 35.9 33.3 35.9C180.7 163.6 195.5 147.5 195.5 127.6z" stroke-dasharray="1200 1000" fill="#1941a5"> </path> <path class="st0" d="M250.2 141.3l28.3-63H294l-43.8 98.9 -42.3-98.9h15.5L250.2 141.3z" stroke-dasharray="1200 1000" fill="#1941a5"> </path> <path class="st1" d="M461 139.1c9.3-6.2 14.8-16.5 14.8-27.6 0-17.8-14.4-32.6-32.3-33.1h-6.2 -22l-0.1 68.6L428 177h0.5v-32.3h14.8l0.3-0.1c1.4 0 2.7-0.1 3.9-0.3l16.6 32.3 0.1 0.3 15.2-0.1L461 139.1zM442.7 131.4h-14.2V91.8h14.2c10.9 0 19.9 8.9 19.9 19.7C462.6 122.5 453.5 131.4 442.7 131.4z" stroke-dasharray="1200 1000" fill="#0974bb"> </path> <path class="st1" d="M548.1 164.1v13.1h-53.5V78.6h52.8v13.2h-39.3v30.3h30.1v13.1h-30.1v28.9H548.1z" stroke-dasharray="1200 1000" fill="#0974bb"> </path> <path class="st1" d="M630.1 78.5v13.1l-27.9-0.1 0.3 86h-13.9l0.1-86h-27.3v-13H630.1z" stroke-dasharray="1200 1000" fill="#0974bb"> </path> <path class="st1" d="M655.6 177.4h-13.7V78.6h13.7V177.4z" stroke-dasharray="1200 1000" fill="#0974bb"> </path> <path class="st0" d="M340.9 53.6l-54.1 123.5 -15.5 0 69.6-159.5 67.4 159.5 -15.5 0L340.9 53.6z" stroke-dasharray="1200 1000" fill="#1941a5"> </path> </svg>
    <!--<h2 class="text-2xl font-bold text-gray-900 dark:text-white">Login a Scada </h2>-->
    <form class="mt-8 space-y-6" hx-post="/webscada/otp" hx-indicator="#spinner" hx-swap="outerHTML" method="post">
        <div>
            <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
            <input type="email" name="email" id="email" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>" required>
        </div>
            <button id="send-otp" type="submit" name="action"
            class="w-full text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex justify-center items-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"></button>
         <div>
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
            Non sei registrato? <a class="text-primary-700 hover:underline dark:text-primary-500">contatta il reparto Scada di Novareti</a>
        </div>
    </form>
</div>

{% include 'common/footer.html' %}
</div>
{% endblock content %}
