import os
import shutil
import toml
import logging
from iset.iset import get_config


           

def app_config(config_path = "config.toml"):    
    try:
        with open(config_path, "r") as f:
            config = toml.load(f)
            isetengines = get_config(config['iset']['isetini'])
            config['isetengines'] = isetengines
            return config
    except FileNotFoundError:
        print(f"Config file not found: {config_path}")
    except toml.TomlDecodeError as e:
        print(f"Invalid TOML format in {config_path}: {e}")

app_config()