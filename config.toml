#config file for webscada

title = "WebScada config"

[server]
port = 5000

[iset]
isetini = "\\\\192.168.30.63\\C_Server1_Scada\\Windows\\iset.ini"
isetgui = "\\\\192.168.30.63\\C_Server1_Scada\\Iset\\Idro.NetWork\\Data\\DR\\Gui\\"
isetusers = "\\\\192.168.30.63\\C_Server1_Scada\\Iset\\Idro.NetWork\\Data\\Server\\Scada\\Users\\DR\\users.data"

[nats]
servers = ["nats://192.168.30.85:4222","nats://192.168.30.40:4222" ]
ca = "\\\\192.168.30.40\\Apps\\Production\\Services\\Nats\\CA certificates 4 clients\\ca.pem"
certificate = "\\\\192.168.30.40\\Apps\\Production\\Services\\Nats\\CA certificates 4 clients\\client-cert.pem"
key = "\\\\192.168.30.40\\Apps\\Production\\Services\\Nats\\CA certificates 4 clients\\client-key.pem"
username = "amine" 
password = "tellmeaboutnode2natsplease"
