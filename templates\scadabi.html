<style>
  .chart-timeline {
    position: relative;
    height: 350px;
    overflow: hidden;
  }

.ag-header-cell-label {
  justify-content: center;
}
.search-input {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
</style>

<!-- Statistics Modal -->
<div id="stats-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
  <div class="bg-white rounded-lg shadow-xl p-6 w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 id="stats-modal-title" class="text-lg font-medium">Statistical Analysis</h3>
      <button id="close-stats-modal" class="text-gray-500 hover:text-gray-700">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="stats-results" class="overflow-x-auto">
      <!-- Results will be inserted here -->
    </div>
    <div id="stats-processing-indicator" class="text-center py-4 hidden">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      <p class="mt-2 text-gray-600">Calcolo statistiche...</p>
    </div>
  </div>
</div>
<!---->

<div id="chart-{{chartIndex}}">
  <div class="rounded-lg dark:bg-gray-800 p-1 md:p-1">
    <div class="flex justify-between mb-2">
      <div id="chart-legend-{{chartIndex}}" class="flex p-1 mx-auto my-2 bg-gray-100 rounded-lg dark:bg-gray-600" role="group">
        <div data-popover="" id="plants-search" role="tooltip" 
          class="absolute overflow-auto z-10 min-w-max  text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-2xl dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 opacity-0 invisible " style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(221px, 731px);" data-popper-placement="bottom-start">
          <div class="p-2">
                {% include 'scadabiplant.html' %} 
          </div>                                        
          <div data-popper-arrow="" style="position: absolute; left: 0px; transform: translate(7px, 0px);"></div>
        </div>        
        <button onclick="{document.getElementById('context-menu').classList.add('hidden')}" 
        data-popover-trigger="click" data-popover-target="plants-search" data-popover-placement="bottom-end"
        class="px-2 py-1.5 mx-1 text-xs font-medium bg-blue-700 hover:bg-blue-800 text-gray-900 hover:bg-blue-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
        <svg class="w-6 h-6 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="m21 21-3.5-3.5M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z"/>
        </svg>
        </button>
        <button id='zoom-{{chartIndex}}-1' onclick="zoomLocalChart(this,1,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          1 g
        </button>
        <button id='zoom-{{chartIndex}}-2' onclick="zoomLocalChart(this,7,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          7 g
        </button>
        <button id='zoom-{{chartIndex}}-3' onclick="zoomLocalChart(this,30,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          1 m
        </button>    
        <button id='zoom-{{chartIndex}}-4' onclick="zoomLocalChart(this,90,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          3 m
        </button> 
        <button id='zoom-{{chartIndex}}-5' onclick="zoomLocalChart(this,180,'chart-timeline-{{chartIndex}}')" class="hidden md:block px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          6 m
        </button>   
        <button id='zoom-{{chartIndex}}-6' onclick="zoomLocalChart(this,365,'chart-timeline-{{chartIndex}}')" class="hidden md:block px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          1 y
        </button>     
        <button  id="zoom-{{chartIndex}}-7" data-dropdown-toggle="datetimePicker-{{chartIndex}}" type="button" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.5 11.5 2.071 1.994M4 10h5m11 0h-1.5M12 7V4M7 7V4m10 3V4m-7 13H8v-2l5.227-5.292a1.46 1.46 0 0 1 2.065 2.065L10 17Zm-5 3h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
          </svg>
          
        </button>     
        <!-- datetime dropdown menu -->
        <div id="datetimePicker-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm w-auto dark:bg-gray-700 p-1">
          <div class="max-w-[16rem] mx-auto gap-4 mb-1">
              <div class="flex mb-2">
                  <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Inizio:</label>
                    <input type="date" id="start-date-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    <input type="time" id="start-time-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
              </div>
              
              <div class="flex mb-2">
                <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Fine:</label>
                  <input type="date" id="end-date-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                  <input type="time" id="end-time-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
            </div>
          </div>          
          <button type="button" onclick="setDateTime('{{chartIndex}}')"
           class=" w-full rounded-b-lg  p-2 text-sm font-medium text-red-600 border-t border-gray-200 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">Invia</button>          
        </div>    
        
        <button  data-dropdown-toggle="axisSettings-{{chartIndex}}" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          <svg id="axisSettingsToggle-{{chartIndex}}" class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v15a1 1 0 0 0 1 1h15M8 16l2.5-5.5 3 3L17.273 7 20 9.667"/>
          </svg>                                           
        </button> 

        <!-- axis update dropdown menu -->
        <div id="axisSettings-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700 p-1">
          <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
            <label for="chart-{{chartIndex}}-min" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Min:</label>
            <input id="chart-{{chartIndex}}-min" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
          </div>
          <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
            <label for="chart-{{chartIndex}}-max" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Max:</label>
            <input id="chart-{{chartIndex}}-max" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
          </div> 
          <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
            <div class="inline-flex items-center me-5 cursor-pointer">
              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Scala</span>
            </div> 
            <label>
              <input type="checkbox" value="" class="sr-only peer" onclick="updateYAxis(this,'{{chartIndex}}')" >
              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>
            </label>
          </div>   

          <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 border-t 
           border-gray-200 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 ">
            <div class="inline-flex items-center me-5 cursor-pointer">
              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Aggregati</span>
             </div>
             <label>
              <input type="checkbox" value="" class="sr-only peer" onclick="enableAvg(this,'{{chartIndex}}')" >
              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>           
             </label> 
          </div> 

          <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 border-t 
           border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 ">
            <div class="inline-flex items-center me-5 cursor-pointer">
              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Multiasse Y</span>
             </div>
             <label>
              <input id="toggle-yaxis" type="checkbox" value="" class="sr-only peer" onclick="toggleYAxis(this,'{{chartIndex}}')" >
              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>           
             </label> 
          </div>           
        </div>  

        <button data-dropdown-toggle="chartSeries-{{chartIndex}}" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
          </svg>                                                                                                        
        </button>
        <!-- Axis dropdown menu -->
        <div id="chartSeries-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700">

          <ul id="charts-list-{{chartIndex}}" class=" px-1 py-1 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownSearchButton">
          </ul>
          <button onclick="deleteSeries('{{chartIndex}}')" class="w-full items-center p-2 text-sm font-medium text-red-600 border-t border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
           Elimina misura
          </button>
      </div>                                          
      </div>
      

    <!--
    {% if chartIndex > 1 %}
    <button id="close-chart-{{chartIndex}}"  hx-get="/webscada/delete/" hx-vals='{"chartIndex":{{chartIndex}}}' hx-swap="outerHTML" hx-target="#chart-{{chartIndex}}"
        type="button" class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"> 
        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
        </svg>
    </button>
  
    {% endif %}  -->
    </div>
  </div>
</div>

<div class="px-4 grid grid-cols-3 gap-4">
    <div class="col-span-3 ...">
        <div id ='data-processing-indicator' class='hidden absolute top-4 left-1/2 transform -translate-x-1/2  z-50  bg-blue-600 text-white px-4 py-2 rounded-md shadow-lg z-50 transition-opacity duration-300'>
                <div class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p id="loading-status-text">Analisi dati...</p>
                </div>
        </div>      
        <div id="chart-{{chartIndex}}">

        <div id="chart-timeline-{{chartIndex}}" class="chart-timeline">
        
        </div>               
    </div> 
</div>
  <div class="..."></div>
  <div class="..."></div>
  <div class="col-span-3 ...">
    <div class="flex justify-between items-center m-2">
      <div id="rowCount" class="mx-2 px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">Righe totali: 0</div>
      <div class="hidden md:block flex space-x-2">
        <button id="restoreData" class="text-sm bg-green-500 text-white rounded px-2 py-1">Ripristina</button>
        <select id="statsDropdown" class="px-2 py-1.5 text-xs font-medium text-gray-900  dark:text-white dark:bg-gray-700 rounded-lg">
          <option value="">Statistiche...</option>
          <option value="hourly">Aggregazione oraria</option>
          <option value="daily">Aggregazione giornaliera</option>
          <option value="weekly">Aggregazione settimanale</option>
          <option value="monthly">Aggregazione mensile</option>
          <option value="yearly">Aggregazione annuale</option>
        </select>
        <select id="aggFunction" class="px-2 py-1.5 text-xs font-medium text-gray-900  dark:text-white dark:bg-gray-700 rounded-lg">
          <option value="AVG">Media</option>
          <option value="MIN">Minimo</option>
          <option value="MAX">Massimo</option>
          <option value="SUM">Somma</option>
          <option value="COUNT">Conteggio</option>
          <option value="STDDEV">Deviazione standard</option>
        </select>
        <button id="calculateStats" class="text-sm bg-blue-500 text-white rounded px-2 py-1">Calcola</button>
        
      </div>
      <div class="block md:hidden flex space-x-2">
            <button  data-dropdown-toggle="aggregates-settings" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
 
                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10.83 5a3.001 3.001 0 0 0-5.66 0H4a1 1 0 1 0 0 2h1.17a3.001 3.001 0 0 0 5.66 0H20a1 1 0 1 0 0-2h-9.17ZM4 11h9.17a3.001 3.001 0 0 1 5.66 0H20a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H4a1 1 0 1 1 0-2Zm1.17 6H4a1 1 0 1 0 0 2h1.17a3.001 3.001 0 0 0 5.66 0H20a1 1 0 1 0 0-2h-9.17a3.001 3.001 0 0 0-5.66 0Z"/>
                </svg>

            </button> 
                  <div id="aggregates-settings" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700 p-1">
                     <div class="flex flex-col space-y-2">
                        <select id="statsDropdownMobile" class="px-2 py-1.5 text-xs font-medium text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg">
                        <option value="">Statistiche...</option>
                        <option value="hourly">Aggregazione oraria</option>
                        <option value="daily">Aggregazione giornaliera</option>
                        <option value="weekly">Aggregazione settimanale</option>
                        <option value="monthly">Aggregazione mensile</option>
                        <option value="yearly">Aggregazione annuale</option>
                        </select>
                        <select id="aggFunctionMobile" class="px-2 py-1.5 text-xs font-medium text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg">
                        <option value="AVG">Media</option>
                        <option value="MIN">Minimo</option>
                        <option value="MAX">Massimo</option>
                        <option value="SUM">Somma</option>
                        <option value="COUNT">Conteggio</option>
                        <option value="STDDEV">Deviazione standard</option>
                        </select>
                        <button id="calculateStatsMobile" class="text-sm bg-blue-500 text-white rounded px-2 py-1">Calcola</button>
                        <button id="restoreDataMobile" class="text-sm bg-green-500 text-white rounded px-2 py-1">Ripristina</button>
                    
                    </div>       
                  </div>  
        </div>
    </div>
    <div id="analyticsGrid" class="px-4 ag-theme-quartz pb-16" style="height: 50vh;">
    </div>  
  </div>
</div>
<!--...........Datatable library ..........-->
<script>
  var seriesData = [] 
  var keymap = {}
  var gridApi = gridInit('#analyticsGrid') 

  datepicker.classList.add('hidden');
  dropdownRadioButton.classList.add('hidden');
  downloadGrid.classList.add('hidden');
  ackButton.classList.add('hidden');
  downloadGrid.classList.remove('hidden');
  resetFilters.classList.remove('hidden');

  //charts index to get all charts
  var chartIndex = 1;
  function getChartsIndex(){
    return chartIndex;
  }
  
    function isMobile() {
    return /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
    }

    var timeframe = 10000;
    var aggregateTime =5;
    if (isMobile()) {
        var timeframe = 365*5;
        var aggregateTime = 15;
    } 

//var gridApi = gridInit('#analyticsGrid')   
//var chartLegend = document.getElementById(`chart-legend-${chartIndex}`)

var chartLegends = document.querySelectorAll('[id^="chart-legend"]');
chartLegends.forEach(chartLegend => {
var buttons = chartLegend.querySelectorAll('button');
          buttons.forEach(button => {
            if(button.id.includes('zoom')){
                button.addEventListener('click', () => {
                  buttons.forEach(btn => {          
                    btn.classList.remove('bg-gray-900', 'text-white');
                  });
                  button.classList.add('bg-gray-900', 'text-white');
                });
              }
          });
        })
//grid init method
function gridInit(id){
      var gridElement = document.querySelector(id);    
      document.addEventListener('dark-mode', function(e) {
          if (gridElement.classList.contains('ag-theme-quartz-dark')) {
              // If dark theme is applied, switch to light theme
              gridElement.classList.remove('ag-theme-quartz-dark');
              gridElement.classList.add('ag-theme-quartz');
          } else {
              // If light theme is applied, switch to dark theme
              gridElement.classList.remove('ag-theme-quartz');
              gridElement.classList.add('ag-theme-quartz-dark');
          }    
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                  gridElement.classList.remove('ag-theme-quartz');
                  gridElement.classList.add('ag-theme-quartz-dark');
              } else {
                  gridElement.classList.remove('ag-theme-quartz-dark');
                  gridElement.classList.add('ag-theme-quartz');
              }
              var localeText = JSON.parse(localStorage.getItem('grid-it'));
      
      var gridOptions = {
              // Row Data: The data to be displayed.
              rowData : [],
              // Column Definitions: Defines the columns to be displayed.
              columnDefs: [],
              defaultColDef: {
                          flex: 1,
                      }, 
                      //enableRangeSelection: true,
        //enableCharts: true,
        localeText: localeText,
        rowSelection: "multiple",  
    onFilterChanged: function() {
      updateRowCount(gridApi);
    },
    
    // Add onGridReady event
    onGridReady: function(params) {
      updateRowCount(params.api);
    }                      
              };        
      var gridApi = agGrid.createGrid(gridElement, gridOptions); 
      
      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      } 
      
      // Initialize statistics listeners
      initStatsListeners();
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      return gridApi;
}

async function setDateTime(id) {
  let startDate = document.getElementById(`start-date-${id}`); 
  let startTime = document.getElementById(`start-time-${id}`);
  let endDate = document.getElementById(`end-date-${id}`);
  let endTime = document.getElementById(`end-time-${id}`);
  let zoomButton = document.getElementById(`zoom-${id}-7`);

  let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
 
  if (startDate.value !== "" && endDate.value !== "") {
    // Set default times if not provided
    const startTimeValue = startTime.value !== "" ? startTime.value : "00:00";
    const endTimeValue = endTime.value !== "" ? endTime.value : "23:59";
    
    // If time fields are empty, update them with default values
    if (startTime.value === "") startTime.value = startTimeValue;
    if (endTime.value === "") endTime.value = endTimeValue;
    
    // Create Date objects to validate the interval
    const startDateTime = new Date(`${startDate.value}T${startTimeValue}`);
    const endDateTime = new Date(`${endDate.value}T${endTimeValue}`);
    
    // Calculate the difference in milliseconds
    const timeDiff = endDateTime - startDateTime;
    
    // Maximum allowed interval (1 year in milliseconds)
    const maxInterval = 365 * 24 * 60 * 60 * 1000;
    
    // Check if interval is valid (positive and not too large)
    if (timeDiff <= 0) {
        alert("La data di fine deve essere successiva alla data di inizio");
        return;
    } else if (timeDiff > maxInterval) {
        alert("L'intervalo selezionato è meggiore di un anno. Scegliere un intervallo più piccolo");
        return;
    }
    
    // Format the datetime strings
    startDatetime = `${startDate.value}T${startTimeValue}`;
    endDatetime = `${endDate.value}T${endTimeValue}`; 
    
    // Update UI to show active state
    zoomButton.classList.add('bg-gray-900', 'text-white');
    
    // Apply the zoom
    myChart.dispatchAction({
        type: 'dataZoom',
        startValue: startDatetime,
        endValue: endDatetime
    });
  } else {
    // Alert if dates are missing
    alert("Seleziona entrambi le date");
  }
}

async function zoomLocalChart(e,days,id) {
  
  var endDate = new Date();
  var startDate = new Date(); 

  endDate.setDate(endDate.getDate() + 1); 
  startDate.setDate(endDate.getDate() - days); // Calculate 'today - X days'

  let myChart = echarts.init(document.getElementById(id));
  try {
    var seriesData = myChart.getOption().series;  
  } catch (error) {
    console.warn(error)
    return
  }
  index = parseInt(id.split("-").pop());
  for (var i = 0; i < seriesData.length; i++) {
      let earlyDate;
      /*if(seriesData[i].dataType == 'DIN'| seriesData[i].dataType == 'DOUT'){
        earlyDate = new Date(seriesData[i].data[0].value[1]);
      }else{
        earlyDate = new Date(Date.parse(seriesData[i].data[0][0]));
      }*/
      earlyDate = new Date(Date.parse(seriesData[i].data[0][0]))
      /*if(earlyDate > startDate){
        htmx.ajax('GET', `/webscada/chart/${days}/${seriesData[i].tag}?chartIndex=${index}`, {swap:'none', indicator:"#spinner"})
      }*/
  }

  myChart.dispatchAction({
      type: 'dataZoom',
      startValue: startDate.toISOString().slice(0, 10), // Format as 'YYYY-MM-DD'
      endValue: endDate.toISOString().slice(0, 10) // Format as 'YYYY-MM-DD'
  });

}

async function clearLocalChart(index,id) {
        console.log(seriesData,index,id)
        seriesData[index] = {}
        chartElement = document.getElementById(id)  
        let myChart = echarts.init(chartElement, null, {
                          renderer: 'canvas',
                          useDirtyRect: false
                      });       
        myChart.setOption({}, true);
        await gridHandle(gridApi)
}

function clearFilters() {
      gridApi.setFilterModel(null);
} 
function onBtnExport() {
  if (gridApi) {
      gridApi.exportDataAsCsv({
        fileName: `data_export_${new Date().toISOString().slice(0, 10)}.csv`
      });
    }
}

function gridHandle(gridApi) {
    // Show loading indicator
    const loadingIndicator = document.getElementById('data-processing-indicator') ;
    updateLoadingStatus(loadingIndicator, 'Processing data...');
    
    // Create a worker for data transformation if it doesn't exist
    if (window.Worker && typeof window.gridDataWorker === 'undefined') {
        try {
            const workerCode = `
                self.onmessage = function(e) {
                    const obj = e.data;
                    
                    // Safety check for null/undefined data
                    if (!obj) {
                        self.postMessage({
                            status: 'error',
                            message: 'No data received'
                        });
                        return;
                    }
                    
                    // Collection phase - gather all timestamps
                    const timestamps = new Set();
                    for (let key in obj) {
                        // Check if obj[key] exists and is an object
                        if (obj[key] && typeof obj[key] === 'object') {
                            // Use Object.entries to safely iterate
                            Object.entries(obj[key]).forEach(([_, series]) => {
                                // Check if series is an array
                                if (Array.isArray(series)) {
                                    series.forEach(dataPoint => {
                                        // Check if dataPoint is an array with at least one element
                                        if (Array.isArray(dataPoint) && dataPoint.length > 0) {
                                            timestamps.add(dataPoint[0]);
                                        }
                                    });
                                }
                            });
                        }
                    }
                    
                    // Create data map with timestamps
                    const dataMap = {};
                    Array.from(timestamps).sort().forEach(timestamp => {
                        dataMap[timestamp] = { timestamp };
                    });
                    
                    // Population phase - add values to the map
                    for (let key in obj) {
                        // Check if obj[key] exists and is an object
                        if (obj[key] && typeof obj[key] === 'object') {
                            Object.entries(obj[key]).forEach(([point, series]) => {
                                // Check if series is an array
                                if (Array.isArray(series)) {
                                    series.forEach(dataPoint => {
                                        // Check if dataPoint is an array with at least two elements
                                        if (Array.isArray(dataPoint) && dataPoint.length > 1) {
                                            const timestamp = dataPoint[0];
                                            const value = dataPoint[1];
                                            if (dataMap[timestamp]) {
                                                dataMap[timestamp][point] = value;
                                            }
                                        }
                                    });
                                }
                            });
                        }
                    }
                    
                    // Convert to array and sort
                    const rowData = Object.values(dataMap);
                    rowData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
                    
                    self.postMessage({
                        status: 'complete',
                        rowData: rowData
                    });
                };
            `;
            
            const blob = new Blob([workerCode], { type: 'application/javascript' });
            const workerUrl = URL.createObjectURL(blob);
            window.gridDataWorker = new Worker(workerUrl);
            URL.revokeObjectURL(workerUrl);
            
            // Set up progress reporting
            window.gridDataWorker.onmessage = function(e) {
                if (e.data.status === 'progress') {
                    updateLoadingStatus(loadingIndicator, e.data.message);
                } else if (e.data.status === 'error') {
                    console.error('Worker error:', e.data.message);
                    updateLoadingStatus(loadingIndicator, `Error: ${e.data.message}`, true);
                }
            };
        } catch (error) {
            console.warn("Web Worker creation failed, falling back to main thread processing", error);
        }
    }
    
    // Process data with worker or fallback to main thread
    if (window.gridDataWorker) {
        updateLoadingStatus(loadingIndicator, 'Preparing data for grid...');
        
        // Create column definitions
        const cols = [];
        cols.push({ 
            field: "timestamp", 
            filter: true, 
            filter: 'agDateColumnFilter', 
            cellStyle: { textAlign: 'center' },
            filterParams: { 
                comparator: (filterLocalDateAtMidnight, cellValue) => {
                    const cellDate = new Date(cellValue);
                    if (cellDate < filterLocalDateAtMidnight) {
                        return -1;
                    } else if (cellDate > filterLocalDateAtMidnight) {
                        return 1;
                    }
                    return 0;
                },
                filterOptions: ['greaterThan', 'lessThan', 'inRange'],
                maxNumConditions: 1
            }
        });
        for (let key in seriesData) {
            for (let title in seriesData[key]) {
                let keyName = keymap[title];
                cols.push({
                    field: title, 
                    headerName: keyName, 
                    filter: true,
                    cellStyle: { textAlign: 'center' },
                    valueFormatter: (params) => {
                        if (params.value !== undefined && params.value !== null) {
                            const numValue = typeof params.value === 'number' ? 
                                params.value : params.value;
                            
                            if (!isNaN(numValue)) {
                                return numValue;
                            }
                        }
                        return params.value !== undefined ? String(params.value) : '';
                    }
                });
            }
        }
        
        // Set column definitions first
        gridApi.setGridOption("columnDefs", cols);
        console.log(gridApi)
        // Create a copy of the data for the worker
        const seriesDataCopy = JSON.parse(JSON.stringify(seriesData));
        
        // Debug log to check data structure
        console.log("Sending data to worker:", seriesDataCopy);
        
        // Set up one-time response handler for this specific request
        const messageHandler = function(e) {
            if (e.data.status === 'complete') {
                // Update grid with the processed data
                gridApi.setGridOption("rowData", e.data.rowData);
                
                // Force grid refresh
                setTimeout(() => {
                    gridApi.refreshCells({ force: true });
                    updateLoadingStatus(loadingIndicator, 'Data loaded successfully!', true);
                }, 100);
                
                  // After setting row data and refreshing cells
                setTimeout(() => {
                    gridApi.refreshCells({ force: true });
                    updateRowCount(gridApi);
                }, 100);

                // Remove this specific handler
                window.gridDataWorker.removeEventListener('message', messageHandler);
            } else if (e.data.status === 'error') {
                console.error('Worker error:', e.data.message);
                updateLoadingStatus(loadingIndicator, `Error: ${e.data.message}`, true);
                
                // Remove this specific handler
                window.gridDataWorker.removeEventListener('message', messageHandler);
                
                // Fall back to main thread processing
                processFallback();
            }
        };
        
        window.gridDataWorker.addEventListener('message', messageHandler);
        
        // Send data to worker
        window.gridDataWorker.postMessage(seriesDataCopy);
    } else {
        // Fallback to main thread processing
        processFallback();
    }
    
    // Fallback processing function
    function processFallback() {
        updateLoadingStatus(loadingIndicator, 'Processing data (fallback mode)...');
        
        // Function to transform the object into AG Grid row data
        function transformToRowData(obj) {
            let rowData = [];
            let timestamps = new Set();
            
            updateLoadingStatus(loadingIndicator, 'Collecting timestamps...');
            // Collect all unique timestamps with safety checks
            for (let key in obj) {
                if (obj[key] && typeof obj[key] === 'object') {
                    Object.values(obj[key]).forEach(series => {
                        if (Array.isArray(series)) {
                            series.forEach(dataPoint => {
                                if (Array.isArray(dataPoint) && dataPoint.length > 0) {
                                    timestamps.add(dataPoint[0]);
                                }
                            });
                        }
                    });
                }
            }
            
            updateLoadingStatus(loadingIndicator, 'Creating data structure...');
            // Create a map for easy lookup
            let dataMap = {};
            timestamps.forEach(timestamp => {
                dataMap[timestamp] = { timestamp };
            });
            
            updateLoadingStatus(loadingIndicator, 'Populating values...');
            // Populate the map with values from each point
            for (let key in obj) {
                if (obj[key] && typeof obj[key] === 'object') {
                    Object.entries(obj[key]).forEach(([point, series]) => {
                        if (Array.isArray(series)) {
                            series.forEach(dataPoint => {
                                if (Array.isArray(dataPoint) && dataPoint.length > 1) {
                                    const timestamp = dataPoint[0];
                                    const value = dataPoint[1];
                                    if (dataMap[timestamp]) {
                                        dataMap[timestamp][point] = value;
                                    }
                                }
                            });
                        }
                    });
                }
            }
            
            // Convert the map to an array of row data
            rowData = Object.values(dataMap);
            
            updateLoadingStatus(loadingIndicator, 'Sorting data...');
            // Sort by timestamp
            rowData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            return rowData;
        }
        
        // Create column definitions
        const cols = [];
        cols.push({ 
            field: "timestamp", 
            filter: true, 
            filter: 'agDateColumnFilter', 
            cellStyle: { textAlign: 'center' },
            filterParams: { 
                comparator: (filterLocalDateAtMidnight, cellValue) => {
                    const cellDate = new Date(cellValue);
                    if (cellDate < filterLocalDateAtMidnight) {
                        return -1;
                    } else if (cellDate > filterLocalDateAtMidnight) {
                        return 1;
                    }
                    return 0;
                },
                filterOptions: ['greaterThan', 'lessThan', 'inRange'],
                maxNumConditions: 1
            }
        });
        
        for (let key in seriesData) {
            for (let title in seriesData[key]) {
                let keyName = keymap[title];
                cols.push({
                    field: title, 
                    headerName: keyName, 
                    filter: true,
                    cellStyle: { textAlign: 'center' },
                    valueFormatter: (params) => {
                        if (params.value !== undefined && params.value !== null) {
                            const numValue = typeof params.value === 'number' ? 
                                params.value : params.value;
                            
                            if (!isNaN(numValue)) {
                                return numValue;
                            }
                        }
                        return params.value !== undefined ? String(params.value) : '';
                    }
                });
            }
        }
        
        // Transform the object to row data
        let gridRowData = transformToRowData(seriesData);
        
        // Update grid
        gridApi.setGridOption("columnDefs", cols);
        gridApi.setGridOption("rowData", gridRowData);
        
        // Force grid refresh
        setTimeout(() => {
            gridApi.refreshCells({ force: true });
            updateLoadingStatus(loadingIndicator, 'Data loaded successfully!', true);
        }, 100);
    }
}

// Update row count display
function updateRowCount(api) {
  const rowCount = api.getDisplayedRowCount();
  document.getElementById('rowCount').textContent = `Righe totali: ${rowCount}`;
}


// Initialize event listeners for statistics
function initStatsListeners() {
  const calculateButton = document.getElementById('calculateStats');
  const restoreButton = document.getElementById('restoreData');
  const calculateButtonMobile = document.getElementById('calculateStatsMobile');
  const restoreButtonMobile = document.getElementById('restoreDataMobile');

  calculateButton.addEventListener('click', () => {
    const period = document.getElementById('statsDropdown').value ;
    const aggFunction = document.getElementById('aggFunction').value;
    
    if (!period) {
      alert('Selezionare un tipo di statistica');
      return;
    }
    
    calculateDuckDBStatistics(period, aggFunction);
  });
  
  restoreButton.addEventListener('click', () => {
    loadHistoricalDataToGrid(gridApi);
  });
    
  calculateButtonMobile.addEventListener('click', () => {
    const period = document.getElementById('statsDropdownMobile').value;
    const aggFunction = document.getElementById('aggFunctionMobile').value;
    
    if (!period) {
      alert('Selezionare un tipo di statistica');
      return;
    }
    
    calculateDuckDBStatistics(period, aggFunction);
  });
  
  restoreButtonMobile.addEventListener('click', () => {
    loadHistoricalDataToGrid(gridApi);
  });

}

// Calculate statistics and display in the main grid
async function calculateDuckDBStatistics(period, aggFunction) {
  const loadingIndicator = document.getElementById('data-processing-indicator');
  updateLoadingStatus(loadingIndicator, 'Calcolo statistiche...');
  
  try {
    // Get the DuckDB connection
    const conn = window.duckdb;
    if (!conn) {
      throw new Error('DuckDB connection not available');
    }
    
    // Get all distinct tags from the historical data
    const tagsQuery = `SELECT DISTINCT tag FROM historical_data`;
    const tagsResult = await conn.query(tagsQuery);
    const tags = tagsResult.toArray().map(row => row.tag);
    
    if (tags.length === 0) {
      throw new Error('No historical data available for analysis');
    }
    
    console.log('Tags available for analysis:', tags);
    
    // Build the time bucket expression based on the selected period
    let timeBucketExpr;
    let periodLabel;
    
    switch (period) {
      case 'hourly':
        timeBucketExpr = "strftime(ts, '%Y-%m-%d %H:00:00')";
        periodLabel = 'Hour';
        break;
      case 'daily':
        timeBucketExpr = "strftime(ts, '%Y-%m-%d')";
        periodLabel = 'Day';
        break;
      case 'weekly':
        // DuckDB doesn't have a direct week function, so we'll use a workaround
        timeBucketExpr = "strftime(date_trunc('week', ts), '%Y-%m-%d')";
        periodLabel = 'Week';
        break;
      case 'monthly':
        timeBucketExpr = "strftime(ts, '%Y-%m')";
        periodLabel = 'Month';
        break;
      case 'yearly':
        timeBucketExpr = "strftime(ts, '%Y')";
        periodLabel = 'Year';
        break;
      default:
        timeBucketExpr = "strftime(ts, '%Y-%m-%d')";
        periodLabel = 'Day';
    }
    
    updateLoadingStatus(loadingIndicator, 'Costruzione della query di aggregazione...');
    
    // Create a list of CASE expressions for each tag
    const caseExpressions = tags.map(tag => `
      ${aggFunction}(CASE WHEN tag = '${tag}' THEN value END) AS "${tag}"
    `).join(',\n      ');
    
    // Build the final query
    const finalQuery = `
      SELECT 
        ${timeBucketExpr} AS timestamp,
        ${caseExpressions}
      FROM historical_data
      GROUP BY timestamp
      ORDER BY timestamp
    `;
    
    console.log('Executing DuckDB query:', finalQuery);
    updateLoadingStatus(loadingIndicator, 'Esecuzione della query di aggregazione...');
    // Execute the query
    const result = await conn.query(finalQuery);
    const statsData = result.toArray();
    
    console.log('DuckDB statistics result:', statsData);
    
    // Create column definitions for the grid
    const cols = [];
    cols.push({ 
      field: "timestamp", 
      //headerName: periodLabel,
      filter: true, 
      filter: 'agDateColumnFilter', 
      cellStyle: { textAlign: 'center' },
      filterParams: { 
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = new Date(cellValue);
          if (cellDate < filterLocalDateAtMidnight) {
            return -1;
          } else if (cellDate > filterLocalDateAtMidnight) {
            return 1;
          }
          return 0;
        },
        filterOptions: ['greaterThan', 'lessThan', 'inRange'],
        maxNumConditions: 1
      }
    });
    
    // Add a column for each tag
    for (const tag of tags) {
      cols.push({
        field: tag, 
        headerName: `${keymap[tag] || tag} (${aggFunction})`, 
        filter: true,
        cellStyle: { textAlign: 'center' },
        valueFormatter: (params) => {
          if (params.value !== undefined && params.value !== null) {
            const numValue = typeof params.value === 'number' ? 
              params.value : params.value;
            
            if (!isNaN(numValue)) {
              return numValue;
            }
          }
          return params.value !== undefined ? String(params.value) : '';
        }
      });
    }
    
    // Update grid
    gridApi.setGridOption("columnDefs", cols);
    gridApi.setGridOption("rowData", statsData);
    
    // Force grid refresh
    setTimeout(() => {
      gridApi.refreshCells({ force: true });
      updateLoadingStatus(loadingIndicator, `${aggFunction} by ${periodLabel} loaded successfully!`, true);
      updateRowCount(gridApi);
    }, 100);

    // After updating the grid, update the chart
    setTimeout(() => {
        updateChartFromGrid(gridApi);
    }, 200);
    
  } catch (error) {
    console.error('Error calculating statistics with DuckDB:', error);
    updateLoadingStatus(loadingIndicator, `Error: ${error.message}`, true);
  }
}

// Function to load data from historical_data table to the main grid
async function loadHistoricalDataToGrid(gridApi) {
  const loadingIndicator = document.getElementById('data-processing-indicator');
  updateLoadingStatus(loadingIndicator, 'Caricamento dati storici...');
  
  try {
    const conn = window.duckdb;
    if (!conn) {
      throw new Error('DuckDB connection not available');
    }
    
    // Get all distinct tags
    const tagsQuery = `SELECT DISTINCT tag FROM historical_data`;
    const tagsResult = await conn.query(tagsQuery);
    const tags = tagsResult.toArray().map(row => row.tag);
    
    if (tags.length === 0) {
      updateLoadingStatus(loadingIndicator, 'Non ci sono dati storici disponibili', true);
      return;
    }
    
    // Get record count to determine if downsampling is needed
    const countQuery = `SELECT COUNT(*) as count FROM historical_data`;
    const countResult = await conn.query(countQuery);
    const recordCount = countResult.toArray()[0].count;
    
    console.log(`Historical data contains ${recordCount} records`);
    
    // Create column definitions
    const cols = [];
    cols.push({ 
      field: "timestamp", 
      headerName: "Timestamp",
      filter: true, 
      filter: 'agDateColumnFilter', 
      cellStyle: { textAlign: 'center' },
      filterParams: { 
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = new Date(cellValue);
          if (cellDate < filterLocalDateAtMidnight) {
            return -1;
          } else if (cellDate > filterLocalDateAtMidnight) {
            return 1;
          }
          return 0;
        },
        filterOptions: ['greaterThan', 'lessThan', 'inRange'],
        maxNumConditions: 1
      }
    });
    
    // Add a column for each tag
    for (const tag of tags) {
      cols.push({
        field: tag, 
        headerName: keymap[tag] || tag, 
        filter: true,
        cellStyle: { textAlign: 'center' },
        valueFormatter: (params) => {
          if (params.value !== undefined && params.value !== null) {
            const numValue = typeof params.value === 'number' ? 
              params.value : params.value;
            
            if (!isNaN(numValue)) {
              return numValue;
            }
          }
          return params.value !== undefined ? String(params.value) : '';
        }
      });
    }
    
    // Determine if we need to downsample based on record count
    const needsDownsampling = recordCount > 100000; // Threshold for downsampling
    
    updateLoadingStatus(loadingIndicator, needsDownsampling ? 
      'Downsampling dei dati per la visualizzazione...' : 'Preparazione dei dati per la visualizzazione...');
    
    let pivotQuery;
    
    if (needsDownsampling) {
      // Downsampling approach - use time buckets or row sampling
      try {
        // Try time-based bucketing first (more accurate)
        pivotQuery = `
          WITH time_buckets AS (
            SELECT 
              tag,
              time_bucket(INTERVAL '${aggregateTime} min', ts) as bucket_time,
              AVG(value) as avg_value
            FROM historical_data
            GROUP BY tag, bucket_time
            ORDER BY tag, bucket_time
          ),
          all_buckets AS (
            SELECT DISTINCT bucket_time FROM time_buckets
          )
          SELECT 
            strftime('%Y-%m-%d %H:%M:%S', b.bucket_time) as timestamp,
            ${tags.map(tag => `
              MAX(CASE WHEN t.tag = '${tag}' THEN t.avg_value END) as "${tag}"
            `).join(',\n            ')}
          FROM all_buckets b
          LEFT JOIN time_buckets t ON b.bucket_time = t.bucket_time
          GROUP BY b.bucket_time
          ORDER BY b.bucket_time
        `;
      } catch (e) {
        console.warn("Advanced time bucketing failed, using simpler approach", e);
        
        // Fallback to row number based sampling
        pivotQuery = `
          WITH row_numbers AS (
            SELECT 
              tag,
              ts,
              value,
              ROW_NUMBER() OVER (PARTITION BY tag ORDER BY ts) as rn
            FROM historical_data
          ),
          sampled_rows AS (
            SELECT 
              tag,
              ts,
              value
            FROM row_numbers
            WHERE rn % ${Math.ceil(recordCount / 10000)} = 0
          ),
          all_timestamps AS (
            SELECT DISTINCT ts FROM sampled_rows ORDER BY ts
          )
          SELECT 
            strftime('%Y-%m-%d %H:%M:%S', t.ts) as timestamp,
            ${tags.map(tag => `
              MAX(CASE WHEN s.tag = '${tag}' THEN s.value END) as "${tag}"
            `).join(',\n            ')}
          FROM all_timestamps t
          LEFT JOIN sampled_rows s ON t.ts = s.ts
          GROUP BY t.ts
          ORDER BY t.ts
        `;
      }
    } else {
      // No downsampling needed - use all data points
      pivotQuery = `
        WITH all_timestamps AS (
          SELECT DISTINCT ts FROM historical_data ORDER BY ts
        )
        SELECT 
          strftime('%Y-%m-%d %H:%M:%S', t.ts) as timestamp,
          ${tags.map(tag => `
            MAX(CASE WHEN h.tag = '${tag}' THEN h.value END) as "${tag}"
          `).join(',\n          ')}
        FROM all_timestamps t
        LEFT JOIN historical_data h ON t.ts = h.ts
        GROUP BY t.ts
        ORDER BY t.ts
      `;
    }
    
    console.log('Executing pivot query:', pivotQuery);
    
    // Execute the query
    const result = await conn.query(pivotQuery);
    const rowData = result.toArray();
    
    console.log(`Loaded ${rowData.length} rows from historical data`);
    
    // Update grid
    gridApi.setGridOption("columnDefs", cols);
    gridApi.setGridOption("rowData", rowData);
    
    // Force grid refresh
    setTimeout(() => {
      gridApi.refreshCells({ force: true });
      updateLoadingStatus(loadingIndicator, 'Historical data loaded successfully!', true);
      updateRowCount(gridApi);
    }, 100);

  setTimeout(() => {
    updateChartFromGrid(gridApi);
  }, 200);    
    
  } catch (error) {
    console.error('Error loading historical data to grid:', error);
    updateLoadingStatus(loadingIndicator, `Error: ${error.message}`, true);
  }
}

// Helper function to create a loading indicator
function createLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'data-processing-indicator';
    indicator.className = 'fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-md shadow-lg z-50 transition-opacity duration-300';
    indicator.style.opacity = '0.9';
    indicator.innerHTML = `
        <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span id="loading-status-text">Processing data...</span>
        </div>
    `;
    document.body.appendChild(indicator);
    return indicator;
}

// Helper function to update loading status
function updateLoadingStatus(indicator, message, hide = false) {
  if (indicator) {
    if (hide) {
      indicator.classList.add('hidden');
    } else {
      indicator.classList.remove('hidden');
      const messageElement = indicator.querySelector('p');
      if (messageElement) {
        messageElement.textContent = message;
      }
    }
  }
}


// Helper function to fetch with progress
async function fetchWithProgress(url, progressCallback) {
    const response = await fetch(url);
    
    // If we can't track progress, return the response directly
    if (!response.body || !response.headers.get('content-length')) {
        return response;
    }
    
    // Get total size
    const contentLength = response.headers.get('content-length');
    const total = parseInt(contentLength, 10);
    
    // Create a new ReadableStream to track progress
    const reader = response.body.getReader();
    let receivedLength = 0;
    const stream = new ReadableStream({
        start(controller) {
            function push() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        controller.close();
                        return;
                    }
                    
                    receivedLength += value.length;
                    progressCallback(receivedLength / total);
                    controller.enqueue(value);
                    push();
                }).catch(error => {
                    console.error('Error during fetch:', error);
                    controller.error(error);
                });
            }
            push();
        }
    });
    
    // Create a new response with the tracked body
    return new Response(stream, {
        headers: response.headers,
        status: response.status,
        statusText: response.statusText
    });
}

// Function to load chart with data
function loadChart(chartData, index) {
  try {
    // Get chart element
    const chartId = `chart-timeline-${index}`;
    const chartElement = document.getElementById(chartId);
    
    if (!chartElement) {
      console.error(`Chart element with ID ${chartId} not found`);
      return;
    }
    
    // Check if we need to dispose and recreate the chart
    let myChart = echarts.getInstanceByDom(chartElement);
    let needsRecreation = false;
    
    // Check if chart has errors or is in an invalid state
    if (myChart) {
      try {
        // Try to get current options - if this fails, the chart is in a bad state
        const currentOptions = myChart.getOption();
        if (!currentOptions || !currentOptions.series || currentOptions.series.length === 0) {
          needsRecreation = true;
        }
      } catch (e) {
        console.warn("Chart appears to be in an invalid state, recreating...", e);
        needsRecreation = true;
      }
    }
    
    // Dispose and recreate chart if needed
    if (!myChart || needsRecreation) {
      if (myChart) {
        try {
          // Try to dispose the chart properly
          myChart.dispose();
        } catch (e) {
          console.warn("Error disposing chart:", e);
        }
      }
      
      // Create a new chart instance
      let myChart = chartInit(chartId);
      
      console.log(`Chart ${chartId} recreated`);
    }
    
    // Prepare series data for the chart
    const series = [];
    
    // Process each tag in chartData
    for (const tag in chartData) {
      if (chartData.hasOwnProperty(tag) && Array.isArray(chartData[tag]) && chartData[tag].length > 0) {
        // Get display name from keymap if available
        const displayName = keymap[tag] || tag;
        
        // Add series for this tag
        series.push({
          tag: tag,
          name: displayName,
          dataType: 'AIN', // Assuming analog input type
          data: chartData[tag],
          type: 'line',
          sampling: 'lttb',
          smoothMonotone: 'x',
          symbol: 'none',
          lineStyle: {
            width: 1.5
          },
          emphasis: {
            focus: 'series'
          }
        });
      }
    }
    
    // If no valid series data, show a message
    if (series.length === 0) {
      console.warn(`No valid series data for chart ${chartId}`);
      
      // Set a simple option to show a message
      myChart.setOption({
        title: {
          text: 'No data available',
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#999'
          }
        },
        series: []
      });
      
      return myChart;
    }
    
    // Set chart options
    const option = {
        grid: {
            containLabel: true
        },
        tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            type: 'cross',
            label: {
            backgroundColor: '#6a7985'
            }
        },    
        position: function (pt) {
            return [pt[0], '10%'];
        }
        },
        legend: {
        //data: ['Legend A', 'Legend B', 'Legend C'],
        //backgroundColor: '#ccc',
        textStyle: {
            color: '#777'
            // ...
        }
        // ...
        },
        toolbox: {
        feature: {
            dataZoom: {
            yAxisIndex: 'none'
            },
            //restore: {},
            saveAsImage: {},
            magicType: {show: true, type: ['line', 'bar']},
        }
        },
        xAxis: {
        type: 'time',
        boundaryGap: false,
        },
        yAxis: {
        type: 'value',
        boundaryGap: ['50%', '100%'],
        splitLine: {
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        
        dataZoom: [
        {
            type: 'inside',
            start: 0,
            end: 100
        },
        {
            start: 0,
            end: 100
        }
        ],
      series: series
    };
    
    // Apply options to chart with clear flag to completely reset the chart
    myChart.clear();
    myChart.setOption(option, true);
    
    // Remove old event listeners to prevent memory leaks
    const oldResizeListener = chartElement._echarts_resize_listener;
    if (oldResizeListener) {
      window.removeEventListener('resize', oldResizeListener);
    }
    
    // Add new resize listener
    const resizeListener = function() {
      if (myChart && !myChart.isDisposed()) {
        myChart.resize();
      }
    };
    window.addEventListener('resize', resizeListener);
    chartElement._echarts_resize_listener = resizeListener;
    
    // Update series dropdown if it exists
    if (typeof updateSeriesDropdown === 'function') {
      try {
        updateSeriesDropdown(myChart.getOption());
      } catch (e) {
        console.warn("Error updating series dropdown:", e);
      }
    }
    
    // Check if multiple axis are enabled
    let toggleYaxis = document.getElementById('toggle-yaxis');
    if (toggleYaxis && toggleYaxis.checked && typeof toggleYAxis === 'function') {
      try {
        toggleYAxis(toggleYaxis, index);
      } catch (e) {
        console.warn("Error toggling Y axis:", e);
      }
    }
    
    return myChart;
  } catch (error) {
    console.error('Error loading chart:', error);
    return null;
  }
}

// Function to update chart from grid data
function updateChartFromGrid(gridApi, index = 1) {
  try {
    // Get all rows from the grid
    const rowData = [];
    gridApi.forEachNode(node => {
      if (node.data) {
        rowData.push(node.data);
      }
    });
    
    if (rowData.length === 0) {
      console.warn("No data in grid to update chart");
      return;
    }
    
    // Get column definitions to identify tags
    const columnDefs = gridApi.getColumnDefs();
    const tagColumns = columnDefs.filter(col => col.field !== 'timestamp');
    
    // Prepare series data for the chart
    const chartData = {};
    
    // Process each tag column
    tagColumns.forEach(column => {
      const tag = column.field;
      
      // Skip non-tag columns
      if (tag === 'timestamp' || !tag) return;
      
      // Extract data for this tag
      const data = rowData.map(row => {
        // Get the value and handle BigInt conversion
        let value = row[tag];
        
        // Convert BigInt to Number if needed
        if (typeof value === 'bigint') {
          // Check if the BigInt is within safe integer range
          if (value <= Number.MAX_SAFE_INTEGER && value >= Number.MIN_SAFE_INTEGER) {
            value = Number(value);
          } else {
            // For very large numbers, convert to string to preserve precision
            value = value.toString();
            // Then convert back to number (will lose precision but prevent errors)
            value = parseFloat(value);
          }
        }
        
        return [
          row.timestamp,
          value !== null && value !== undefined ? value : null
        ];
      }).filter(item => item[1] !== null); // Filter out null values
      
      // Only add if we have data
      if (data.length > 0) {
        chartData[tag] = data;
      }
    });
    
    // Check if we have any data to chart
    if (Object.keys(chartData).length === 0) {
      console.warn("No valid data found in grid for charting");
      return;
    }
    
    // Update seriesData for the chart
    if (!seriesData[index]) {
      seriesData[index] = {};
    }
    
    // Update seriesData with the new data
    Object.keys(chartData).forEach(tag => {
      seriesData[index][tag] = chartData[tag];
    });
    
    // Use loadChart function to update the chart
    loadChart(seriesData[index], index);
    
  } catch (error) {
    console.error("Error updating chart from grid:", error);
  }
}
</script>

<script>
  var getDb = async () => {
    const duckdb = window.duckdbduckdbWasm;
    // @ts-ignore
    if (window._db) return window._db;
    const JSDELIVR_BUNDLES = duckdb.getJsDelivrBundles();

    // Select a bundle based on browser checks
    const bundle = await duckdb.selectBundle(JSDELIVR_BUNDLES);

    const worker_url = URL.createObjectURL(
      new Blob([`importScripts("${bundle.mainWorker}");`], {
        type: "text/javascript",
      })
    );

    // Instantiate the asynchronus version of DuckDB-wasm
    const worker = new Worker(worker_url);
    // const logger = null //new duckdb.ConsoleLogger();
    const logger = new duckdb.ConsoleLogger();
    const db = new duckdb.AsyncDuckDB(logger, worker);
    await db.instantiate(bundle.mainModule, bundle.pthreadWorker);
    URL.revokeObjectURL(worker_url);
    window._db = db;
    return db;
  }
var conn; 
// Advanced data handling for large parquet files (10M+ records)
async function readdb(key, tagname) {
    let spinner = document.getElementById('spinner');
    spinner.classList.remove('htmx-indicator');
    
    const loadingIndicator = document.getElementById('data-processing-indicator');
    updateLoadingStatus(loadingIndicator, 'Initializing data fetch...');
    
    try {
        keymap[key] = tagname;
        keymap[key.replace('::','')] = tagname;
        const filename = key.replace('::','');
        
        updateLoadingStatus(loadingIndicator, 'Connessione al database...');
        
        const db = await getDb();
        // Create a new connection
        conn = await db.connect();
    
        updateLoadingStatus(loadingIndicator, 'Il server sta preparando i dati...');
        
        // Fetch the parquet file with progress tracking
        const res = await fetchWithProgress(`bivalues/${timeframe}/${key}?chartIndex=1/result.parquet`, 
            (progress) => {
                updateLoadingStatus(loadingIndicator, `Scarico dati: ${Math.round(progress * 100)}%`);
            }
        );
        
        updateLoadingStatus(loadingIndicator, 'Lettura del file di archivio...');
        
        // Register the file with DuckDB
        const arrayBuffer = await res.arrayBuffer();
        await db.registerFileBuffer('result.parquet', new Uint8Array(arrayBuffer));
        
        // First, get data statistics to make smart decisions
        updateLoadingStatus(loadingIndicator, 'Analisi dei dati...');
        
        // Create a persistent table for all historical data
        await conn.query(`
        CREATE TABLE IF NOT EXISTS historical_data (
            tag VARCHAR,
            ts TIMESTAMP,
            value DOUBLE
        )
        `);
        // Insert into historical_data, ignoring duplicates
        await conn.query(`
        INSERT INTO historical_data
        SELECT DISTINCT * FROM "result.parquet"
        `);
        
        // Get count of records
        const countResult = await conn.query(`SELECT COUNT(*) as count FROM historical_data`);
        console.log(`Historical data table now has ${countResult.toArray()[0].count} records`);

        // Get record count and time range
        const statsQuery = `
            SELECT 
                COUNT(*) as record_count,
                MIN(ts) as min_time,
                MAX(ts) as max_time
            FROM historical_data
        `;
        
        const statsResult = await conn.query(statsQuery);
        const stats = statsResult.toArray()[0];
        const recordCount = stats.record_count;
        
        console.log(`Dataset contains ${recordCount} records from ${stats.min_time} to ${stats.max_time}`);
        
        // Determine if we need to downsample based on record count
        const needsDownsampling = recordCount > 1000000; // Threshold for downsampling
        
        // Prepare chart data with appropriate strategy
        updateLoadingStatus(loadingIndicator, needsDownsampling ? 
            'Downsampling dei dati per la visualizzazione...' : 'Preparazione dei dati per la visualizzazione...');
        
        let chartData;
        
        if (needsDownsampling) {
            // Strategy 1: Time-based bucketing for very large datasets
            const bucketQuery = `
                WITH time_buckets AS (
                    SELECT 
                        tag,
                        time_bucket(INTERVAL '${aggregateTime} min', ts) as bucket_time,
                        AVG(value) as avg_value,
                        MIN(value) as min_value,
                        MAX(value) as max_value
                    FROM historical_data
                    GROUP BY tag, bucket_time
                    ORDER BY tag, bucket_time
                )
                SELECT 
                    tag, 
                    strftime('%Y-%m-%d %H:%M:%S', bucket_time) as ts,
                    avg_value as value,
                    min_value,
                    max_value
                FROM time_buckets
            `;
            
            try {
                chartData = await conn.query(bucketQuery);
                console.log('Advanced time bucketing successful',bucketQuery);
            } catch (e) {
                // Fallback if time_bucket function is not available
                console.warn("Advanced time bucketing failed, using simpler approach", e);
                
                const simpleBucketQuery = `
                    WITH row_numbers AS (
                        SELECT 
                            tag,
                            ts,
                            value,
                            ROW_NUMBER() OVER (PARTITION BY tag ORDER BY ts) as rn
                        FROM historical_data
                    )
                    SELECT 
                        tag, 
                        strftime('%Y-%m-%d %H:%M:%S', ts) as ts,
                        value
                    FROM row_numbers
                    WHERE rn % ${Math.ceil(recordCount / 10000)} = 0
                    ORDER BY tag, ts
                `;
                
                chartData = await conn.query(simpleBucketQuery);
            }
        } else {
            // For smaller datasets, use all data points
            const query = `
                SELECT 
                    tag, 
                    strftime('%Y-%m-%d %H:%M:%S', ts) as ts,
                    value
                FROM historical_data
                ORDER BY tag, ts
            `;
            
            chartData = await conn.query(query);
        }
        
        updateLoadingStatus(loadingIndicator, 'Elaborazione dati grafico...');
        
        // Process chart data
        const results = chartData.toArray();
        let index = 1;
        if (seriesData.length == 0)
            seriesData[index] = {};
            
        // Process in batches to avoid UI freezing
        const batchSize = 5000;
        const totalBatches = Math.ceil(results.length / batchSize);
        
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const start = batchIndex * batchSize;
            const end = Math.min(start + batchSize, results.length);
            const batch = results.slice(start, end);
            
            updateLoadingStatus(loadingIndicator, `Elaborazione dati in batch ${batchIndex + 1}/${totalBatches}...`);
            
            // Process this batch
            for (let i = 0; i < batch.length; i++) {
                const result = batch[i];
                if (!seriesData[index].hasOwnProperty(result.tag)) {
                    seriesData[index][result.tag] = [];
                }
                seriesData[index][result.tag].push([result.ts, result.value]);
            }
            
            // Allow UI to breathe between batches
            await new Promise(resolve => setTimeout(resolve, 0));
        }
        
        updateLoadingStatus(loadingIndicator, 'Rendering del grafico...');
        
        // Prepare chart data with progressive rendering
        const chartOptions = {
            series: [],
            progressive: 500,  // Process 500 points per frame
            progressiveThreshold: 5000,  // Enable progressive rendering above 5000 points
            progressiveChunkMode: 'sequential'
        };
        
        // Add series data
        for (let i in seriesData[index]) {
            chartOptions.series.push({
                tag: i,
                name: keymap[i],
                dataType: 'AIN',
                data: seriesData[index][i],
                type: 'line',
                sampling: 'lttb',  // Use Largest-Triangle-Three-Buckets sampling
                smoothMonotone: 'x',
                symbol: 'none',
                lineStyle: {
                    width: 1.5  // Thinner lines render faster
                },
                emphasis: {
                    focus: 'series'
                }
            });
        }
        
        // Initialize and render chart
        let myChart = chartInit('chart-timeline-' + index);
        myChart.setOption(chartOptions);
        updateSeriesDropdown(myChart.getOption());
        
        // Now prepare grid data with virtual scrolling
        updateLoadingStatus(loadingIndicator, 'Preparing grid data...');
        
        // Call gridHandle to update the grid with the data
        await gridHandle(gridApi);
        
        // Check if multiple axis are enabled
        let toggleYaxis = document.getElementById('toggle-yaxis');
        if (toggleYaxis && toggleYaxis.checked)
            toggleYAxis(toggleYaxis, index);
            
        spinner.classList.add('htmx-indicator');
        updateLoadingStatus(loadingIndicator, 'Data loaded successfully!', true);
    } catch (error) {
        console.error("Error processing large dataset:", error);
        updateLoadingStatus(loadingIndicator, `Error: ${error.message}`, true);
        spinner.classList.add('htmx-indicator');
    }
    await conn.close();
}

/*
// Set up virtual scrolling grid for large datasets
async function setupVirtualGrid(gridApi, key, conn) {
    try {
        // First, get column information
        const columnQuery = `
            SELECT DISTINCT tag
            FROM historical_data
            ORDER BY tag
        `;
        
        const columnResult = await conn.query(columnQuery);
        const tags = columnResult.toArray().map(row => row.tag);
        
        // Create column definitions
        const columnDefs = [
            { 
                field: "timestamp", 
                headerName: "Timestamp",
                filter: true, 
                filterParams: {
                    filterOptions: ['equals', 'greaterThan', 'lessThan', 'inRange'],
                    comparator: (filterLocalDateAtMidnight, cellValue) => {
                        const cellDate = new Date(cellValue);
                        if (cellDate < filterLocalDateAtMidnight) {
                            return -1;
                        } else if (cellDate > filterLocalDateAtMidnight) {
                            return 1;
                        }
                        return 0;
                    }
                }
            }
        ];
        
        // Add a column for each tag
        tags.forEach(tag => {
            columnDefs.push({
                field: tag,
                headerName: keymap[tag] || tag,
                filter: 'agNumberColumnFilter',
                filterParams: {
                    filterOptions: ['equals', 'greaterThan', 'lessThan', 'inRange'],
                },
                valueFormatter: params => {
                    if (params.value !== undefined && params.value !== null) {
                        return parseFloat(params.value).toFixed(2);
                    }
                    return '';
                }
            });
        });
        
        // Configure the grid for server-side row model
        const gridOptions = {
            columnDefs: columnDefs,
            rowModelType: 'serverSide',
            serverSideStoreType: 'partial',
            cacheBlockSize: 200,
            maxBlocksInCache: 10,
            purgeClosedRowNodes: true,
            maxConcurrentDatasourceRequests: 2,
            blockLoadDebounceMillis: 100
        };
        
        // Apply grid options
        gridApi.setGridOption(gridOptions);
        
        // Create server-side datasource
        const dataSource = createServerSideDatasource(conn);
        gridApi.setServerSideDatasource(dataSource);
    } catch (error) {
        console.error("Error setting up virtual grid:", error);
        throw error;
    }
}

// Create a server-side datasource for AG Grid
function createServerSideDatasource(conn) {
    return {
        getRows: async (params) => {
            try {
                console.log('AG Grid requesting rows:', params.request);
                
                // Extract request details
                const startRow = params.request.startRow;
                const endRow = params.request.endRow;
                const pageSize = endRow - startRow;
                
                // Build SQL query based on sort and filter model
                let whereClause = '';
                let orderByClause = 'ORDER BY ts';
                
                // Handle filtering
                if (params.request.filterModel) {
                    const filters = [];
                    
                    for (const [field, filter] of Object.entries(params.request.filterModel)) {
                        if (field === 'timestamp') {
                            // Handle date filtering
                            if (filter.type === 'greaterThan') {
                                filters.push(`ts > '${filter.dateFrom}'`);
                            } else if (filter.type === 'lessThan') {
                                filters.push(`ts < '${filter.dateTo}'`);
                            } else if (filter.type === 'inRange') {
                                filters.push(`ts BETWEEN '${filter.dateFrom}' AND '${filter.dateTo}'`);
                            }
                        } else {
                            // Handle numeric filtering
                            if (filter.type === 'equals') {
                                filters.push(`${field} = ${filter.filter}`);
                            } else if (filter.type === 'greaterThan') {
                                filters.push(`${field} > ${filter.filter}`);
                            } else if (filter.type === 'lessThan') {
                                filters.push(`${field} < ${filter.filter}`);
                            } else if (filter.type === 'inRange') {
                                filters.push(`${field} BETWEEN ${filter.filter} AND ${filter.filterTo}`);
                            }
                        }
                    }
                    
                    if (filters.length > 0) {
                        whereClause = `WHERE ${filters.join(' AND ')}`;
                    }
                }
                
                // Handle sorting
                if (params.request.sortModel && params.request.sortModel.length > 0) {
                    const sortParts = params.request.sortModel.map(model => {
                        return `${model.colId} ${model.sort.toUpperCase()}`;
                    });
                    orderByClause = `ORDER BY ${sortParts.join(', ')}`;
                }
                
                // Build the pivot query to transform the data
                const query = `
                    WITH numbered_rows AS (
                        SELECT 
                            ts,
                            tag,
                            value,
                            ROW_NUMBER() OVER (${orderByClause}) as row_num
                        FROM historical_data
                        ${whereClause}
                    ),
                    distinct_timestamps AS (
                        SELECT DISTINCT 
                            ts,
                            row_num
                        FROM numbered_rows
                        WHERE row_num BETWEEN ${startRow + 1} AND ${endRow}
                        ORDER BY row_num
                    )
                    SELECT 
                        dt.ts as timestamp,
                        ${params.request.columnDefs
                            .filter(col => col.field !== 'timestamp')
                            .map(col => `MAX(CASE WHEN nr.tag = '${col.field}' THEN nr.value END) as "${col.field}"`)
                            .join(',\n')}
                    FROM distinct_timestamps dt
                    LEFT JOIN numbered_rows nr ON dt.ts = nr.ts
                    GROUP BY dt.ts, dt.row_num
                    ORDER BY dt.row_num
                    LIMIT ${pageSize}
                `;
                
                console.log('Executing query:', query);
                
                // Execute the query
                const result = await conn.query(query);
                const rowData = result.toArray();
                
                // Get total count for pagination
                const countQuery = `
                    SELECT COUNT(DISTINCT ts) as total
                    FROM historical_data
                    ${whereClause}
                `;
                
                const countResult = await conn.query(countQuery);
                const totalCount = countResult.toArray()[0].total;
                await conn.close();
                // Return the results to AG Grid
                params.success({
                    rowData: rowData,
                    rowCount: totalCount
                });
            } catch (error) {
                console.error('Error fetching grid data:', error);
                params.fail();
            }
        }
    };
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}
*/
</script>

<script type="module">
  // Get the current date and time
  import * as duckdbduckdbWasm from "https://cdn.jsdelivr.net/npm/@duckdb/duckdb-wasm@1.29.0/+esm";
  window.duckdbduckdbWasm = duckdbduckdbWasm;
  getDb().then(async (db) => {
    // Create a new connection
    window.duckdb = await db.connect();
    console.log('DB initilized')       
  });
</script>

<script>
  var seriesDropdown = document.getElementById('charts-list-' + getChartsIndex());

  function updateSeriesDropdown(chart) {
    seriesDropdown.innerHTML = ''; // Clear existing options
    chart.series.forEach(series => {
      let option = document.createElement('li');
      let html =` 
              <div class="flex items-center p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                <input id="tag-${series.tag}" type="checkbox" value="${series.tag}" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                <label for="tag-${series.tag}" class="w-full ms-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">${series.name}</label>
              </div>
              `
      option.innerHTML = html;  
      seriesDropdown.appendChild(option);
    });
  }

  // Update dropdown whenever series are added or removed
 function deleteSeries(id) {
    let chartId = `chart-timeline-${id}` 
    let listId = `charts-list-${id}`
    let myChart = echarts.init(document.getElementById(chartId));
    let chartList = document.getElementById(listId)
    let seriesChoices = chartList.querySelectorAll('input');
    seriesChoices.forEach(choice => {
      if(choice.checked){
        let seriesName = choice.checked;
        let option = myChart.getOption();
        option.series = option.series.filter(function (serie) {
              if (serie.tag == choice.value)
                 delete seriesData[parseInt(id)][serie.tag]
              return serie.tag !== choice.value;
          });
          myChart.setOption(option, true);
          gridHandle(gridApi)
          updateSeriesDropdown(myChart.getOption());
      }
    });
  }

  //update the y-axis
  function updateYAxis(e,id) {
    const min = document.getElementById(`chart-${id}-min`)
    const max = document.getElementById(`chart-${id}-max`)
    const button = document.getElementById(`axisSettingsToggle-${id}`)
    let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
    if (e.checked ){
       
      button.classList.remove('text-gray-800','dark:text-white')
      button.classList.add('text-red-500')

      myChart.setOption({
                  yAxis: {
                      min: min.value,
                      max: max.value
                  }
            });
      }else{
        button.classList.add('text-gray-800','dark:text-white')
        button.classList.remove('text-red-500')
        myChart.setOption({yAxis: { min: undefined,max: undefined}});
      }
    }

  function enableAvg(e,id) {
    let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
    let option = myChart.getOption();
    let updatedSeries;
    if (e.checked ){       
      updatedSeries = option.series.map(function(serie) {
                return {
                    ...serie,
                    markPoint: {
                        data: [
                            { type: 'min', name: 'Min' },
                            { type: 'max', name: 'Max' },
                            { type: 'average', name: 'Avg' }
                        ]
                    }
                };
            });

            myChart.setOption({
                series: updatedSeries
            });
      }else{
        updatedSeries = option.series.map(function(serie) {
                return {
                    ...serie,
                    markPoint: { data: [] }
                };
            });

            myChart.setOption({
                series: updatedSeries
            });
      }
    }

    function toggleYAxis(e,id) {
        let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
        let option = myChart.getOption();
        var currentYAxisCount = option.yAxis.length;
        if (e.checked ){
          
            // Update to multiple y-axes with series names
            var updatedYAxis = option.series.map((serie, index) => {
                return {
                    type: 'value',
                    //name: serie.name,
                    position: 'left',
                    alignTicks: true,
                    axisLine: { /*show: true,*/lineStyle: { color: option.color[index] } },
                    axisLabel: { show: true, color: option.color[index] },
                    offset: index * 30 // Offset each y-axis for visibility
                };
            });

            myChart.setOption({
                yAxis: updatedYAxis,
                series: option.series.map((serie, index) => ({
                    ...serie,
                    yAxisIndex: index // Assign each series to the corresponding y-axis
                }))
            });
        } else {
            // Update to single y-axis
            myChart.setOption({
                yAxis: [{ type: 'value', boundaryGap: ['50%', '100%']}],
                series: option.series.map(serie => ({
                    ...serie,
                    yAxisIndex: 0 // Assign all series to the single y-axis
                }))
            });
        }
    }    
</script>
