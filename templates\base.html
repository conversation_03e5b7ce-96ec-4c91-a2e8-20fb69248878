<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="static/img/favicon.ico">
    <link href="static/js/flowbite.min.css" rel="stylesheet" />
    <script src="static/js/flowbite.min.js"></script>   
    <script type="module" src="static/js/index.js"></script>    
    <script src="static/js/htmx.min.js"></script>
    <script src="static/js/sse.js"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <script src="static/js/graphics.js"></script>
    <script src="static/js/datepicker.min.js"></script>
    <script src="static/js/echarts.min.js"></script>
    <script src="static/js/ag-grid-community.min.js"></script>
    <script src="static/js/dexie.min.js"></script>
    <script src="static/js/dist.min.js"></script>
    <script src="static/js/maplibre-gl.min.js"></script>
    
    <link href="static/js/maplibre-gl.min.css" rel="stylesheet" />    
    <script>
    
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark')
        }
    </script>

    {% block header %} {% endblock %}
</head>

<body class="size-full overflow-auto bg-gray-50 dark:bg-gray-800">
    {% block content %} {% endblock content %}                       
    {% block script %} {% endblock script %}
</body>

</html>