"""
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import StreamingResponse
from starlette.middleware.cors import CORSMiddleware
import aiohttp

app = FastAPI()

# Add CORS middleware to allow requests from any origin
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.route("/{path:path}", methods=["GET", "PUT", "POST", "DELETE"])
async def proxy_endpoint(request: Request, path: str):
    async with aiohttp.ClientSession() as session:
        method = request.method.lower()
        url = f"http://your-node-server/{path}"
        headers = {k: v for k, v in request.headers.items()}

        if method == "get":
            # Handle special cases for GET requests
            if "Accept" in headers and "text/event-stream" in headers["Accept"]:
                # Handle SSE
                async with session.get(url, headers=headers) as resp:
                    async def event_stream():
                        async for line in resp.content:
                            yield line

                    return StreamingResponse(event_stream(), media_type="text/event-stream")
            elif "Upgrade" in headers and headers["Upgrade"].lower() == "websocket":
                # Handle WebSocket
                ws_client = await session.ws_connect(url)
                async for msg in ws_client:
                    await request.app.websocket.send_text(msg.data)
                await ws_client.close()
            else:
                # Handle regular GET request
                async with session.get(url, headers=headers) as resp:
                    return Response(await resp.read(), status_code=resp.status, headers=resp.headers)
        else:
            # Handle other methods (PUT, POST, DELETE)
            data = await request.body()
            async with session.request(method, url, data=data, headers=headers) as resp:
                return Response(await resp.read(), status_code=resp.status, headers=resp.headers)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

"""
#"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
#//*************:3000/updates

from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from starlette.middleware.cors import CORSMiddleware
import httpx

app = FastAPI()

# Add CORS middleware to allow requests from any origin
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.route("/{path:path}", methods=["GET", "PUT", "POST", "DELETE"])
async def proxy_endpoint(request: Request, path: str):
    async with httpx.AsyncClient() as client:
        method = request.method.lower()
        url = f"http://*************:3000/{path}"
        headers = {k: v for k, v in request.headers.items()}

        if method == "get":
            # Handle special cases for GET requests
            if "Accept" in headers and "text/event-stream" in headers["Accept"]:
                # Handle SSE
                async with client.stream("GET", url, headers=headers) as resp:
                    async def event_stream():
                        async for chunk in resp.aiter_bytes():
                            yield chunk

                    return StreamingResponse(event_stream(), media_type="text/event-stream")
            elif "Upgrade" in headers and headers["Upgrade"].lower() == "websocket":
                # Handle WebSocket
                # Note: WebSocket support with httpx might require additional configuration
                # This is a simplified example
                async with client.websocket_connect(url) as websocket:
                    async for message in websocket:
                        await request.app.websocket.send_text(message.text)
            else:
                # Handle regular GET request
                response = await client.get(url, headers=headers)
                return Response(response.content, status_code=response.status_code, headers=response.headers)
        else:
            # Handle other methods (PUT, POST, DELETE)
            data = await request.body()
            response = await client.request(method, url, data=data, headers=headers)
            return Response(response.content, status_code=response.status_code, headers=response.headers)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)