
<button id="dropdownRadioButton" data-dropdown-toggle="dropdownRadioSearch" data-dropdown-placement="bottom" 
    class="hidden flex items-center w-full justify-start mx-5 px-3 py-2 items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" type="button">
    Cerca mappa ... 
</button>    
    
    <div id="dropdownRadioSearch" class="absolute z-60 hidden bg-white rounded-lg shadow w-96 dark:bg-gray-700">
        <!-- Dropdown menu 
        <div class="flex-1 p-3">
          <label for="radio-map-search" class="sr-only">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
              </svg>
            </div>
            <input type="text" id="radio-map-search" oninput="radioMapSearch()"  class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Trova pagina">
          </div>
        </div>
        -->
        <ul id="radio-map-ul" class="h-96 px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownRadioButton">
            {% for key in radiomap %}
             <li>
                <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                <label onclick="cancelEventListners()" for={{key}} hx-get=/webscada/radiokey/{{key}}  hx-target="#radioGrid" class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">{{key}}</label>
                </div>
            </li>
            {% endfor %}
        </ul>
    </div>
 
    <script>
        function cancelEventListners(){
            const originalDiv = document.getElementById('panel');
            const clonedDiv = originalDiv.cloneNode(true);
            originalDiv.parentNode.replaceChild(clonedDiv, originalDiv);
        }

        try{
        resetFilters.classList.add('hidden');
        ackButton.classList.add('hidden');}catch{}
        function radioMapSearch(){
            var input, filter, ul, li, a, i, txtValue;
            input = document.getElementById('radio-map-search');
            filter = input.value.toUpperCase();
            ul = document.getElementById("radio-map-ul");
            li = ul.getElementsByTagName('li');
            for (i = 0; i < li.length; i++) {
                label = li[i].getElementsByTagName("label")[0];
                txtValue = label.textContent || label.innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    li[i].style.display = "";
                } else {
                    li[i].style.display = "none";
                }
            }
        }
    </script>
