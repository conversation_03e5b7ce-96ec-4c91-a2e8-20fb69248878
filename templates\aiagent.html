<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Novareti Scada Agent</title>
<style>
  #timer {
    opacity: 0;
    transition: opacity 500ms ease-in;
  }

  #timer.active {
    opacity: 1;
  }

  /* Custom styles for session items */
  .session-item {
    transition: all 0.2s ease-in-out;
  }
  .session-item:hover {
    transform: translateX(4px);
  }
  .session-item.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
  }
</style>
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

<div class="flex ">
  <div class="container w-64  px-2 py-4 h-full">
  <!-- Sessions Sidebar -->
      <div
        class="border rounded-lg shadow-md py-4 dark:bg-gray-800 dark:border-gray-700 bg-white h-full overflow-hidden flex flex-col"
        style="height:80vh;">

    <!-- Sessions List -->
    <div class="flex-1  p-4" id="sessions-list">
      <!-- Sessions will be loaded here via HTMX -->
    {% for s in sessions %}
        <div  id="{{s}}" class="mb-2 bg-gray-100 dark:bg-gray-700 p-3 rounded-lg dark:text-white whitespace-normal text-wrap">
            <button hx-get="/webscada/getsession/{{s}}" hx-target="#conversation" hx-swap="outerHTML" hx-indicator="#spinner" class="font-medium text-wrap whitespace-normal md:text-balance">{{s}}</button>
        </div>
    {% endfor %}
        <button
        hx-get="/webscada/newsession/"
        hx-target="#sessions-list"
        hx-swap="afterbegin"
        class=" w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
      </button>
    </div>
  </div>
  </div>

  <!-- Main Chat Container -->
  <div id="conversation-container" class="flex-1">
    <div class="container  max-w-3xl px-2 py-4 h-full">
      <div
        class="border rounded-lg shadow-md p-4 dark:bg-gray-800 dark:border-gray-700 bg-white h-full overflow-hidden flex flex-col"
        style="height:80vh;">
  
        <div class="flex items-center m-4">
          <img src="https://flowbite.s3.amazonaws.com/logo.svg" class="mr-3 h-8">
          <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">SCADA AGENT</span>
        </div>

        {% include 'aichat.html' %}
        <div id="error" class="hidden mt-3 text-red-600 dark:text-red-400">
          Errore,rifai la domanda o ricarica la pagina.
        </div>
      </div>
    </div>
</div>

<script type="module">
  import { marked } from 'https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.0/lib/marked.esm.js'
  var convElement = document.getElementById('chat-element')
  var promptInput = document.getElementById('prompt-input')
  var stopElement = document.getElementById('stop-element')
  var stopButton = document.getElementById('stop-button')

  // Timer variables
  let startTime = null
  let timerInterval = null
  let currentReader = null
  let isStreaming = false

  // Configure marked to respect dark mode
  marked.setOptions({
    highlight: function (code, lang) {
      return `<pre class="bg-gray-100 dark:bg-gray-900 p-2 rounded"><code>${code}</code></pre>`;
    }
  });

  // Function to process existing chat history with markdown
  function processExistingMessages() {
    const existingMessages = document.querySelectorAll('#conversation .content')
    existingMessages.forEach(contentDiv => {
      const rawContent = contentDiv.textContent || contentDiv.innerText

      if (rawContent && rawContent.trim()) {
        try {
          // Parse with marked.js
          contentDiv.innerHTML = marked.parse(rawContent.trim())

          // Apply styles to links
          contentDiv.querySelectorAll('a').forEach(a => {
            a.className = 'text-blue-600 dark:text-blue-400 hover:underline'
          })

          // Apply styles to tables
          contentDiv.querySelectorAll('table').forEach(table => {
            table.className = 'border-collapse border border-gray-300 dark:border-gray-600 w-full'
            table.querySelectorAll('th, td').forEach(cell => {
              cell.className = 'border border-gray-300 dark:border-gray-600 p-2'
            })
          })
        } catch (error) {
          console.error('Error processing existing message:', error)
          // Keep original content if markdown parsing fails
        }
      }
    })

    convElement.scrollTop = convElement.scrollHeight;
  }

  // Process existing messages when page loads
  processExistingMessages();


  function stopTimer() {

    stopElement.classList.add('hidden')
    isStreaming = false
    currentReader = null
  }

  function stopStreaming() {
    if (currentReader) {
      currentReader.cancel()
    }
    stopTimer()
    promptInput.disabled = false
    promptInput.focus()
  }

  // Add event listener for stop button
  stopButton.addEventListener('click', stopStreaming)

  // stream the response and render messages as each chunk is received
  async function onFetchResponse(response) {
    let text = ''
    let decoder = new TextDecoder()
    if (response.ok) {
      const reader = response.body.getReader()
      currentReader = reader

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            break
          }
          //text += decoder.decode(value)
          text = decoder.decode(value)

          addMessages(text)
        }
        addMessages(text)
        stopTimer()
        promptInput.disabled = false
        promptInput.focus()
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Stream was cancelled by user')
        } else {
          throw error
        }
      }
    } else {
      const text = await response.text()
      console.error(`Unexpected response: ${response.status}`, { response, text })
      stopTimer()
      throw new Error(`Unexpected response: ${response.status}`)
    }
  }

  // Global variables for streaming
  let incompleteJson = ''
  let messageStore = new Map() // Store accumulated content by timestamp
  let pendingUpdates = new Set() // Track pending DOM updates

  // Non-blocking JSON parser
  function parseJsonLines(text) {
    const lines = text.split('\n')
    const messages = []

    for (const line of lines) {
      const trimmed = line.trim()
      if (!trimmed) continue

      try {
        messages.push(JSON.parse(trimmed))
      } catch (e) {
        // Skip invalid JSON lines
      }
    }

    return messages
  }

  // Create or get message bubble
  function getOrCreateMessageBubble(timestamp, role) {
    const id = `msg-${timestamp}`
    let msgDiv = document.getElementById(id)

    if (!msgDiv) {
      msgDiv = document.createElement('div')
      msgDiv.id = id

      const date = new Date(timestamp)
      const timeString = date.toLocaleTimeString('it-IT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })

      if (role === 'user') {
        msgDiv.className = 'bg-blue-100 dark:bg-blue-900 p-3 rounded-lg dark:text-white'
        msgDiv.innerHTML = `
          <div class="flex justify-between items-center mb-1">
            <div class="font-bold">Tu:</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${timeString}</div>
          </div>
          <div class="content"></div>`
      } else {
        msgDiv.className = 'bg-gray-100 dark:bg-gray-700 p-3 rounded-lg dark:text-white'
        msgDiv.innerHTML = `
          <div class="flex justify-between items-center mb-1">
            <div class="font-bold">Scada Agent:</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${timeString}</div>
          </div>
          <div class="content"></div>`
      }
      stopElement.classList.remove('hidden')

      convElement.appendChild(msgDiv)
      messageStore.set(timestamp, '')
    }

    return msgDiv
  }

  // Update message content efficiently
  function updateMessageContent(timestamp, newContent) {
    if (pendingUpdates.has(timestamp)) return

    pendingUpdates.add(timestamp)

    // Use requestAnimationFrame for smooth updates
    requestAnimationFrame(() => {
      const msgDiv = document.getElementById(`msg-${timestamp}`)
      if (!msgDiv) {
        pendingUpdates.delete(timestamp)
        return
      }

      const contentDiv = msgDiv.querySelector('.content')
      const fullContent = messageStore.get(timestamp) || ''

      // Filter out <think> tags
      let displayContent = fullContent.replace(/<think>[\s\S]*?<\/think>/g, '').trim()

      if (displayContent) {
        // Simple text update for streaming performance
        contentDiv.textContent = displayContent

        // Only parse markdown when content seems complete
        if (/[.!?]\s*$/.test(displayContent) || /[*_`#\[\](){}]/.test(displayContent)) {
          try {
            contentDiv.innerHTML = marked.parse(displayContent)

            // Apply styles efficiently
            const links = contentDiv.querySelectorAll('a')
            const tables = contentDiv.querySelectorAll('table')

            links.forEach(a => a.className = 'text-blue-600 dark:text-blue-400 hover:underline')
            tables.forEach(table => {
              table.className = 'border-collapse border border-gray-300 dark:border-gray-600 w-full'
              table.querySelectorAll('th, td').forEach(cell => {
                cell.className = 'border border-gray-300 dark:border-gray-600 p-2'
              })
            })
          } catch (e) {
            contentDiv.textContent = displayContent
          }
        }
      }

      pendingUpdates.delete(timestamp)

      // Smooth scroll
      convElement.scrollTop = convElement.scrollHeight
    })
  }

  // Main addMessages function - completely rewritten
  function addMessages(responseText) {
    const fullText = incompleteJson + responseText
    incompleteJson = ''

    // Parse messages efficiently
    const messages = parseJsonLines(fullText)

    // Process messages without blocking
    for (const message of messages) {
      const { timestamp, role, content } = message

      // Create bubble if needed
      getOrCreateMessageBubble(timestamp, role)

      // Accumulate content
      const existing = messageStore.get(timestamp) || ''
      messageStore.set(timestamp, existing + content)

      // Schedule non-blocking update
      updateMessageContent(timestamp, content)
    }
  }

  function onError(error) {
    console.error(error)
    document.getElementById('error').classList.remove('hidden')
    stopTimer()
    promptInput.disabled = false
  }

  async function onSubmit(e) {
    e.preventDefault()
    var body = new FormData(e.target)
    const id = e.target.id
    body.append('session_id',id)
    promptInput.value = ''
    promptInput.disabled = true

    incompleteJson = ''
    messageStore.clear()
    pendingUpdates.clear()

    try {
      const response = await fetch('/webscada/aichat/', { method: 'POST', body })
      await onFetchResponse(response)
    } catch (error) {
      onError(error)
    }
  }

  document.addEventListener('submit', (e) => {
  if (e.target.matches('form')) {
    // Handle any form submission
    onSubmit(e).catch(onError)
  }
})

  htmx.on('htmx:afterSwap', (msg)=> {     
        //document.querySelector('form').addEventListener('submit', (e) => onSubmit(e).catch(onError))
    convElement = document.getElementById('chat-element')
    promptInput = document.getElementById('prompt-input')
    stopElement = document.getElementById('stop-element')
    stopButton = document.getElementById('stop-button')        
    //fetch('/webscada/aichat/').then(onFetchResponse).catch(onError)
    setTimeout(processExistingMessages(), 500);
  });  
</script>