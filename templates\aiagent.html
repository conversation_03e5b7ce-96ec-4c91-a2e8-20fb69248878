<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Novareti Scada Agent</title>
<style>
  #timer {
    opacity: 0;
    transition: opacity 500ms ease-in;
  }

  #timer.active {
    opacity: 1;
  }
</style>

<div class="container mx-auto max-w-3xl px-2 py-4">
  <div
    class="border rounded-lg shadow-md p-4 dark:bg-gray-800 dark:border-gray-700 bg-white h-screen max-h-screen overflow-hidden flex flex-col"
    style="height:80vh;">

    <div class="flex items-center m-4">
      <img src="https://flowbite.s3.amazonaws.com/logo.svg" class="mr-3 h-8">
      <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">SCADA AGENT</span>
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
      <div
        class="border-2 text-center border-dashed border-gray-300 rounded-lg dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Aggiornami sui valori
      </div>
      <div
        class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Dammi gli allarmi
      </div>
      <div
        class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Info sugli impianti
      </div>
      <div
        class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Chiedimi qualsiasi info sullo Scada
      </div>
    </div>

    <div id="conversation" class="mb-4 space-y-4 flex-1 overflow-y-auto p-2"></div>

    <div id="timer"
      class="hidden flex items-center justify-center text-center text-xs text-gray-400 dark:text-gray-500 mb-3">
      <div class="px-2 py-1 text-xs font-medium leading-none text-center text-blue-800 bg-blue-200 rounded-full animate-pulse dark:bg-blue-900 dark:text-blue-200">
        Generazione risposta... </div>
      <button id="stop-button" class="mx-2 px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none">
        Stop
      </button>
    </div>

    <form method="post" class="space-y-2">
      <input id="prompt-input" name="prompt"
        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        placeholder="Scrivi il tuo messaggio qui..."></input>
      <div class="flex justify-end">
        <button
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-4 focus:ring-blue-300">
          Invia
        </button>
      </div>
    </form>

    <div id="error" class="hidden mt-3 text-red-600 dark:text-red-400">
      Errore,rifai la domanda o ricarica la pagina.
    </div>
  </div>
</div>

<script type="module">
  import { marked } from 'https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.0/lib/marked.esm.js'
  const convElement = document.getElementById('conversation')
  const promptInput = document.getElementById('prompt-input')
  const timerElement = document.getElementById('timer')
  const stopButton = document.getElementById('stop-button')

  // Timer variables
  let startTime = null
  let timerInterval = null
  let currentReader = null
  let isStreaming = false

  // Configure marked to respect dark mode
  marked.setOptions({
    highlight: function (code, lang) {
      return `<pre class="bg-gray-100 dark:bg-gray-900 p-2 rounded"><code>${code}</code></pre>`;
    }
  });

  function startTimer() {
    startTime = Date.now()
    timerElement.classList.remove('hidden')
    isStreaming = true

  }

  function stopTimer() {

    timerElement.classList.add('hidden')
    isStreaming = false
    currentReader = null
  }

  function stopStreaming() {
    if (currentReader) {
      currentReader.cancel()
    }
    stopTimer()
    promptInput.disabled = false
    promptInput.focus()
  }

  // Add event listener for stop button
  stopButton.addEventListener('click', stopStreaming)

  // stream the response and render messages as each chunk is received
  async function onFetchResponse(response) {
    let text = ''
    let decoder = new TextDecoder()
    if (response.ok) {
      const reader = response.body.getReader()
      currentReader = reader

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            break
          }
          text += decoder.decode(value)
          addMessages(text)
        }
        addMessages(text)
        stopTimer()
        promptInput.disabled = false
        promptInput.focus()
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Stream was cancelled by user')
        } else {
          throw error
        }
      }
    } else {
      const text = await response.text()
      console.error(`Unexpected response: ${response.status}`, { response, text })
      stopTimer()
      throw new Error(`Unexpected response: ${response.status}`)
    }
  }

  // Global variables for handling streaming chunks
  let incompleteJson = ''
  let messageContents = new Map() // Store message content by timestamp

  // take raw response text and render messages into the `#conversation` element
  function addMessages(responseText) {
    const fullText = incompleteJson + responseText
    const lines = fullText.split('\n')

    const messages = []
    incompleteJson = ''

    // Parse JSON lines, handling multi-line JSON objects
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (line.length === 0) continue

      try {
        const message = JSON.parse(line)
        messages.push(message)
      } catch (e) {
        if (i === lines.length - 1) {
          incompleteJson = line
        } else {
          let combinedLine = line
          let j = i + 1
          while (j < lines.length) {
            combinedLine += '\n' + lines[j]
            try {
              const message = JSON.parse(combinedLine)
              messages.push(message)
              i = j
              break
            } catch (e2) {
              j++
            }
          }
          if (j >= lines.length) {
            incompleteJson = combinedLine
            break
          }
        }
      }
    }

    // Process each parsed message
    for (const message of messages) {
      const { timestamp, role, content } = message

      // Debug logging
      console.log(`Message: timestamp=${timestamp}, role=${role}, content="${content}"`)

      // Use role-based grouping instead of timestamp since timestamps might vary slightly
      const messageKey = role === 'user' ? `user-${Math.floor(Date.now() / 10000)}` : `ai-${Math.floor(Date.now() / 10000)}`
      let msgDiv = document.querySelector(`[data-message-key="${messageKey}"]`)

      // Create message div if it doesn't exist
      if (!msgDiv) {
        msgDiv = document.createElement('div')
        msgDiv.setAttribute('data-message-key', messageKey)
        msgDiv.id = `msg-${timestamp}`

        const date = new Date(timestamp)
        const timeString = date.toLocaleTimeString('it-IT', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        })

        if (role === 'user') {
          msgDiv.classList.add('bg-blue-100', 'dark:bg-blue-900', 'p-3', 'rounded-lg', 'dark:text-white')
          msgDiv.innerHTML = `
          <div class="flex justify-between items-center mb-1">
            <div class="font-bold">Tu:</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${timeString}</div>
          </div>`
          timerElement.classList.remove('hidden')
        } else {
          msgDiv.classList.add('bg-gray-100', 'dark:bg-gray-700', 'p-3', 'rounded-lg', 'dark:text-white')
          msgDiv.innerHTML = `
          <div class="flex justify-between items-center mb-1">
            <div class="font-bold">Scada Agent:</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${timeString}</div>
          </div>`
          timerElement.classList.remove('hidden')
        }

        const contentDiv = document.createElement('div')
        contentDiv.classList.add('content')
        msgDiv.appendChild(contentDiv)
        convElement.appendChild(msgDiv)

        // Initialize content for this message key
        messageContents.set(messageKey, '')

        console.log(`Created new message bubble with key: ${messageKey}`)
      }

      // Append new content to existing content for this message
      const existingContent = messageContents.get(messageKey) || ''
      const newContent = existingContent + content
      messageContents.set(messageKey, newContent)

      console.log(`Updated content for ${messageKey}: "${newContent}"`)

      // Update the display
      const contentDiv = msgDiv.querySelector('.content')

      // Filter out <think> tags
      let displayContent = newContent.replace(/<think>[\s\S]*?<\/think>/g, '').trim()

      if (displayContent) {
        // Always show as plain text for now to debug
        contentDiv.textContent = displayContent

        console.log(`Display content: "${displayContent}"`)
      }
    }

    // Scroll to bottom
    convElement.scrollTop = convElement.scrollHeight
  }

  function onError(error) {
    console.error(error)
    document.getElementById('error').classList.remove('hidden')
    stopTimer()
    promptInput.disabled = false
  }

  async function onSubmit(e) {
    e.preventDefault()
    const body = new FormData(e.target)

    promptInput.value = ''
    promptInput.disabled = true

    incompleteJson = ''
    messageContents.clear()

    try {
      const response = await fetch('/webscada/aichat/', { method: 'POST', body })
      await onFetchResponse(response)
    } catch (error) {
      onError(error)
    }
  }

  document.querySelector('form').addEventListener('submit', (e) => onSubmit(e).catch(onError))
  fetch('/webscada/aichat/').then(onFetchResponse).catch(onError)
</script>