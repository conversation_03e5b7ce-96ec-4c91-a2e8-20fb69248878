<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Novareti Scada Agent</title>
<style>
  #timer {
    opacity: 0;
    transition: opacity 500ms ease-in;
  }

  #timer.active {
    opacity: 1;
  }
</style>

<div class="container mx-auto max-w-3xl px-2 py-4">
  <div
    class="border rounded-lg shadow-md p-4 dark:bg-gray-800 dark:border-gray-700 bg-white h-screen max-h-screen overflow-hidden flex flex-col"
    style="height:80vh;">

    <div class="flex items-center m-4">
      <img src="https://flowbite.s3.amazonaws.com/logo.svg" class="mr-3 h-8">
      <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">SCADA AGENT</span>
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
      <div
        class="border-2 text-center border-dashed border-gray-300 rounded-lg dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Aggiornami sui valori
      </div>
      <div
        class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Dammi gli allarmi
      </div>
      <div
        class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Info sugli impianti
      </div>
      <div
        class="border-2 text-center border-dashed rounded-lg border-gray-300 dark:border-gray-600 p-4 h-32 md:h-64 text-gray-600 dark:text-gray-400">
        Chiedimi qualsiasi info sullo Scada
      </div>
    </div>

    <div id="conversation" class="mb-4 space-y-4 flex-1 overflow-y-auto p-2"></div>

    <div id="timer"
      class="hidden flex items-center justify-center text-center text-xs text-gray-400 dark:text-gray-500 mb-3">
      <div class="px-2 py-1 text-xs font-medium leading-none text-center text-blue-800 bg-blue-200 rounded-full animate-pulse dark:bg-blue-900 dark:text-blue-200">
        Generazione risposta... </div>
      <button id="stop-button" class="mx-2 px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none">
        Stop
      </button>
    </div>

    <form method="post" class="space-y-2">
      <input id="prompt-input" name="prompt"
        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        placeholder="Scrivi il tuo messaggio qui..."></input>
      <div class="flex justify-end">
        <button
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-4 focus:ring-blue-300">
          Invia
        </button>
      </div>
    </form>

    <div id="error" class="hidden mt-3 text-red-600 dark:text-red-400">
      Errore,rifai la domanda o ricarica la pagina.
    </div>
  </div>
</div>

<script type="module">
  import { marked } from 'https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.0/lib/marked.esm.js'
  const convElement = document.getElementById('conversation')
  const promptInput = document.getElementById('prompt-input')
  const timerElement = document.getElementById('timer')
  const stopButton = document.getElementById('stop-button')

  // Timer variables
  let startTime = null
  let timerInterval = null
  let currentReader = null
  let isStreaming = false

  // Configure marked to respect dark mode
  marked.setOptions({
    highlight: function (code, lang) {
      return `<pre class="bg-gray-100 dark:bg-gray-900 p-2 rounded"><code>${code}</code></pre>`;
    }
  });

  function startTimer() {
    startTime = Date.now()
    timerElement.classList.remove('hidden')
    isStreaming = true

  }

  function stopTimer() {

    timerElement.classList.add('hidden')
    isStreaming = false
    currentReader = null
  }

  function stopStreaming() {
    if (currentReader) {
      currentReader.cancel()
    }
    // Clear processing queue and reset state
    processingQueue.length = 0
    isProcessing = false
    clearTimeout(scrollTimeout)

    stopTimer()
    promptInput.disabled = false
    promptInput.focus()
  }

  // Add event listener for stop button
  stopButton.addEventListener('click', stopStreaming)

  // stream the response and render messages as each chunk is received
  async function onFetchResponse(response) {
    let text = ''
    let decoder = new TextDecoder()
    let chunkCount = 0
    const THROTTLE_INTERVAL = 5 // Process every 5th chunk to reduce load

    if (response.ok) {
      const reader = response.body.getReader()
      currentReader = reader

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            break
          }

          text += decoder.decode(value)
          chunkCount++

          // Throttle processing to avoid overwhelming the main thread
          if (chunkCount % THROTTLE_INTERVAL === 0) {
            await addMessages(text)
            // Small delay to allow UI updates
            await yieldToEventLoop()
          }
        }

        // Process final chunk
        await addMessages(text)
        stopTimer()
        promptInput.disabled = false
        promptInput.focus()
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Stream was cancelled by user')
        } else {
          throw error
        }
      }
    } else {
      const text = await response.text()
      console.error(`Unexpected response: ${response.status}`, { response, text })
      stopTimer()
      throw new Error(`Unexpected response: ${response.status}`)
    }
  }

  // Global variables for handling streaming chunks
  let incompleteJson = ''
  let messageContents = new Map()
  let processingQueue = []
  let isProcessing = false

  // Utility function to yield control to the event loop
  function yieldToEventLoop() {
    return new Promise(resolve => setTimeout(resolve, 0))
  }

  // Non-blocking JSON parsing with yielding
  async function parseMessagesAsync(responseText) {
    const fullText = incompleteJson + responseText
    const lines = fullText.split('\n')
    const messages = []
    incompleteJson = ''

    // Process lines in chunks to avoid blocking
    const CHUNK_SIZE = 10
    for (let i = 0; i < lines.length; i += CHUNK_SIZE) {
      const chunk = lines.slice(i, Math.min(i + CHUNK_SIZE, lines.length))

      for (let j = 0; j < chunk.length; j++) {
        const actualIndex = i + j
        const line = chunk[j].trim()
        if (line.length === 0) continue

        try {
          const message = JSON.parse(line)
          messages.push(message)
        } catch (e) {
          if (actualIndex === lines.length - 1) {
            incompleteJson = line
          } else {
            let combinedLine = line
            let k = actualIndex + 1
            while (k < lines.length) {
              combinedLine += '\n' + lines[k]
              try {
                const message = JSON.parse(combinedLine)
                messages.push(message)
                // Skip processed lines
                const skipCount = k - actualIndex
                j += skipCount
                break
              } catch (e2) {
                k++
              }
            }
            if (k >= lines.length) {
              incompleteJson = combinedLine
              break
            }
          }
        }
      }

      // Yield to event loop after processing each chunk
      if (i + CHUNK_SIZE < lines.length) {
        await yieldToEventLoop()
      }
    }

    return messages
  }

  // Smooth scroll management
  let scrollTimeout = null
  let shouldAutoScroll = true

  function smoothScrollToBottom() {
    if (!shouldAutoScroll) return

    clearTimeout(scrollTimeout)
    scrollTimeout = setTimeout(() => {
      if (shouldAutoScroll) {
        convElement.scrollTop = convElement.scrollHeight
      }
    }, 50) // Small delay to batch scroll updates
  }

  // Check if user has scrolled up manually
  function checkScrollPosition() {
    const isAtBottom = convElement.scrollTop + convElement.clientHeight >= convElement.scrollHeight - 10
    shouldAutoScroll = isAtBottom
  }

  // Add scroll listener to detect manual scrolling
  convElement.addEventListener('scroll', checkScrollPosition)

  // Non-blocking message processing
  async function processMessagesAsync(messages) {
    const MESSAGE_CHUNK_SIZE = 5

    for (let i = 0; i < messages.length; i += MESSAGE_CHUNK_SIZE) {
      const chunk = messages.slice(i, Math.min(i + MESSAGE_CHUNK_SIZE, messages.length))

      for (const message of chunk) {
        await processMessageAsync(message)
      }

      // Yield to event loop after processing each chunk
      if (i + MESSAGE_CHUNK_SIZE < messages.length) {
        await yieldToEventLoop()
      }
    }
  }

  // Process individual message with optimizations
  async function processMessageAsync(message) {
    const { timestamp, role, content } = message
    const id = `msg-${timestamp}`
    let msgDiv = document.getElementById(id)

    // Create message div if it doesn't exist
    if (!msgDiv) {
      msgDiv = document.createElement('div')
      msgDiv.id = id

      const date = new Date(timestamp)
      const timeString = date.toLocaleTimeString('it-IT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })

      if (role === 'user') {
        msgDiv.className = 'bg-blue-100 dark:bg-blue-900 p-3 rounded-lg dark:text-white'
        msgDiv.innerHTML = `
        <div class="flex justify-between items-center mb-1">
          <div class="font-bold">Tu:</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">${timeString}</div>
        </div>`
        timerElement.classList.remove('hidden')
      } else {
        msgDiv.className = 'bg-gray-100 dark:bg-gray-700 p-3 rounded-lg dark:text-white'
        msgDiv.innerHTML = `
        <div class="flex justify-between items-center mb-1">
          <div class="font-bold">Scada Agent:</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">${timeString}</div>
        </div>`
        timerElement.classList.remove('hidden')
      }

      const contentDiv = document.createElement('div')
      contentDiv.className = 'content'
      msgDiv.appendChild(contentDiv)
      convElement.appendChild(msgDiv)
      messageContents.set(timestamp, '')
    }

    const previousContent = messageContents.get(timestamp) || ''

    if (content !== previousContent) {
      messageContents.set(timestamp, content)
      const contentDiv = msgDiv.querySelector('.content')

      // Use requestAnimationFrame for DOM updates
      requestAnimationFrame(() => {
        try {
          contentDiv.innerHTML = marked.parse(content)

          // Batch DOM queries and updates
          const links = contentDiv.querySelectorAll('a')
          const tables = contentDiv.querySelectorAll('table')

          // Apply styles in batches
          if (links.length > 0) {
            links.forEach(a => {
              a.className = 'text-blue-600 dark:text-blue-400 hover:underline'
            })
          }

          if (tables.length > 0) {
            tables.forEach(table => {
              table.className = 'border-collapse border border-gray-300 dark:border-gray-600 w-full'
              const cells = table.querySelectorAll('th, td')
              cells.forEach(cell => {
                cell.className = 'border border-gray-300 dark:border-gray-600 p-2'
              })
            })
          }
        } catch (error) {
          console.error('Error rendering content:', error)
          contentDiv.textContent = content // Fallback to plain text
        }
      })
    }
  }

  // Main function to handle messages with non-blocking processing
  async function addMessages(responseText) {
    // Add to processing queue
    processingQueue.push(responseText)

    // If already processing, return early
    if (isProcessing) {
      return
    }

    isProcessing = true

    try {
      while (processingQueue.length > 0) {
        const text = processingQueue.shift()

        // Parse messages asynchronously
        const messages = await parseMessagesAsync(text)

        // Process messages asynchronously
        if (messages.length > 0) {
          await processMessagesAsync(messages)

          // Smooth scroll to bottom
          smoothScrollToBottom()
        }
      }
    } catch (error) {
      console.error('Error processing messages:', error)
    } finally {
      isProcessing = false
    }
  }

  function onError(error) {
    console.error(error)
    document.getElementById('error').classList.remove('hidden')
    stopTimer()
    promptInput.disabled = false
  }

  // Function to immediately display user message
  function displayUserMessage(prompt) {
    const timestamp = Date.now()
    const userMessage = {
      timestamp: timestamp,
      role: 'user',
      content: prompt
    }

    // Process user message immediately
    processMessageAsync(userMessage)

    // Scroll to bottom immediately
    requestAnimationFrame(() => {
      convElement.scrollTop = convElement.scrollHeight
    })
  }

  async function onSubmit(e) {
    e.preventDefault()
    const body = new FormData(e.target)
    const prompt = body.get('prompt')

    // Display user message immediately
    displayUserMessage(prompt)

    promptInput.value = ''
    promptInput.disabled = true

    // Reset processing state
    incompleteJson = ''
    messageContents.clear()
    processingQueue.length = 0
    isProcessing = false

    try {
      const response = await fetch('/webscada/aichat/', { method: 'POST', body })
      await onFetchResponse(response)
    } catch (error) {
      onError(error)
    }
  }

  document.querySelector('form').addEventListener('submit', (e) => onSubmit(e).catch(onError))
  fetch('/webscada/aichat/').then(onFetchResponse).catch(onError)
</script>