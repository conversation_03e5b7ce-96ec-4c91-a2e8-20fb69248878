<!-- Main modal -->
<div id="global-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden  fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <!-- Modal content -->
    <div class="relative  p-4 w-full max-w-2xl max-h-full bg-white dark:bg-gray-800 rounded-lg shadow ">
        <!-- Modal header -->
        <div class="flex items-center justify-between rounded-t dark:bg-gray-800">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white ">
                Grafico Globale
            </h3>

            <div class="rounded-lg dark:bg-gray-800 p-4 md:p-4">  
                    <button id="dropdown-chart-global"
                        data-dropdown-toggle="dropdown-toggle-global"
                        data-dropdown-placement="bottom" type="button" class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Oggi <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                        </svg>
                    </button>
                
                <div id="dropdown-toggle-global" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                        <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-chart-global">
                        <li>
                            <label id="yesterday-global" onclick="zoomChart(1)"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Ieri</label>
                        </li>
                        <li>
                            <label id="one_week-global"  onclick="zoomChart(7)"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Ultimi 7 gg</label>
                        </li>
                        <li>
                            <label id="one_month-global" onclick="zoomChart(30)"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Ultimi 30 gg</label>
                        </li>
                        <li>
                            <label id="six_months-global" onclick="zoomChart(90)"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Ultimi 6 mesi</label>
                        </li>
                        <li>
                            <label id="one_year-global" onclick="zoomChart(365)"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Ultimo anno</label>
                        </li>
                        <li>
                            <label onclick="clearGlobalChart()" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Reset</label>
                        </li>                        
                        </ul>
                  </div>                                             
            </div>               

            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="global-modal">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Close modal</span>
            </button>

        </div>
        <div id="chart-global">
            <div id="global-timeline-{{chartIndex}}" class="chart-timeline"></div>
        </div> 
    </div>
</div> 
<script>

var db = new Dexie("scadadb");
db.version(1).stores({
    objects: 'name,payload'
});

// Open the database
db.open().catch(function (e) {
    console.error("Open failed: " + e);
});

function addObject(obj) {
    db.objects.put(obj).then(function() {
        console.log("Object added");
        // Notify other tabs that a change has occurred
    
        broadcastChannel.postMessage({operation:'add',name :obj.name});
    });
}

function getObjectByName(name) {
    db.objects.where('name').equals(name).first().then(function(obj) {
        if (obj) {
            console.log('Object found:');
            globalSeriesData = obj.payload;
            for (let idx in globalSeriesData){
                loadChart(globalSeriesData[idx],idx)
            }
        } else {
            console.log('Object not found');  
            return {};              
        }
    }).catch(function(error) {
        console.error('Failed to fetch object:', error);
    });        
}

// Function to clear the database
function clearGlobalChart() {
    db.objects.clear().then(function() {
        console.log("Database cleared");
        for (let idx in globalSeriesData){
            let id = 'global-timeline-' + idx;
            clearChart(id);
            broadcastChannel.postMessage({operation:'clear',name :""});

        }
    }).catch(function(error) {
        console.error("Failed to clear database:", error);
    });
}

// Create a new Broadcast Channel
var broadcastChannel = new BroadcastChannel('global_chart');

// Listen for messages from other tabs
broadcastChannel.onmessage = function (e) {
        let name = e.data.name;

        /* for now donothing!!!
        if (e.data.operation == 'add'){
            getObjectByName(name);            
        }
        if (e.data.operation == 'clear'){
            clearGlobalChart();            
        }
        */
};

</script>
<script>
  function getQueryParameters(url){
            const urlObj = new URL(url, 'https://telecontrollo.novareti.eu'); // Base URL is needed only for parsing relative URLs
            // Use URLSearchParams to get the query parameters
            const params = new URLSearchParams(urlObj.search);
            // Get the chartIndex parameter and convert it to an integer
            index = parseInt(params.get('chartIndex'), 10) + 1;
            return index
    }

    //chart init method
function chartInit(id){
    var chartElement = document.getElementById(id); 
    var chartsObj = [];
    var myChart = echarts.init(chartElement, null, {
        renderer: 'canvas',
        useDirtyRect: false
    });
    
    var app = {};
    var data = []
    var option = {
        tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
            backgroundColor: '#6a7985'
            }
        },    
        position: function (pt) {
            return [pt[0], '10%'];
        }
        },
        legend: {
        //data: ['Legend A', 'Legend B', 'Legend C'],
        //backgroundColor: '#ccc',
        textStyle: {
            color: '#777'
            // ...
        }
        // ...
        },
        toolbox: {
        feature: {
            dataZoom: {
            yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
        }
        },
        xAxis: {
        type: 'time',
        boundaryGap: false,
        },
        yAxis: {
        type: 'value',
        boundaryGap: ['50%', '100%']
        },
        
        dataZoom: [
        {
            type: 'inside',
            start: 0,
            end: 100
        },
        {
            start: 0,
            end: 100
        }
        ],
        series: [
        {
            type: 'line',
            smooth: true,
            symbol: 'none',
            areaStyle: {},
            data: data
        }
        ]
    };
    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }
    
    window.addEventListener('resize', myChart.resize);
    return myChart;
}

function clearChart(id){
    var chartElement = document.getElementById(id); 
    var chartsObj = []

    var myChart = echarts.init(chartElement, null, {
        renderer: 'canvas',
        useDirtyRect: false
    });
    let option = {}    
    myChart.setOption(option, true);
}

var globalSeriesData = {}
getObjectByName('globalchart')

function loadChart(result,index){
    let id = 'global-timeline-'+index;
    const myChart = chartInit(id)                                
    var data = []
    if (!globalSeriesData.hasOwnProperty(index)) {
    globalSeriesData[index] = {}
    }
    
    for (let e in result) {
    globalSeriesData[index][e] = result[e]
    } 
    
    for(let key in globalSeriesData[index]){
        console.log('keyname is', key)
        let keyName = document.getElementsByName(key)[0]
        //console.log(id,key,keyName.innerText,globalSeriesData[index][key])
        data.push({name: keyName.innerText ,data: globalSeriesData[index][key], type: 'line',smooth: true,symbol: 'none',areaStyle: {}})
    }               
    myChart.setOption({
        series: data
        });    
}

function restoreChart(){
    for (let idx in globalSeriesData){
        loadChart(globalSeriesData[idx],idx)
    }
}

function zoomChart(startDate, endDate) {
    for (let idx in globalSeriesData){
        let id = 'global-timeline-'+idx;
        let myChart = echarts.init(document.getElementById(id));
        myChart.dispatchAction({
            type: 'dataZoom',
            startValue: startDate,
            endValue: endDate
        });        
    }

}

function zoomChart(days) {
    var endDate = new Date(); // Today's date
    var startDate = new Date();
    startDate.setDate(endDate.getDate() - days); // Calculate 'today - X days'
    for (let idx in globalSeriesData){
        let id = 'global-timeline-'+idx;
        let myChart = echarts.init(document.getElementById(id));
        myChart.dispatchAction({
            type: 'dataZoom',
            startValue: startDate.toISOString().slice(0, 10), // Format as 'YYYY-MM-DD'
            endValue: endDate.toISOString().slice(0, 10) // Format as 'YYYY-MM-DD'
        });
    }
}

htmx.on('htmx:afterRequest', (msg)=> {    
      //update chartsindex for the UI
      /*if (msg.detail.pathInfo.responsePath.includes("/addchart")){    
        chartIndex = getQueryParameters(msg.detail.pathInfo.responsePath)           
      }*/
      if (msg.detail.pathInfo.responsePath.includes("/globalchart")){    
              const index = getQueryParameters(msg.detail.pathInfo.responsePath) - 1;
              loadChart(JSON.parse(msg.detail.xhr.response), index);
              addObject( { name: "globalchart", payload: globalSeriesData });
        }            
     });
    
</script>
