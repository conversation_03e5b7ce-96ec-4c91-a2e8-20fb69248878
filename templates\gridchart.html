<style>
.ag-header-cell-label {
  justify-content: center;
}
</style>

<div id="chart-grid">
  {% include 'chart.html'%}  
    <!-- DATAGRID-->
    <div id="analyticsGrid" class="ag-theme-quartz pb-16" style="height: 600px;">
      <div class="flex  justify-between  py-4 ">
        <input type="text" id="grid-filter"
          class="block  hidden px-4 py-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500"
          placeholder="Cerca...">
          <button 
          class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
          type="button" onclick="onBtnExport()">Esporta tabella</button>
          <button 
          class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
          type="button" onclick="clearFilters()">Azzera filtri</button>

      </div>  
    </div>    
</div>

<script>
  var gridApi = localGridInit('#analyticsGrid')   

  //charts index to get all charts
  var chartIndex = 1;
  function getChartsIndex(){
    return chartIndex;
  }
  
  //all pages series will be contained here 
  var seriesData = {} 
  var dataTypes = []

function deltaTime(startDate, delta){
    console.log("Current date:", startDate);

     // Calculate the number of milliseconds in 3 days
    let millisecondsInADay = 24 * 60 * 60 * 1000; // hours * minutes * seconds * milliseconds
    let millisecondsToAdd = delta * millisecondsInADay;

    // Add the milliseconds to the current date
    let result = new Date(startDate.getTime() - millisecondsToAdd);
    result.setHours(0);
    result.setMinutes(0);
    result.setSeconds(0);
    result.setMilliseconds(0);    
    return result;
}

function zoomLocalChart(e,days,id) {
  console.log(e)
  var endDate = new Date();
  var startDate = new Date(); 

  if(days == 0.1){
    startDate.setHours(startDate.getHours() - 1)
    console.log(endDate.toISOString(),startDate.toISOString())
  }
  else if( days == 1){
    startDate.setHours(0);
    startDate.setMinutes(0);
    startDate.setSeconds(0);
    startDate.setMilliseconds(0);
  }
  else{
    endDate.setDate(endDate.getDate() + 1);     
    //startDate.setDate(endDate.getDate() - days);
    startDate = deltaTime(startDate,days)
  }
  
  let myChart = echarts.init(document.getElementById(id));
  try {
    var seriesData = myChart.getOption().series;  
    console.log(seriesData)
  } catch (error) {
    console.warn(error)
    return
  }
  index = parseInt(id.split("-").pop());
  dates = []
  for (var i = 0; i < seriesData.length; i++) {
      earlyDate = new Date(Date.parse(seriesData[i].data[0][0]));
      dates.push([earlyDate,seriesData[i].tag])
      /*if(seriesData[i].dataType == 'DIN'| seriesData[i].dataType == 'DOUT'){
        console.log('xxxx',seriesData[i].data[0].value);

        earlyDate = new Date(seriesData[i].data[0].value[1]);
      }else{
        earlyDate = new Date(Date.parse(seriesData[i].data[0][0]));
      }*/
      }
   for (let i in dates){
    if(dates[i][0] > startDate){
      htmx.ajax('GET', `/webscada/chart/${days}/${dates[i][1]}?chartIndex=${index}`, {swap:'none', indicator:"#spinner"}).then((response) => {
        console.log(dates[i][0],dates[i][1])
      });
    }  
  }

  myChart.dispatchAction({
      type: 'dataZoom',
      startValue: startDate.toISOString(), // Format as 'YYYY-MM-DD'
      endValue: endDate.toISOString() // Format as 'YYYY-MM-DD'
  });

  //crossSelection
  /*
  endDate.setDate(endDate.getDate() + 1); 
  const filterModel = {          
          timestamp: {
            type: 'inRange', dateFrom: startDate.toISOString(), dateTo: endDate.toISOString()
            }            
          }
  gridApi.setFilterModel(filterModel);      
  */
}

function onBtnExport() {
    gridApi.exportDataAsCsv();
  }

function clearFilters() {
      gridApi.setFilterModel(null);
  } 

function calculatePercentage(minValue, maxValue, inputValue) {
  const range = maxValue - minValue;
  const percentage = ((inputValue - minValue) / range) * 100;
  return percentage;
}

</script>