
<style>
  .chart-timeline {
    position: relative;
    height: 350px;
    overflow: hidden;
  }

.ag-header-cell-label {
  justify-content: center;
}
.search-input {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
</style>


<div class="rounded-lg dark:bg-gray-800 p-1 md:p-1">
  <div class="flex justify-between mb-2"> 
    <!--  chart legend-->
      <div id="chart-legend" class="grid grid-cols-10 gap-2 p-1 mx-auto my-2 bg-gray-100 rounded-lg dark:bg-gray-600" role="group">
        <div data-popover="" id="plants-search" role="tooltip" 
        class="absolute overflow-auto z-10 min-w-max  text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-2xl dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 opacity-0 invisible " style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(221px, 731px);" data-popper-placement="bottom-start">
          <div class="p-2">
                {% include 'scadabiplant.html' %} 
          </div>                                        
          <div data-popper-arrow="" style="position: absolute; left: 0px; transform: translate(7px, 0px);"></div>
      </div>        
        <button onclick="{document.getElementById('context-menu').classList.add('hidden')}" 
        data-popover-trigger="click" data-popover-target="plants-search" data-popover-placement="bottom-end"
        class="px-2 py-1.5 text-xs font-medium bg-blue-700 hover:bg-blue-800 text-gray-900 hover:bg-blue-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
        <svg class="w-6 h-6 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="m21 21-3.5-3.5M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z"/>
        </svg>
        </button>
        <button id='zoom-{{chartIndex}}-1' onclick="zoomLocalChart(this,1,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          1 g
        </button>
        <button id='zoom-{{chartIndex}}-2' onclick="zoomLocalChart(this,7,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          7 g
        </button>
        <button id='zoom-{{chartIndex}}-3' onclick="zoomLocalChart(this,30,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          1 m
        </button>    
        <button id='zoom-{{chartIndex}}-4' onclick="zoomLocalChart(this,90,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          3 m
        </button> 
        <button id='zoom-{{chartIndex}}-5' onclick="zoomLocalChart(this,180,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          6 m
        </button>   
        <button id='zoom-{{chartIndex}}-6' onclick="zoomLocalChart(this,365,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          1 y
        </button>     
        <button id="dropdownTimepickerButton" data-dropdown-toggle="datetimePicker-{{chartIndex}}" type="button" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.5 11.5 2.071 1.994M4 10h5m11 0h-1.5M12 7V4M7 7V4m10 3V4m-7 13H8v-2l5.227-5.292a1.46 1.46 0 0 1 2.065 2.065L10 17Zm-5 3h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
          </svg>
          
        </button>     
        <!-- datetime dropdown menu -->
        <div id="datetimePicker-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm w-auto dark:bg-gray-700 p-3">
          <div class="max-w-[16rem] mx-auto gap-4 mb-1">
              <div class="flex mb-2">
                  <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Inizio:</label>
                    <input type="date" id="start-date-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    <input type="time" id="start-time-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
              </div>

              <div class="flex">
                <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Fine:</label>
                  <input type="date" id="end-date-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                  <input type="time" id="end-time-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
            </div>

          </div>
          <!--<button id="saveTimeButton" type="button" class="text-blue-700 dark:text-blue-500 text-sm hover:underline p-0">Invia</button>-->
        </div>    
        
        <button id = "axisSettingsToggle-{{chartIndex}}" data-dropdown-toggle="axisSettings-{{chartIndex}}"
         class="px-2 py-1.5 text-xs font-medium text-gray-900  dark:text-white  hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg">
          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v15a1 1 0 0 0 1 1h15M8 16l2.5-5.5 3 3L17.273 7 20 9.667"/>
          </svg>                                           
        </button> 
        
        <!-- Axis dropdown menu -->
        <div id="axisSettings-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700 p-1">

            <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
              <label for="chart-{{chartIndex}}-min" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Min:</label>
              <input id="chart-{{chartIndex}}-min" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
              <label for="chart-{{chartIndex}}-max" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Max:</label>
              <input id="chart-{{chartIndex}}-max" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div> 
            <div" 
            class="flex w-full items-center p-1 text-sm font-medium text-red-600 border-t border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
            <label class="inline-flex items-center me-5 cursor-pointer">
              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Imposta</span>
              <input type="checkbox" value="" class="sr-only peer" onclick="updateYAxis(this,'{{chartIndex}}')" >
              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>
            </label> 
          </div>                       
          <!--<button id="saveTimeButton" type="button" class="text-blue-700 dark:text-blue-500 text-sm hover:underline p-0">Invia</button>-->
        </div>  

        <button data-dropdown-toggle="chartSeries-{{chartIndex}}" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
          </svg>                                                                                                        
        </button>
        <!-- Axis dropdown menu -->
        <div id="chartSeries-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700">

          <ul id="charts-list-{{chartIndex}}" class=" px-1 py-1 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownSearchButton">
          </ul>
          <button onclick="deleteSeries('chart-timeline-{{chartIndex}}','charts-list-{{chartIndex}}')" 
          class="flex w-full items-center p-3 text-sm font-medium text-red-600 border-t border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
           Elimina misura
          </button>
      </div>                                          
      </div>
      

    <!--
    {% if chartIndex > 1 %}
    <button id="close-chart-{{chartIndex}}"  hx-get="/webscada/delete/" hx-vals='{"chartIndex":{{chartIndex}}}' hx-swap="outerHTML" hx-target="#chart-{{chartIndex}}"
        type="button" class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"> 
        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
        </svg>
    </button>
  
    {% endif %}  -->
  </div>
  </div>
<div class="px-4 grid grid-cols-3 gap-4">
    <div class="col-span-3 ...">
        <div id="chart-{{chartIndex}}">

        <div id="chart-timeline-{{chartIndex}}" class="chart-timeline">
        
        </div>               
      </div> 
</div>
  <div class="..."></div>
  <div class="..."></div>
  <div class="col-span-3 ...">
    <div id="analyticsGrid" class="px-4 ag-theme-quartz pb-16" style="height: 50vh;">
    </div>  
  </div>
</div>
<!--...........Datatable library ..........-->
<script>
  var seriesData = [] 
  var keymap = {}
  datepicker.classList.add('hidden');
  dropdownRadioButton.classList.add('hidden');
  downloadGrid.classList.add('hidden');
  ackButton.classList.add('hidden');
  downloadGrid.classList.remove('hidden');
  resetFilters.classList.remove('hidden');

  //charts index to get all charts
  var chartIndex = 1;
  function getChartsIndex(){
    return chartIndex;
  }
  
//var gridApi = gridInit('#analyticsGrid')   
//console.log(gridApi)
var chartLegend = document.getElementById('chart-legend')
var buttons = chartLegend.querySelectorAll('button');

buttons.forEach(button => {

  if(button.id.includes('zoom')){
      button.addEventListener('click', () => {
        buttons.forEach(btn => {          
          btn.classList.remove('bg-gray-900', 'text-white');
        });
        button.classList.add('bg-gray-900', 'text-white');
      });
    }
});

//grid init method
function gridInit(id){
      var gridElement = document.querySelector(id);    
      document.addEventListener('dark-mode', function(e) {
          if (gridElement.classList.contains('ag-theme-quartz-dark')) {
              // If dark theme is applied, switch to light theme
              gridElement.classList.remove('ag-theme-quartz-dark');
              gridElement.classList.add('ag-theme-quartz');
          } else {
              // If light theme is applied, switch to dark theme
              gridElement.classList.remove('ag-theme-quartz');
              gridElement.classList.add('ag-theme-quartz-dark');
          }    
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                  gridElement.classList.remove('ag-theme-quartz');
                  gridElement.classList.add('ag-theme-quartz-dark');
              } else {
                  gridElement.classList.remove('ag-theme-quartz-dark');
                  gridElement.classList.add('ag-theme-quartz');
              }
              var localeText = JSON.parse(localStorage.getItem('grid-it'));
      
      var gridOptions = {
              // Row Data: The data to be displayed.
              rowData : [],
              // Column Definitions: Defines the columns to be displayed.
              columnDefs: [],
              defaultColDef: {
                          flex: 1,
                      }, 
                      //enableRangeSelection: true,
        //enableCharts: true,
        localeText: localeText,
        rowSelection: "multiple",                
              };        
      var gridApi = agGrid.createGrid(gridElement, gridOptions); 
      
      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      } 
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      return gridApi;
}

async function zoomLocalChart(e,days,id) {
  
  var endDate = new Date();
  var startDate = new Date(); 

  endDate.setDate(endDate.getDate() + 1); 
  startDate.setDate(endDate.getDate() - days); // Calculate 'today - X days'

  let myChart = echarts.init(document.getElementById(id));
  try {
    var seriesData = myChart.getOption().series;  
    console.log(seriesData)
  } catch (error) {
    console.warn(error)
    return
  }
  index = parseInt(id.split("-").pop());
  for (var i = 0; i < seriesData.length; i++) {
      let earlyDate;

      /*if(seriesData[i].dataType == 'DIN'| seriesData[i].dataType == 'DOUT'){
        console.log('xxxx',seriesData[i].data[0].value);

        earlyDate = new Date(seriesData[i].data[0].value[1]);
      }else{
        earlyDate = new Date(Date.parse(seriesData[i].data[0][0]));
      }*/
      earlyDate = new Date(Date.parse(seriesData[i].data[0][0]))
      /*if(earlyDate > startDate){
        htmx.ajax('GET', `/webscada/chart/${days}/${seriesData[i].tag}?chartIndex=${index}`, {swap:'none', indicator:"#spinner"})
      }*/
  }

  myChart.dispatchAction({
      type: 'dataZoom',
      startValue: startDate.toISOString().slice(0, 10), // Format as 'YYYY-MM-DD'
      endValue: endDate.toISOString().slice(0, 10) // Format as 'YYYY-MM-DD'
  });

}

async function clearLocalChart(index,id) {
        console.log(seriesData,index,id)
        seriesData[index] = {}
        chartElement = document.getElementById(id)  
        let myChart = echarts.init(chartElement, null, {
                          renderer: 'canvas',
                          useDirtyRect: false
                      });       
        myChart.setOption({}, true);
        await gridHandle(gridApi)
}
function clearFilters() {
      gridApi.setFilterModel(null);
  } 
function onBtnExport() {
    gridApi.exportDataAsCsv();
  }

function gridHandle(gridApi){
    // Function to transform the object into AG Grid row data
    function transformToRowData(obj) {
              let rowData = [];
              let timestamps = new Set();
            
              // Collect all unique timestamps
              for (let key in obj){
                  Object.values(obj[key]).forEach(series => {
                    series.forEach(dataPoint => {
                        timestamps.add(dataPoint[0]);
                    });
                  });
              }
              // Create a map for easy lookup
              let dataMap = {};
              timestamps.forEach(timestamp => {
                  dataMap[timestamp] = { timestamp };
              });
            
              // Populate the map with values from each point
              for (let key in obj){
                  Object.entries(obj[key]).forEach(([point, series]) => {
                    series.forEach(([timestamp, value]) => {
                
                      if (! dataMap[timestamp][point]) {
                        dataMap[timestamp][point] = value;             
                      } else {
                        // Handle duplicate timestamps within the same point if necessary
                        // For example, you could sum the values, take the average, etc.
                      }
                    });
                  });
              }
              // Convert the map to an array of row data
              rowData = Object.values(dataMap);
            
              // Sort by timestamp if necessary
              rowData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
              return rowData;
        }
        
    // Transform the object to row data  
    var cols = []
    cols.push({ field: "timestamp", filter: true, filter: 'agDateColumnFilter', cellStyle: { textAlign: 'center' },
    filterParams: { 
      comparator: (filterLocalDateAtMidnight, cellValue) => {
            const cellDate = new Date(cellValue);
            if (cellDate < filterLocalDateAtMidnight) {
                return -1;
            } else if (cellDate > filterLocalDateAtMidnight) {
                return 1;
            }
            return 0;
        },
      filterOptions : [ 'greaterThan','lessThan','inRange'],
      maxNumConditions:1}})
    for (let key in seriesData){
        for (let title in seriesData[key] ){
          let keyName = keymap[title];
          cols.push({field:title, headerName:keyName , filter: true,cellStyle: { textAlign: 'center' }})
        }
    }          
    // Transform the object to row data          
    let gridRowData = transformToRowData(seriesData);

    //gridApi.refreshCells();
    gridApi.setGridOption("columnDefs",cols);
    gridApi.setGridOption("rowData",gridRowData);
}

var gridApi = gridInit('#analyticsGrid') 
</script>

<script>
  
  var getDb = async () => {
    const duckdb = window.duckdbduckdbWasm;
    // @ts-ignore
    if (window._db) return window._db;
    const JSDELIVR_BUNDLES = duckdb.getJsDelivrBundles();

    // Select a bundle based on browser checks
    const bundle = await duckdb.selectBundle(JSDELIVR_BUNDLES);

    const worker_url = URL.createObjectURL(
      new Blob([`importScripts("${bundle.mainWorker}");`], {
        type: "text/javascript",
      })
    );

    // Instantiate the asynchronus version of DuckDB-wasm
    const worker = new Worker(worker_url);
    // const logger = null //new duckdb.ConsoleLogger();
    const logger = new duckdb.ConsoleLogger();
    const db = new duckdb.AsyncDuckDB(logger, worker);
    await db.instantiate(bundle.mainModule, bundle.pthreadWorker);
    URL.revokeObjectURL(worker_url);
    window._db = db;
    return db;
  }
 console.log('passed')
  function gethist(key,tagname){
    spinner.classList.remove('htmx-indicator');
    spinner.classList.remove('hidden');

    keymap[key]=tagname;
    
    //spinner.classList.remove('hidden');

    getDb().then(async (db) => {
    // Create a new connection
    const conn = await db.connect();

    uri = `${document.baseURI}bivalues/7600/${key}?chartIndex=1/result.parquet`
    query = `SELECT tag, strftime('%Y-%m-%d %H:%M:%S',ts) as ts,value FROM "${uri}"`;
    // Prepare query
    const result = await conn.query(query)

    // Process and use the results
    const results = result.toArray(); 
    await conn.close();
      //all pages series will be contained here 
    let index= 1;
    if (seriesData.length ==0)
        seriesData[index] = {};  
    console.log('xxxx',seriesData) 
    for(let i in results) {
      
      if (!seriesData[index].hasOwnProperty(results[i].tag)) {
          seriesData[index][results[i].tag]= []
        } 
      seriesData[index][results[i].tag].push([results[i].ts, results[i].value])
    }
              
    var data = []
    for (let i in seriesData[index] ){
      data.push({tag:i ,name: keymap[i] ,dataType: 'AIN', data: seriesData[index][i],
       type: 'line',sampling: 'max',smooth: false,symbol: 'none',/* areaStyle: {},*/
       markPoint: {
                    data: [
                    { type: 'max', name: 'Max' },
                    { type: 'min', name: 'Min' }
                    ]
                },            
            markLine: {
                data: [
                    { type: 'average', name: 'Med' }
                ]
            }}) //, "yAxisIndex": 1
    }
    
    let myChart = chartInit('chart-timeline-'+index);
    //myChart.on('finished', updateSeriesDropdown);
    myChart.setOption({
      series: data
      })
    updateSeriesDropdown(myChart.getOption());                        
    // Grid handeling
    gridHandle(gridApi) 
    //spinner.classList.add('hidden');
    
    })    
    spinner.classList.add('htmx-indicator');

}

</script>

<script type="module">
  // Get the current date and time
  var now = new Date();console.log('1',now.toString());
  import * as duckdbduckdbWasm from "https://cdn.jsdelivr.net/npm/@duckdb/duckdb-wasm@1.29.0/+esm";
  now = new Date();
  window.duckdbduckdbWasm = duckdbduckdbWasm;
  getDb().then(async (db) => {
    // Create a new connection
    window.duckdb = await db.connect();
    console.log('DB initilized')       
  });
</script>
<!--
<script>
  var seriesDropdown = document.getElementById('charts-list-' + getChartsIndex());

  function updateSeriesDropdown(chart) {
    console.log('chartssss',chart)
    seriesDropdown.innerHTML = ''; // Clear existing options
    chart.series.forEach(series => {
      console.log('xxx')
      let option = document.createElement('li');
      let html =` 
              <div class="flex items-center p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                <input id="tag-${series.tag}" type="checkbox" value="${series.tag}" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                <label for="tag-${series.tag}" class="w-full ms-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">${series.name}</label>
              </div>
              `
      option.innerHTML = html;  
      seriesDropdown.appendChild(option);
    });
  }

 function deleteSeries(chartId, listId) {
    let myChart = echarts.init(document.getElementById(chartId));
    console.log(myChart.getOption())
    // Update dropdown whenever series are added or removed
    let chartList = document.getElementById(listId)
    let seriesChoices = chartList.querySelectorAll('input');
    seriesChoices.forEach(choice => {
      if(choice.checked){
        let seriesName = choice.checked;
        console.log('namex',choice)
        let option = myChart.getOption();
        option.series = option.series.filter(function (serie) {
              console.log('zzzz',serie,choice.value)
              return serie.tag !== choice.value;
          });
          console.log('result',option)
          myChart.setOption(option, true);
          updateSeriesDropdown(myChart.getOption());
      }
    });
  }

  // Function to update the y-axis
  function updateYAxis(e,id) {
    const min = document.getElementById(`chart-${id}-min`)
    const max = document.getElementById(`chart-${id}-max`)
    const button = document.getElementById(`axisSettingsToggle-${id}`)
    
    if (e.checked ){
      button.classList.remove('text-gray-900','dark:text-white')
      button.classList.add('text-red-900','dark:text-red')

      chart.setOption({
                  yAxis: {
                      min: min.value, // Set the minimum value
                      max: max.value // Set the maximum value
                  }
              });
      }else{
        chart.setOption({yAxis: {}});
        button.classList.add('text-gray-900','dark:text-white')
        button.classList.remove('text-red-900','dark:text-red')
      }
    }
</script>
-->
