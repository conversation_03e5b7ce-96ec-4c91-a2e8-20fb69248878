<div id="context-menu" class="z-10 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
    <div class="p-3 space-y-2">
        <p> Caricamento config ....</p>
        <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
    </div>
</div>
<div id="ioGrid"  hx-get=/webscada/scadaplants hx-swap="none" hx-indicator="#spinner" hx-trigger="load" class="ag-theme-quartz pb-4" style="height:40vh;width: 500px">
</div>  

  <style>
  .ag-header-cell-label {
    justify-content: center;
  }
  .ag-cell {
    justify-content: center;
  }
  .level-0 {
    text-align: center;
    font-weight: bold;
    background-color:#14ce71;
  }
  .level-1 {
    text-align: center;
    font-weight: bold;
    background-color: #a696ee;
  }
  .level-2 {
    text-align: center;
    font-weight: bold;
    background-color: #00b0f5;
  }
  .level-3 {
    font-weight: bold;
    background-color: #f436b5;
  }
  .level-4 {
    font-weight: bold;
    background-color: #91e7ce;
  }
  </style>
  <script>
      //all pages series will be contained here 
      datepicker.classList.add('hidden');
      dropdownRadioButton.classList.add('hidden');
      resetFilters.classList.remove('hidden');
      ackButton.classList.add('hidden');

      //grid init method
      function ioGridInit() {
        var gridElement = document.querySelector('#ioGrid');
        document.addEventListener('dark-mode', function (e) {
          if (gridElement.classList.contains('ag-theme-quartz-dark')) {
            // If dark theme is applied, switch to light theme
            gridElement.classList.remove('ag-theme-quartz-dark');
            gridElement.classList.add('ag-theme-quartz');
          } else {
            // If light theme is applied, switch to dark theme
            gridElement.classList.remove('ag-theme-quartz');
            gridElement.classList.add('ag-theme-quartz-dark');
          }
        });
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          gridElement.classList.remove('ag-theme-quartz');
          gridElement.classList.add('ag-theme-quartz-dark');
        } else {
          gridElement.classList.remove('ag-theme-quartz-dark');
          gridElement.classList.add('ag-theme-quartz');
        }
        var localeText = JSON.parse(localStorage.getItem('grid-it'));
        var lastCellClicked;
        var gridOptions = {
          // Row Data: The data to be displayed.
          rowData: [],
          suppressNoRowsOverlay : true,
          // Column Definitions: Defines the columns to be displayed.
          columnDefs: [],
          defaultColDef: {
            flex: 1,          
          },
          loading: true,
          localeText: localeText,
          suppressHorizontalScroll: false,
          rowSelection: "multiple",
  
          onCellClicked: (e) => {
            let clicked = e.rowIndex.toString() + e.value;
            let ctxMenu = document.getElementById("context-menu");   
            const gridContainer = document.getElementById('ioGrid');
            const rect = gridContainer.getBoundingClientRect();                                     
              if (e.column.getId() == 'tag'){       
                if(lastCellClicked == clicked){
                    ctxMenu.classList.toggle('hidden');
                  }else{
                    htmx.ajax('GET', `/webscada/config/${e.data.numero}/?chartsIndex=-2`, {target:'#context-menu', swap:'innerHTML'}).then(() => {
                              ctxMenu.style.top = `${e.event.pageY - rect.top}px`;
                              ctxMenu.style.left =  `${e.event.pageX  - rect.left}px`;
                    });
                    ctxMenu.classList.remove('hidden');
                    lastCellClicked = clicked;
                  }                                                                                                
              }else{
                  ctxMenu.classList.add('hidden');
              }
        }
                /*,
          rowClassRules: {
            "level-0": params => params.api.getValue("tipo", params.node) == 'AIN',
            "level-1": params => params.api.getValue("tipo", params.node) == 'AOUT',
            "level-2": params => params.api.getValue("tipo", params.node) == 'DIN',
            "level-3": params => params.api.getValue("tipo", params.node) == 'DOUT',
            "level-4": params => params.api.getValue("tipo", params.node) == 'SP'
          }*/
        };
        
  
        var cols = [                
                  { field: "tag",headerName :'nome',filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
                  { field: "pagina", headerName:"impianto", filter: true, minWidth:200, cellStyle: { textAlign: 'center' }},
               ]
        var ioGridApi = agGrid.createGrid(gridElement, gridOptions);
        // Transform the object to row data          
        //let gridRowData = transformToRowData(seriesData)
        ioGridApi.setGridOption("columnDefs", cols);
        return ioGridApi;
      }    
      var ioGridApi = ioGridInit()    
      function onBtnExport() {
        ioGridApi.exportDataAsCsv();
      }
      function clearFilters() {
        ioGridApi.setFilterModel(null);
    }  
      htmx.on('htmx:afterRequest', (msg)=> {     
        if  (msg.detail.pathInfo.responsePath.includes("/scadaplants")){  
          const result = JSON.parse(msg.detail.xhr.response);        
          let seriesData = []
          for (let e in result) {
            seriesData[e] = result[e]
          }

          ioGridApi.setGridOption("rowData", seriesData);    

        } 
      });   
  </script>