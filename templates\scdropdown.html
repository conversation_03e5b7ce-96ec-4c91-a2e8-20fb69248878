
<button id="dropdownBiButton" data-dropdown-toggle="dropdownBiSearch" data-dropdown-placement="bottom" 
 class="flex w-32 py-2 px-4  text-gray-900 rounded-lg justify-center dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group border border-gray-200 dark:border-gray-600">
  Impianti
    
</button>   
<!-- Dropdown menu -->
<div id="dropdownBiSearch" class="absolute left-1 z-100 hidden bg-white rounded-lg shadow w-96 dark:bg-gray-700">
    
    <div class="flex-1 p-3">
        <label for="bi-search" class="sr-only">Search</label>
        <div class="relative">
        <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
            </svg>
        </div>
        <input type="text" id="bi-search" oninput="plantSearch()"  class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Trova impianto">
        </div>
    </div>
    <ul id="bi-ul" class="h-96 px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownBiButton">
        {% for plant in plants %}
        
            <li>
            <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
            <label  hx-get=/webscada/scadabi/{{plant[1]}}  hx-target="#tags-{{plant[1]}}" class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300"  aria-controls="{{plant[1]}}" data-collapse-toggle="{{plant[1]}}">| {{plant[0]}}</label>
            </div>
            <ul id="{{plant[1]}}" class="hidden py-2 space-y-2">
                <div id="tags-{{plant[1]}}"  class="w-full pl-5  ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300" >Caricamento tag...</div>
            </ul>   
        </li>
        {% endfor %}
    </ul>
</div>
 
<script>
    try{
    resetFilters.classList.add('hidden');
    ackButton.classList.add('hidden');}catch{}
    function plantSearch(){
        var input, filter, ul, li, a, i, txtValue;
        input = document.getElementById('bi-search');
        filter = input.value.toUpperCase();
        ul = document.getElementById("bi-ul");
        li = ul.getElementsByTagName('li');
        for (i = 0; i < li.length; i++) {
            label = li[i].getElementsByTagName("label")[0];
            txtValue = label.textContent || label.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                li[i].style.display = "";
            } else {
                li[i].style.display = "none";
            }
        }
    }
</script>
