from multiprocessing import Process
import uvicorn
import schedule
import time
import os
import signal


# Function to run the first app
def run_webscada1():
     uvicorn.run("app:app", host="0.0.0.0", port = 5001, reload=False,reload_excludes=['*.log'], workers=1)

# Function to run the second app
def run_webscada2():
     uvicorn.run("app:app", host="0.0.0.0", port = 5002, reload=False,reload_excludes=['*.log'], workers=1)

# Function to restart a process
def restart_process(process_name, pid):
    print(f"Restarting {process_name}...")
    try:
        os.kill(pid, signal.SIGTERM)  # Try to kill the process
        time.sleep(2)  # Wait before restarting
    except PermissionError:
        print(f"Permission error when trying to kill {process_name} (PID: {pid})")
        print("Attempting alternative shutdown method...")
        try:
            # On Windows, use taskkill as an alternative
            if os.name == 'nt':
                os.system(f'taskkill /F /PID {pid}')
            time.sleep(2)
        except Exception as e:
            print(f"Alternative shutdown failed: {e}")
    except ProcessLookupError:
        print(f"Process {pid} not found, it may have already terminated")
    except Exception as e:
        print(f"Error restarting {process_name}: {e}")
    
    # Create and start a new process regardless of kill success
    if process_name == 'webscada1':
        process = Process(target=run_webscada1, daemon=True, name=process_name)
    elif process_name == 'webscada2':
        process = Process(target=run_webscada2, daemon=True, name=process_name)
    
    process.start()
    print(f"Restarted {process_name} with PID: {process.pid}")
    
    # Return the new process so we can update the reference
    return process


if __name__ == "__main__":
    # Define processes for both apps
    process1 = Process(target=run_webscada1, daemon=True, name='webscada1')
    process2 = Process(target=run_webscada2, daemon=True, name='webscada2')

    # Start both processes
    process1.start()
    print(f"Started webscada1 with PID: {process1.pid}")
    time.sleep(30)
    process2.start()
    print(f"Started webscada2 with PID: {process2.pid}")

    # Scheduling restarts with process reference updates
    def restart_webscada1():
        global process1
        process1 = restart_process("webscada1", process1.pid)
        
    def restart_webscada2():
        global process2
        process2 = restart_process("webscada2", process2.pid)

    # Schedule the restarts using the new functions
    schedule.every().day.at("01:45").do(restart_webscada1)
    schedule.every().day.at("04:00").do(restart_webscada2)
    schedule.every(1).hours.do(restart_webscada1)
    schedule.every(1).hours.do(restart_webscada2)

    while True:
        try:
            schedule.run_pending()
        except Exception as e:
            print(f"Scheduler error: {e}")
        time.sleep(1)  # Wait a second before checking the schedule
        
