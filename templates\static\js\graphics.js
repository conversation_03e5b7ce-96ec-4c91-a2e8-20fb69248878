
function colorNameToRGBA(colorName) {
    // Create a new option element to apply the color to
    const optionElement = new Option();
    optionElement.style.color = colorName;
  
    // Append the option element to the body to ensure the style is computed
    document.body.appendChild(optionElement);
  
    // Get the computed color style
    const rgbColor = window.getComputedStyle(optionElement).color;
  
    // Remove the option element from the body
    document.body.removeChild(optionElement);
  
    // Extract the numeric values of the RGB color
    let rgbValues = rgbColor.match(/\d+/g).map(Number);
  
    rgbValues.push(1);

    return rgbValues;
  }
  
function getPixel(imageData, x, y) {
  if (x < 0 || y < 0 || x >= imageData.width || y >= imageData.height) {
    return [-1, -1, -1, -1];  // impossible color
  } else {
    const offset = (y * imageData.width + x) * 4;
    return imageData.data.slice(offset, offset + 4);
  }
}
  
function setPixel(imageData, x, y, color) {
  const offset = (y * imageData.width + x) * 4;
  imageData.data[offset + 0] = color[0];
  imageData.data[offset + 1] = color[1];
  imageData.data[offset + 2] = color[2];
  //imageData.data[offset + 3] = color[0];
}
  
function colorsMatch(a, b) {
  let thres = 25;//20
  return a[0] >= b[0]-thres && a[0] <= b[0]+thres
          && a[1] >= b[1]-thres && a[1] <= b[1]+thres
          && a[2] >= b[2]-thres && a[2] <= b[2]+thres
          //&& a[3] >= b[3]-thres && a[3] <= b[3]+thres
}

function floodFill(ctx, x, y, fillColor) {
  // read the pixels in the canvas
  const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
  
  // get the color we're filling
  const targetColor = getPixel(imageData, x, y);
  
  // check we are actually filling a different color
  if(typeof targetColor !== 'undefined'){ // avoid artifacts
    if (!colorsMatch(targetColor, fillColor)) {
      // Use non-recursive implementation
      fillPixelIterative(imageData, x, y, targetColor, fillColor);
      
      // put the data back
      ctx.putImageData(imageData, 0, 0);
    }
  }
}

// Non-recursive flood fill using a queue
function fillPixelIterative(imageData, startX, startY, targetColor, fillColor) {
  const width = imageData.width;
  const height = imageData.height;
  
  // Create a queue for pixels to process
  const queue = [];
  queue.push([startX, startY]);
  
  // Create a visited map to avoid processing the same pixel twice
  // Using a Uint8Array is much more memory efficient than an object
  const visited = new Uint8Array(width * height);
  
  // Process pixels until queue is empty
  while (queue.length > 0) {
    // Get next pixel from queue
    const [x, y] = queue.shift();
    
    // Skip if out of bounds
    if (x < 0 || y < 0 || x >= width || y >= height) {
      continue;
    }
    
    // Calculate index in the visited array
    const index = y * width + x;
    
    // Skip if already visited
    if (visited[index]) {
      continue;
    }
    
    // Mark as visited
    visited[index] = 1;
    
    // Get current color
    const currentColor = getPixel(imageData, x, y);
    
    // If color matches target, fill it and add neighbors to queue
    if (colorsMatch(currentColor, targetColor)) {
      setPixel(imageData, x, y, fillColor);
      
      // Add 4-connected neighbors to queue
      // Using unshift for depth-first behavior which can be more efficient for some shapes
      queue.push([x + 1, y]);
      queue.push([x - 1, y]);
      queue.push([x, y + 1]);
      queue.push([x, y - 1]);
    }
  }
}

// Keep the recursive version as a fallback but don't use it by default
function fillPixel(imageData, x, y, targetColor, fillColor) {
  const currentColor = getPixel(imageData, x, y);
  if (colorsMatch(currentColor, targetColor)) {
    setPixel(imageData, x, y, fillColor);
    fillPixel(imageData, x + 1, y, targetColor, fillColor);
    fillPixel(imageData, x - 1, y, targetColor, fillColor);
    fillPixel(imageData, x, y + 1, targetColor, fillColor);
    fillPixel(imageData, x, y - 1, targetColor, fillColor);
  }
}

// Optimize the fill function to batch operations
function fill(ctx, result, coordinates) {
  // Create a map to group fill operations by color
  const fillOperations = new Map();
  
  for (let e in coordinates) { 
    let data = coordinates[e];
    let states = data[2];
    
    // Process each tag
    for (let tag in states) {
      if (!result[tag]) continue;
      
      // Handle "fuoriscansione" tags
      if (result[tag].description === 'F.Sc.') {
        const key = '128,128,128,1'; // Gray color for F.Sc.
        if (!fillOperations.has(key)) {
          fillOperations.set(key, []);
        }
        fillOperations.get(key).push([data[0], data[1]]);
        continue;
      }
      
      // Process normal tags
      for (let i in states[tag]) {
        let boolState = 0;
        if (states[tag][i].state === 'true') {
          boolState = 1;
        }
        
        if (result[tag].value === boolState) {
          try {
            const rgba = states[tag][i].rgba;
            const key = rgba.join(',');
            
            if (!fillOperations.has(key)) {
              fillOperations.set(key, []);
            }
            
            fillOperations.get(key).push([data[0], data[1]]);
            
            // Handle blinking elements
            if (states[tag].blink === 'true') {
              blink[tag] = {
                'x': data[0],
                'y': data[1],
                'on': [255, 0, 0, 1],
                'off': [0, 255, 0, 1]
              };
            }
          } catch (e) {
            console.log(e, states[tag][i]);
          }
        }
      }
    }
  }
  
  // Process fill operations by color to reduce the number of getImageData/putImageData calls
  fillOperations.forEach((points, colorKey) => {
    const color = colorKey.split(',').map(Number);
    
    // Get image data once per color
    const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
    let modified = false;
    
    // Fill all points with this color
    for (const [x, y] of points) {
      const targetColor = getPixel(imageData, x, y);
      
      if (typeof targetColor !== 'undefined' && !colorsMatch(targetColor, color)) {
        fillPixelIterative(imageData, x, y, targetColor, color);
        modified = true;
      }
    }
    
    // Only put image data back if it was modified
    if (modified) {
      ctx.putImageData(imageData, 0, 0);
    }
  });
}

// Enhanced worker with mobile-specific optimizations
var ctxWorker;
var canvasOffscreen;
var mobileMode = false;
var cachedImageData = null;
var canvasWidth = 0;
var canvasHeight = 0;

// Optimized fill function specifically for mobile
function optimizedMobileFill(result, coordinates) {
  if (!cachedImageData) {
    console.error("No cached image data available for mobile fill");
    return;
  }
  
  // Create a copy of the cached image data to work with
  const imageData = new ImageData(
    new Uint8ClampedArray(cachedImageData.data),
    cachedImageData.width,
    cachedImageData.height
  );
  
  // Group fill operations by color to minimize processing
  const fillOperations = new Map();
  
  // Process coordinates and group by color
  for (let e in coordinates) { 
    let data = coordinates[e];
    if (!data || !Array.isArray(data) || data.length < 3) continue;
    
    let x = Math.floor(parseFloat(data[0]));
    let y = Math.floor(parseFloat(data[1]));
    let states = data[2];
    
    if (isNaN(x) || isNaN(y) || !states) continue;
    
    for (let tag in states) {
      if (!result[tag]) continue;
      
      // Handle special cases
      if (result[tag].description === 'F.Sc.') {
        const key = '128,128,128,255';
        if (!fillOperations.has(key)) {
          fillOperations.set(key, []);
        }
        fillOperations.get(key).push([x, y]);
        continue;
      }
      
      // Process normal tags
      for (let i in states[tag]) {
        let boolState = 0;
        if (states[tag][i].state === 'true') {
          boolState = 1;
        }
        
        if (result[tag].value === boolState) {
          try {
            let rgba = states[tag][i].rgba;
            if (!Array.isArray(rgba) || rgba.some(isNaN)) continue;
            
            if (rgba.length < 4) rgba.push(255);
            const key = rgba.join(',');
            
            if (!fillOperations.has(key)) {
              fillOperations.set(key, []);
            }
            fillOperations.get(key).push([x, y]);
          } catch (e) {
            console.warn("Error processing tag:", e);
          }
        }
      }
    }
  }
  
  // Process each color group
  let modified = false;
  fillOperations.forEach((points, colorKey) => {
    const color = colorKey.split(',').map(Number);
    
    // Skip invalid colors
    if (color.some(isNaN)) return;
    
    // Process each point with this color
    for (const [x, y] of points) {
      const targetColor = getPixel(imageData, x, y);
      if (targetColor && !colorsMatch(targetColor, color)) {
        // Use scanline fill algorithm which is more efficient for mobile
        scanlineFill(imageData, x, y, targetColor, color);
        modified = true;
      }
    }
  });
  
  // Only send back the image data if it was modified
  if (modified) {
    self.postMessage({
      type: 'mobileUpdate',
      imageData: imageData
    }, [imageData.data.buffer]);
  }
}

// Scanline fill algorithm - more efficient than recursive or queue-based for mobile
function scanlineFill(imageData, startX, startY, targetColor, fillColor) {
  const width = imageData.width;
  const height = imageData.height;
  
  // Stack for storing seed points
  const stack = [[startX, startY]];
  
  while (stack.length > 0) {
    // Pop a point from the stack
    const [x, y] = stack.pop();
    
    // Find the leftmost and rightmost pixels of the current scanline
    let leftX = x;
    while (leftX >= 0 && colorsMatch(getPixel(imageData, leftX, y), targetColor)) {
      leftX--;
    }
    leftX++;
    
    let rightX = x;
    while (rightX < width && colorsMatch(getPixel(imageData, rightX, y), targetColor)) {
      rightX++;
    }
    rightX--;
    
    // Fill the scanline
    for (let i = leftX; i <= rightX; i++) {
      setPixel(imageData, i, y, fillColor);
    }
    
    // Check scanlines above and below for new seed points
    const checkScanline = (newY) => {
      if (newY < 0 || newY >= height) return;
      
      let inRange = false;
      for (let i = leftX; i <= rightX; i++) {
        const currentColor = getPixel(imageData, i, newY);
        const matches = colorsMatch(currentColor, targetColor);
        
        if (!inRange && matches) {
          // Found a new seed point
          stack.push([i, newY]);
          inRange = true;
        } else if (inRange && !matches) {
          // End of a range
          inRange = false;
        }
      }
    };
    
    // Check scanlines above and below
    checkScanline(y + 1);
    checkScanline(y - 1);
  }
}

self.addEventListener('message', async function(e) {
  try {
    const data = e.data;
    
    // Handle mobile initialization
    if (data.mobileInit) {
      mobileMode = true;
      cachedImageData = data.imageData;
      canvasWidth = data.width;
      canvasHeight = data.height;
      console.log("Worker initialized in mobile mode");
      return;
    }
    
    // Handle desktop initialization with OffscreenCanvas
    if (data.canvas) {
      mobileMode = false;
      canvasOffscreen = data.canvas;
      ctxWorker = canvasOffscreen.getContext("2d", { willReadFrequently: true });
      
      if (data.imgblob) {
        try {
          const img = await createImageBitmap(data.imgblob);
          ctxWorker.drawImage(img, 0, 0);
          // Cache the initial image data for potential fallback
          cachedImageData = ctxWorker.getImageData(0, 0, canvasOffscreen.width, canvasOffscreen.height);
          canvasWidth = canvasOffscreen.width;
          canvasHeight = canvasOffscreen.height;
        } catch (error) {
          console.error("Error creating image bitmap:", error);
        }
      }
      return;
    }
    
    // Handle fill operations
    if (data.result && data.coordinates) {
      if (mobileMode) {
        // Use mobile-optimized fill
        optimizedMobileFill(data.result, data.coordinates);
      } else if (ctxWorker) {
        // Use desktop fill with OffscreenCanvas
        fill(ctxWorker, data.result, data.coordinates);
      }
    }
  } catch (error) {
    console.error("Error in worker:", error);
    self.postMessage({
      type: 'error',
      message: error.message
    });
  }
}, false);

