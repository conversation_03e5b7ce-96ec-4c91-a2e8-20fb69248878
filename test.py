import pandas as pd
import numpy as np
import faiss
import pickle
import os
from typing import List, Dict, Any
from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.embeddings import Embeddings
from langchain_core.retrievers import BaseRetriever
from langchain_core.callbacks.manager import CallbackManagerForRetrieverRun
import requests
import json
import logging
from pydantic import Field

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Custom OpenAI Embeddings class that doesn't rely on langchain-community
class CustomOpenAIEmbeddings(Embeddings):
    def __init__(self, model: str = "text-embedding-ada-002", openai_api_base: str = None, openai_api_key: str = None):
        self.model = model
        self.openai_api_base = openai_api_base or "https://api.openai.com/v1"
        self.openai_api_key = openai_api_key or "sk-..."
        logger.info(f"Initialized CustomOpenAIEmbeddings with model: {model}")
        
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents using the OpenAI API."""
        logger.info(f"Embedding {len(texts)} documents in batches")
        
        # Process in batches of 32 documents
        batch_size = 32
        all_embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(texts)-1)//batch_size + 1} with {len(batch)} documents")
            
            # Create batch request
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.openai_api_key}"
            }
            
            payload = {
                "model": self.model,
                "input": batch
            }
            
            logger.info(f"Sending batch request to {self.openai_api_base}/embeddings")
            response = requests.post(
                f"{self.openai_api_base}/embeddings",
                headers=headers,
                json=payload
            )
            
            if response.status_code != 200:
                logger.error(f"Error from API: {response.text}")
                raise ValueError(f"Error from OpenAI API: {response.text}")
            
            data = response.json()
            batch_embeddings = [item["embedding"] for item in data["data"]]
            logger.info(f"Received {len(batch_embeddings)} embeddings in batch")
            
            all_embeddings.extend(batch_embeddings)
        
        logger.info(f"Completed embedding {len(texts)} documents")
        return all_embeddings
    
    def embed_query(self, text: str) -> List[float]:
        """Embed a query using the OpenAI API."""
        logger.info(f"Embedding query: {text[:50]}...")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.openai_api_key}"
        }
        
        payload = {
            "model": self.model,
            "input": text
        }
        
        logger.info(f"Sending request to {self.openai_api_base}/embeddings")
        response = requests.post(
            f"{self.openai_api_base}/embeddings",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            logger.error(f"Error from API: {response.text}")
            raise ValueError(f"Error from OpenAI API: {response.text}")
        
        data = response.json()
        embedding = data["data"][0]["embedding"]
        logger.info(f"Received embedding with dimension: {len(embedding)}")
        return embedding

# Simple FAISS vector store implementation
class SimpleVectorStore:
    def __init__(self, embeddings: Embeddings):
        self.embeddings = embeddings
        self.index = None
        self.documents = []
        logger.info("Initialized SimpleVectorStore")
        
    def add_documents(self, documents: List[Document]):
        """Add documents to the vector store."""
        if not documents:
            logger.warning("No documents provided to add_documents")
            return
        
        logger.info(f"Adding {len(documents)} documents to vector store")
        
        # Get embeddings for documents
        texts = [doc.page_content for doc in documents]
        logger.info("Getting embeddings for documents")
        embeddings = self.embeddings.embed_documents(texts)
        logger.info(f"Received {len(embeddings)} embeddings")
        
        # Create or update FAISS index
        if self.index is None:
            dimension = len(embeddings[0])
            logger.info(f"Creating new FAISS index with dimension {dimension}")
            self.index = faiss.IndexFlatL2(dimension)
        
        # Add embeddings to index
        embeddings_np = np.array(embeddings, dtype=np.float32)
        logger.info(f"Adding embeddings to FAISS index, shape: {embeddings_np.shape}")
        self.index.add(embeddings_np)
        
        # Store documents
        self.documents.extend(documents)
        logger.info(f"Vector store now contains {len(self.documents)} documents")
        
    def similarity_search(self, query: str, k: int = 4) -> List[Document]:
        """Search for documents similar to the query."""
        if self.index is None or not self.documents:
            logger.warning("Cannot search: index is None or documents list is empty")
            return []
        
        logger.info(f"Performing similarity search for query: {query[:50]}...")
        
        # Get embedding for query
        logger.info("Getting embedding for query")
        query_embedding = self.embeddings.embed_query(query)
        query_embedding_np = np.array([query_embedding], dtype=np.float32)
        
        # Search in FAISS index
        logger.info(f"Searching FAISS index for top {k} matches")
        distances, indices = self.index.search(query_embedding_np, k)
        
        # Return found documents
        results = [self.documents[i] for i in indices[0]]
        logger.info(f"Found {len(results)} documents")
        for i, (doc, dist) in enumerate(zip(results, distances[0])):
            logger.info(f"Result {i+1}: distance={dist}, content={doc.page_content[:50]}...")
        
        return results
    
    def save_local(self, folder_path: str):
        """Save the vector store to disk."""
        logger.info(f"Saving vector store to {folder_path}")
        os.makedirs(folder_path, exist_ok=True)
        
        # Save the FAISS index
        index_path = os.path.join(folder_path, "index.faiss")
        logger.info(f"Saving FAISS index to {index_path}")
        faiss.write_index(self.index, index_path)
        
        # Save the documents
        docs_path = os.path.join(folder_path, "documents.pkl")
        logger.info(f"Saving documents to {docs_path}")
        with open(docs_path, "wb") as f:
            pickle.dump(self.documents, f)
        
        logger.info("Vector store saved successfully")
    
    @classmethod
    def load_local(cls, folder_path: str, embeddings: Embeddings):
        """Load the vector store from disk."""
        logger.info(f"Loading vector store from {folder_path}")
        instance = cls(embeddings)
        
        # Load the FAISS index
        index_path = os.path.join(folder_path, "index.faiss")
        logger.info(f"Loading FAISS index from {index_path}")
        instance.index = faiss.read_index(index_path)
        
        # Load the documents
        docs_path = os.path.join(folder_path, "documents.pkl")
        logger.info(f"Loading documents from {docs_path}")
        with open(docs_path, "rb") as f:
            instance.documents = pickle.load(f)
        
        logger.info(f"Vector store loaded with {len(instance.documents)} documents")
        return instance
    
    def as_retriever(self, search_kwargs=None):
        """Create a retriever from the vector store."""
        logger.info("Creating retriever from vector store")
        return SimpleRetriever(vectorstore=self, search_kwargs=search_kwargs or {"k": 4})

# Simple Retriever implementation
class SimpleRetriever(BaseRetriever):
    vectorstore: SimpleVectorStore = Field(description="Vector store for retrieval")
    search_kwargs: Dict[str, Any] = Field(default_factory=lambda: {"k": 4}, description="Search parameters")
    
    def __init__(self, vectorstore: SimpleVectorStore, search_kwargs: Dict[str, Any] = None):
        search_kwargs = search_kwargs or {"k": 4}
        super().__init__(vectorstore=vectorstore, search_kwargs=search_kwargs)
        logger.info(f"Initialized SimpleRetriever with search_kwargs: {self.search_kwargs}")
    
    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """Get documents relevant to the query."""
        logger.info(f"Retrieving documents for query: {query[:50]}...")
        results = self.vectorstore.similarity_search(query, k=self.search_kwargs.get("k", 4))
        logger.info(f"Retrieved {len(results)} documents")
        return results

# Custom LLM class for Ollama
class OllamaLLM:
    def __init__(self, model: str = "gemma3:12b", base_url: str = "http://localhost:11434/v1", api_key: str = "ollama"):
        self.model = model
        self.base_url = base_url
        self.api_key = api_key
        logger.info(f"Initialized OllamaLLM with model: {model}")
    
    def invoke(self, prompt: str) -> str:
        """Generate text using Ollama API."""
        logger.info(f"Generating text with model {self.model}")
        logger.info(f"Prompt: {prompt[:100]}...")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ]
        }
        
        logger.info(f"Sending request to {self.base_url}/chat/completions")
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            logger.error(f"Error from Ollama API: {response.text}")
            raise ValueError(f"Error from Ollama API: {response.text}")
        
        data = response.json()
        result = data["choices"][0]["message"]["content"]
        logger.info(f"Generated text: {result[:100]}...")
        return result

# Main execution
logger.info("Starting RAG application")

# Load CSV into DataFrame
logger.info("Loading CSV data")
df = pd.read_csv("reg.csv")
logger.info(f"Loaded DataFrame with shape: {df.shape}")

# Convert each row into a Document
logger.info("Converting DataFrame rows to Documents")
documents = [Document(page_content=str(row.to_dict())) for _, row in df.iterrows()]
logger.info(f"Created {len(documents)} documents")

# Create custom embeddings and vector store
logger.info("Creating embedding model")
embedding = CustomOpenAIEmbeddings(
    model="nomic-embed-text",  # Change to a model Ollama supports
    openai_api_base="http://localhost:11434/v1",
    openai_api_key="ollama"
)

logger.info("Creating vector store")
vectorstore = SimpleVectorStore(embedding)

logger.info("Adding documents to vector store")
vectorstore.add_documents(documents)

logger.info("Creating retriever")
retriever = vectorstore.as_retriever()

# Set up the LLM using Ollama
logger.info("Setting up LLM")
llm = OllamaLLM(
    model="gemma3:12b",
    base_url="http://localhost:11434/v1",
    api_key="ollama"
)

# Define a prompt template
logger.info("Creating prompt template")
template = """
You are an assistant for question-answering tasks. 
Use the following pieces of retrieved context to answer the question. 
If you don't know the answer, just say that you don't know.

Question: {question}
Context: {context}

Answer:
"""
prompt = ChatPromptTemplate.from_template(template)

# Create the RAG chain using the new LangChain architecture
def format_docs(docs):
    formatted = "\n\n".join(doc.page_content for doc in docs)
    logger.info(f"Formatted {len(docs)} documents into context of length {len(formatted)}")
    return formatted

# Define the RAG pipeline
def rag_pipeline(query):
    logger.info(f"Processing query: {query}")
    
    # Retrieve relevant documents
    logger.info("Retrieving relevant documents")
    
    # Use a simpler approach for the run_manager
    try:
        # Try the public method first (recommended)
        docs = retriever.get_relevant_documents(query)
    except Exception as e:
        logger.warning(f"Error using get_relevant_documents: {e}")
        # Fall back to direct call with None as run_manager
        docs = retriever._get_relevant_documents(query, run_manager=None)
    
    logger.info(f"Retrieved {len(docs)} documents")
    
    # Format documents into context
    logger.info("Formatting documents into context")
    context = format_docs(docs)
    
    # Create the prompt
    logger.info("Creating formatted prompt")
    formatted_prompt = prompt.format(question=query, context=context)
    
    # Generate answer using LLM
    logger.info("Generating answer using LLM")
    answer = llm.invoke(formatted_prompt)
    logger.info(f"Generated answer: {answer[:100]}...")
    
    return answer

# Interactive CLI
logger.info("Starting interactive CLI")
print("Ask questions about your CSV. Type 'exit' to quit.")
while True:
    query = input("You: ")
    if query.lower() == "exit":
        logger.info("Exiting application")
        break
    logger.info(f"Received query: {query}")
    answer = rag_pipeline(query)
    print("ScadaAgent:", answer)
