<div id="analyticsGrid"  class="ag-theme-quartz-auto-dark" style="height: 600px;">
  
  <div class="relative  py-4 ">
    <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
      <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
      </svg>
    </div>
    <input type="text" id="grid-filter" class="block  px-4 py-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Cerca...">
  </div>

</div>
<script>
 // Grid Options: Contains all of the data grid configurations
var gridOptions = {
 // Row Data: The data to be displayed.
 rowData: [],
 // Column Definitions: Defines the columns to be displayed.
 columnDefs: [],
 defaultColDef: {
    flex: 1,
  }, 
};
// Your Javascript code to create the data grid
var gridElement = document.querySelector('#analyticsGrid');
var gridApi = agGrid.createGrid(gridElement, gridOptions); 
// Function to apply the quick filter
function quickFilter() {
  var filterValue = document.getElementById('grid-filter').value;
  gridApi.updateGridOptions({ quickFilterText: filterValue })
} 
//document.getElementById('grid-filter').addEventListener('input', quickFilter)
</script>