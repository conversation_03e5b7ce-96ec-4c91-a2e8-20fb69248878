<div  class="h-96  w-full overflow-y-auto text-sm text-gray-700 dark:text-gray-200">
    <table id="alarmsTable" class="max-w-full whitespace-nowrap text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3 text-center">timestamp</th>
                <!--<th scope="col" class="px-6 py-3 text-center">rtu</th>
                <th scope="col" class="px-6 py-3 text-center">tag</th>   -->
                <th scope="col" class="px-6 py-3 text-center">evento</th>
                <th scope="col" class="px-6 py-3 text-center">valore</th>                           

            </tr>
        </thead>
        <tbody >
        </tbody>
        {% if  items%}  
        {%- for value in items -%}  
            <tr class="min-w-max bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    
                <th scope="row" class=" font-medium text-center text-gray-900 whitespace-nowrap dark:text-white">
                    {{value[0]}}
                </th>
                <!--
                <td class=" font-medium text-center">
                    {{value[1]}}
                </td>
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 ">
                    {{value[2]}}
                </td>
                -->
                {% if value[3] == 6 %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  text-white bg-red-700 hover:bg-red-800 focus:ring-red-300  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">
                    all. inferiore
                </td>
                {% endif %}

                {% if value[3] == 16 %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 ">
                    rientro all. inferiore
                </td>
                {% endif %}
                {% if value[3] == 7 %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  text-white bg-red-700 hover:bg-red-800 focus:ring-red-300  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">
                    all. superiore
                </td>
                {% endif %}

                {% if value[3] == 17 %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 ">
                    rientro all. superiore
                </td>
                {% endif %}      
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 ">
                    {{value[4]}}
                </td>                  
            </tr>
            {%- endfor -%}
        {% endif %} 
        {% if  iset%}  
        {%- for value in iset -%}  
        <tr class="min-w-max bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                   
            <th scope="row" class=" font-medium text-center text-gray-900 whitespace-nowrap dark:text-white">
                {{value['timestamp']}}  
            </th>

             {% if value['livello'] == 4 %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  text-white bg-red-700 hover:bg-red-800 focus:ring-red-300  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">
                    {{value['livello']}}
                </td>
            {% elif value['livello'] == 3 %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  text-white bg-yellow-300 hover:bg-yellow-400 focus:ring-yellow-300 dark:focus:ring-yellow-900">
                    {{value['livello']}}
                </td>
            {% else %}
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2">
                    {{value['livello']}}
                </td>
            {% endif %}
            <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2">
                {{value['messaggio']}}
            </td>
            
        </tr>
        {%- endfor -%}
        {% endif %}

    </table>
</div>    
<!--
    <ul  class="h-96 px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200">
        {% for value in items %}
         <li>
            <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
            <label class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">{{value[0]}} | {{value[1]}} | {{value[2]}}</label>
            </div>            
        </li>
                    
        {% endfor %}
    </ul>

-->