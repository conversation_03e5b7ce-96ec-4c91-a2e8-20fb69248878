import logging
from logging.handlers import RotatingFileHandler
import os

# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('webscadadev')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/webscadadev.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*100, backupCount=10)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)