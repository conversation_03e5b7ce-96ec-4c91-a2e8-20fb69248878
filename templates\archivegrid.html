<div id="context-menu" class="z-10 absolute hidden text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
  <div class="p-3 space-y-2">
      <p> Caricamento config ....</p>
      <a  class="flex items-center font-medium text-primary-600 dark:text-primary-500 dark:hover:text-primary-600 hover:text-primary-700"></a>
  </div>
</div>
<div id="archiveGrid"  class="ag-theme-quartz pb-4" style="height:85vh;">
</div>    
<style>
.ag-header-cell-label {
  justify-content: center;
}
.ag-cell {
  justify-content: center;
}
.level-1 {
  text-align: center;
  font-weight: bold;
  background-color: #f44336;
}
.level-2 {
  text-align: center;
  font-weight: bold;
  background-color: #f5a700;
}
.level-3 {
  font-weight: bold;
  background-color: #f5a700;
}
.level-4 {
  font-weight: bold;
  background-color: #f44336;
}
</style>

<script >
    //datepicker.classList.remove('hidden');
    dropdownRadioButton.classList.add('hidden');
    downloadGrid.classList.remove('hidden');
    resetFilters.classList.remove('hidden');
    ackButton.classList.add('hidden');
    function archiveInit() {
      var gridElement = document.querySelector('#archiveGrid');
      document.addEventListener('dark-mode', function (e) {
        if (gridElement.classList.contains('ag-theme-quartz-dark')) {
          // If dark theme is applied, switch to light theme
          gridElement.classList.remove('ag-theme-quartz-dark');
          gridElement.classList.add('ag-theme-quartz');
        } else {
          // If light theme is applied, switch to dark theme
          gridElement.classList.remove('ag-theme-quartz');
          gridElement.classList.add('ag-theme-quartz-dark');
        }
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        gridElement.classList.remove('ag-theme-quartz');
        gridElement.classList.add('ag-theme-quartz-dark');
      } else {
        gridElement.classList.remove('ag-theme-quartz-dark');
        gridElement.classList.add('ag-theme-quartz');
      }
      var localeText = JSON.parse(localStorage.getItem('grid-it'));
      var lastCellClicked;
      var gridOptions = {
        // Column Definitions: Defines the columns to be displayed.
        columnDefs: [],
        defaultColDef: {
          flex: 1,          
        },
        localeText: localeText,
        rowSelection: "multiple",
        rowClassRules: {
          //"level-0": params => params.api.getValue("livello", params.node) == 0,
          "level-1": params => params.api.getValue("livello", params.node) == 1,
          "level-2": params => params.api.getValue("livello", params.node) == 2,
          "level-3": params => params.api.getValue("livello", params.node) == 3,
          "level-4": params => params.api.getValue("livello", params.node) == 4
        },
        // tell grid we want virtual row model type
        rowModelType: "infinite",
        // how big each page in our page cache will be, default is 100
        cacheBlockSize: 100,
        // how many extra blank rows to display to the user at the end of the dataset,
        // which sets the vertical scroll and then allows the grid to request viewing more rows of data.
        // default is 1, ie show 1 row.
        cacheOverflowSize: 2,
        // how many server side requests to send at a time. if user is scrolling lots, then the requests
        // are throttled down
        maxConcurrentDatasourceRequests: 1,
        // how many rows to initially show in the grid. having 1 shows a blank row, so it looks like
        // the grid is loading from the users perspective (as we have a spinner in the first col)
        infiniteInitialRowCount: 10,
        // how many pages to store in cache. default is undefined, which allows an infinite sized cache,
        // pages are never purged. this should be set for large data to stop your browser from getting
        // full of data
        maxBlocksInCache: 10 ,
        /*onFilterChanged: function(e) {
          console.log(gridApi.getFilterModel())
        } */
        onCellClicked: (e) => {
        let clicked = e.rowIndex.toString() + e.value;
        let ctxMenu = document.getElementById("context-menu");                                        
          if (e.column.getId() == 'name'){       
            if(lastCellClicked == clicked){
                ctxMenu.classList.toggle('hidden');
              }else{
                htmx.ajax('GET', `/webscada/config/${e.data.tag}/?chartsIndex=-1`, {target:'#context-menu', swap:'innerHTML'}).then(() => {
                          ctxMenu.style.top = `${e.event.pageY}px`;
                          ctxMenu.style.left =  `${e.event.pageX}px`;
                });
                ctxMenu.classList.remove('hidden');
                lastCellClicked = clicked;
              }                                                                                                
          }else{
              ctxMenu.classList.add('hidden');
          }
        }
      };
      

      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      }
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)

      var cols = [
                  // this row shows the row index, doesn't use any data from the row
                  {
                    headerName: "ID",
                    maxWidth: 100,
                    cellStyle: { textAlign: 'center' },
                    // it is important to have node.id here, so that when the id changes (which happens
                    // when the row is loaded) then the cell is refreshed.
                    valueGetter: "node.id",
                    cellRenderer: (params) => {
                      if (params.value !== undefined) {
                        return params.value;
                      } else {
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><style>.spinner_z9k8{transform-origin:center;animation:spinner_StKS .75s infinite linear}@keyframes spinner_StKS{100%{transform:rotate(360deg)}}</style><path d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z" fill="#E1EFFE" opacity=".25"/><path d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z" fill="#1A56DB" class="spinner_z9k8"/></svg>';
                      }
                    },
                  },        
                { field: "timestamp", filter: true,minWidth:200, filter: 'agDateColumnFilter', filterParams: { filterOptions : ['equals', 'greaterThan','lessThan','inRange'],maxNumConditions:1},cellStyle: { textAlign: 'center' }},
                { field: "livello" , filter: true,filter: 'agNumberColumnFilter', filterParams: { filterOptions : ['equals', 'greaterThan','lessThan'],maxNumConditions:1}, maxWidth:100, cellStyle: { textAlign: 'center' }},
                { field: "name" , headerName: 'Canale', filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "messaggio" , filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "periferica" , filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }},
                { field: "utente" , headerName: 'UserAck',filter: true,filterParams: { filterOptions : ['contains', 'equals'],maxNumConditions:1}, minWidth:200, cellStyle: { textAlign: 'center' }}
              ]
      var gridApi = agGrid.createGrid(gridElement, gridOptions);
      // Transform the object to row data          
      //let gridRowData = transformToRowData(seriesData)
      gridApi.setGridOption("columnDefs", cols);
      return gridApi;
    }
  
  var gridArchive = archiveInit()
  function onBtnExport() {
      gridArchive.exportDataAsCsv();
  }   
  function clearFilters() {
      gridArchive.setFilterModel(null);
  }  
  var inputElements = document.querySelectorAll('input');  
  var datepicker = document.getElementById('datepicker-form');
  var dataSource = {
                     rowCount: undefined, // behave as infinite scroll
                     getRows: (params) => {
                            //console.log("limit " + params.startRow + " offset " + params.endRow,);
                            query = getQuery()
                            query['offset'] = params.startRow 
                            query['limit'] = params.endRow                                                         
                            fetch('histalarms?' + new URLSearchParams(query).toString())
                              .then((response) => response.json())
                              .then(function (seriesData) {
                                      // take a slice of the total rows
                                      const rowsThisPage = seriesData.slice(params.startRow, params.endRow);
                                      // if on or after the last page, work out the last row.
                                      let lastRow = -1;
                                      if (seriesData.length <= params.endRow) {
                                        lastRow = seriesData.length+1;
                                      }
                                      
                                      // call the success callback
                                      params.successCallback(rowsThisPage, lastRow);
                                      
                              })
                            },
                      };
// Set the datasource on the grid
gridArchive.setGridOption("datasource", dataSource); 
  var dispatch = function(event) {
      //const customEvent = new Event("dateevent");
      //datepicker.dispatchEvent(customEvent); 
      gridArchive.setGridOption("datasource", dataSource); 
  };

  // Add a 'changeDate' ondatetime range picker
  inputElements.forEach(function(input) {
      input.addEventListener('changeDate', dispatch);
  });
  
  function getQuery(){
    let obj = {}
    inputElements.forEach(function(input) {
        if(input.name.length > 0){
          obj[input.name] = input.value;
        }      
    });
    
    obj['offset'] = 0
    obj['limit'] = 9

    //filter builder
    let model = gridArchive.getFilterModel()
    console.log(model)
    for(let e in model){
      for(let filter in model[e])
        console.log(e,filter)
    }

    obj['filter'] = JSON.stringify(model)
    console.log('filer is :',model)
    return obj
  }
  
  function clearInputs(){
    inputElements.forEach(function(input) {
      input.value =""
   });
  }
  </script>
