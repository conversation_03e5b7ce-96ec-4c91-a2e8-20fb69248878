import leafmap.foliumap as leafmap

geopmap = leafmap.Map(center=(46.0748, 11.1217),
                       zoom=10,
                       draw_control=False,
                       measure_control=False,
                       fullscreen_control=False,
                       attribution_control=True,)

glogrtu_df = glogrtu_df[['NOME','INDIRIZZO_RTU','INDIRIZZO_IP','NUMERO_TELEFONO','UBICAZIONE','LATITUDINE','LONGITUDINE']]
geopmap.add_points_from_xy(
    glogrtu_df,
    x="LONGITUDINE",
    y="LATITUDINE",
    #color_column='region',
    icon_names=['gear', 'map', 'leaf', 'globe'],
    #spin=True,
    add_legend=True,
)  


geopmap.add_basemap("HYBRID")
html = geopmap.to_html()