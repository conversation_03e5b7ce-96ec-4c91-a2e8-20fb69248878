<div class="p-3 space-y-2">
{% for data in items %}
<div id="accordion-{{ loop.index }}" data-accordion="collapse" data-active-classes="dark:text-white text-gray-900" data-inactive-classes="text-gray-500 dark:text-gray-400">
    <h2 id="accordion-heading-{{ loop.index }}">
      <button type="button" class="flex items-center justify-between w-full  font-medium rtl:text-right text-gray-500  dark:text-gray-400 gap-3" data-accordion-target="#accordion-body-{{ loop.index }}" aria-expanded="false" aria-controls="accordion-body-{{ loop.index }}">
        <span>{{data["Nome"]}}</span>
        <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
        </svg>
      </button>
    </h2>
    <div id="accordion-body-{{ loop.index }}" class="hidden mx-4" aria-labelledby="accordion-heading-{{ loop.index }}">
      <div class="py-1 border-b border-gray-200 dark:border-gray-700">
                    <!---->
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Impianto</h3>
                            <p>{{data["Plant"]}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Motore</h3>
                            <p>{{data["Engine"]}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Tag</h3>
                            <p>{{data["Tag"]}}</p>
                        </div>                           
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Valore</h3>
                            <p>{{data["value"]}} {{data["UMisura"]}}</p>
                        </div>   
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Timestamp</h3>
                            <p>{{data["ts"]}}</p>
                        </div>                               
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Modo</h3>
                            <p>{{data["ModoOp"]}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Tipo</h3>
                            <p>{{data["Type"]}}</p>
                        </div> 
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Numero</h3>
                            <p>{{data["Number"]}}</p>
                        </div> 
                        {% if data["Type"] == 'AIN' or data["Type"] == 'AOUT' or data["Type"] == 'SP' %}  
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Unità di Misura</h3>
                            <p>{{data["UMisura"]}}</p>
                        </div>                          
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Allarme inferiore</h3>
                            <p>{{data["AllarmeInf"]["Valore"]|round}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Allarme Superiore</h3>
                            <p>{{data["AllarmeSup"]["Valore"]|round}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">PreAllarme inferiore</h3>
                            <p>{{data["PAllarmeSup"]["Valore"]|round}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">PreAllarme superiore</h3>
                            <p>{{data["PAllarmeInf"]["Valore"]|round}}</p>
                        </div>
                        {% else %}
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Stato riposo</h3>
                            <p>{{data["DNormale"]}}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-900 dark:text-white">Stato eccitato</h3>
                            <p>{{data["DAllarme"]}}</p>
                        </div>      
                        {% endif %}                  
                        <button id="{{data["Tag"]+'-dropPage'}}" data-dropdown-toggle="{{data["Tag"]+'-pagine'}}" data-dropdown-placement="right" 
                        class="inline-flex items-center justify-center w-full my-2 spx-5 py-2 me-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">     
                        Pagine
                        <div id="{{data["Tag"]+'-pagine'}}" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                            <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby=""{{data["Tag"]+'-dropPage'}}"">
                            {% for n in data["cross"] %}
                                <li>
                                    <label hx-get=/webscada/page/{{n.indice}} hx-indicator="#spinner" hx-trigger="click" hx-target="#panel"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white text-left">{{n.pagina}}</label>
                                </li>
                            {% endfor %}   
                            </ul>
                        </div>   
                        </button>
                        <div class="flex items-center justify-between">
                        <button data-modal-target="global-modal" data-modal-toggle="global-modal" onclick="restoreChart()"
                            class="inline-flex items-center justify-center w-full px-5 py-2 me-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                            
                            <svg class="w-4 h-4 gray-500  hover:blue-700 dark:gray-400 dark:hover:white me-2 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor" viewBox="0 0 16 20">
                                <path fill-rule="evenodd"
                                    d="M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5H9Zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Zm-1 9a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0v-2Zm2-5a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1Zm4 4a1 1 0 1 0-2 0v3a1 1 0 1 0 2 0v-3Z"
                                    clip-rule="evenodd" />
                            </svg>
                            Globale
                        </button>                    

                        <button id={{data["Tag"]+'-button'}} data-dropdown-toggle={{data["Tag"]+'-menu'}}  data-dropdown-placement="right" 
                            class="inline-flex items-center justify-center w-full px-5 py-2 me-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                            <svg class="w-4 h-4 gray-500 hover:blue-700 dark:gray-400 dark:hover:white me-2 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor" viewBox="0 0 16 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 15v4m6-6v6m6-4v4m6-6v6M3 11l6-5 6 5 5.5-5.5" />
                            </svg>
                            Grafica
                            <div id={{data["Tag"]+'-menu'}}  class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby={{data["Tag"]+'-button'}}>
                                {% if charts > 0 %}        
                                    {% for n in range(charts) %}
                                    <li>
                                        <label hx-get=/webscada/chart/1/{{data["Tag"]}}?chartIndex={{n+1}} hx-indicator="#spinner"  hx-trigger="click" hx-swap="none"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white text-left">Grafico {{n+1}} </label>
                                    </li>
                                {% endfor %}   
                            {% endif %}          
                                <li>
                                    <label data-modal-target="global-modal" data-modal-toggle="global-modal"  hx-get=/webscada/globalchart/1/{{data["Tag"]}}?chartIndex=1 hx-indicator="#spinner" hx-trigger="click" hx-swap="none"  class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white text-left">Grafico globale</label>
                                </li>
                                </ul>
                            </div>        
                        </button>   
                        </div>  

                    <!---->
       </div>
    </div>

</div>
{% endfor %}
</div>