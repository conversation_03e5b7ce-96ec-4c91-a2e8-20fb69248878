<div id='notes-container' class="fixed bottom-6 end-6 z-30 w-96" >
    <button type="button" onclick="showNotes(false)" class=" flex min-h-screen items-center p-4 justify-between text-gray-600 dark:text-white bg-gray-200 dark:bg-gray-600 rounded-lg w-full h-8 items-center ">
      <div class="text-sm font-normal text-gray-500 lex dark:text-gray-300"> Ticket id <span class="font-semibold text-gray-900 dark:text-white">{{data["id"]}}</span> creato da <span class="font-semibold text-gray-900 dark:text-white">{{data["user"]}}</span></div>
      <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
  
    </button>
    
    <div class="bg-gray-200 dark:bg-gray-600 rounded-lg shadow-md p-4  overflow-y-auto" style="height:34rem;">

<ol class="relative border-s border-gray-200 dark:border-gray-700 ml-3 w-128">     

    <li class="mb-4 ms-6">
        <span class="absolute flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full -start-3 ring-8 ring-white dark:ring-gray-900 dark:bg-blue-900">
            <svg class="w-2.5 h-2.5 text-blue-800 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
            </svg>
        </span>
        <div class=" p-2 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600">
            <div  class="flex-col mb-2 sm:flex">
                <div class="text-xs text-gray-900 dark:text-white font-small">Ticket creato
                </div>
                <div class="text-xs font-small text-gray-500 lex dark:text-gray-300">{{info.creato}}</div>
            </div>
            <div class="p-2 text-sm italic font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300">{{info.richiesta}}</div>           
            {% for file in info.attachments %}
                <a href="/webscada/download/{{file}}" class="text-sm font-small text-blue-600 dark:text-blue-500 hover:underline">{{file.replace("helpdesk.attachments.","")}}</a>
            {% endfor %}
        </div>
    </li>

    {% for values in result %}
    <li class="mb-4 ms-6">
        <span class="absolute flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full -start-3 ring-8 ring-white dark:ring-gray-900 dark:bg-blue-900">
            <svg class="w-2.5 h-2.5 text-blue-800 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
            </svg>
        </span>
        <div class=" p-2 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600">
            <div  class="flex-col mb-2 sm:flex">
                <div class="text-xs text-gray-900 dark:text-white ">{{values.from}} </div>
                {% for event  in values.event %}
                    <div class="text-xs text-gray-900 dark:text-white ">{{event}}</div>
                {% endfor %}
                <div class="text-xs font-normal text-gray-500 lex dark:text-gray-300">{{values.ts}}</div>
            </div>
            {% if values.message|length > 0 %}
            <div class="p-2 text-sm italic font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300">{{values.message}}</div>           
            {% endif %}
            {% for file in values.attachments %}
                <a href="/webscada/download/{{file}}" class="text-xs font-small text-blue-600 dark:text-blue-500 hover:underline">{{file.replace("helpdesk.attachments.","")}}</a>
            {% endfor %}
        </div>
    </li>
    {% endfor %}
    <li id="last-row"></li>
    <li class="ms-6">
        <form hx-post="/webscada/sendnote/{{data['id']}}/{{data['user']}}" enctype="multipart/form-data"  hx-indicator="#spinner" hx-target="#last-row" class="w-full mb-4 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
            <div class="px-4 py-2 bg-white rounded-t-lg dark:bg-gray-800">
                <label for="message" class="sr-only">il tuo messaggio</label>
                <textarea name ="message" id="message" rows="4" class="w-full px-0 text-sm text-gray-900 bg-white border-0 dark:bg-gray-800 focus:ring-0 dark:text-white dark:placeholder-gray-400" placeholder="Scrivi un messaggio..." required="required" ></textarea>
            </div> 
            
            <div class="flex items-center justify-between px-3 py-2 border-t dark:border-gray-600">
                <button type="submit" 

                 class="inline-flex justify-center items-center p-2 text-gray-500 rounded cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-600">
                    <svg class="w-4 h-4 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
                    </svg>                      
                </button>

                    <div  class="flex ps-0 space-x-1 rtl:space-x-reverse sm:ps-2">
                        <label 
                          for="file-upload"
                         class="flex-row justify-center items-center p-2 text-gray-500 rounded cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-600">
                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 20">
                                <path stroke="currentColor" stroke-linejoin="round" stroke-width="2" d="M1 6v8a5 5 0 1 0 10 0V4.5a3.5 3.5 0 1 0-7 0V13a2 2 0 0 0 4 0V6"/>
                            </svg>
                            <input id="file-upload" name="files" type="file" hidden multiple/>
                            <p id="file-names"></p>
                        </label>                      
                    </div>
                </div>
            <div id="upload-result"></div>
        </form>
    </li>
</ol>

</div>
</div>
<script>
    function getMessage(id,user){
        var text = document.getElementById('message');
        var result = {};
        result['message'] = text.value;
        result['id'] = id;
        result['email'] = user;

        return result;
    }
    var fileInput = document.getElementById('file-upload');
  var fileNamesElement = document.getElementById('file-names');

  fileInput.addEventListener('change', () => {
    const fileNames = Array.from(fileInput.files).map(file => file.name).join(', ');
    fileNamesElement.textContent = fileNames;
  });
</script>