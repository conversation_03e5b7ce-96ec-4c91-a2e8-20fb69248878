import clr
import System
from System.Resources import <PERSON>Reader
#from System.Drawing.Bitmap import
from System.IO import MemoryStream
import base64
import os

if not os.path.exists('iset\\resources'):
    os.makedirs('iset\\resources')

def bitmap_to_base64(bitmap):
    # Create a MemoryStream to save the bitmap as a PNG
    memory_stream = MemoryStream()
    
    # Save the bitmap as a PNG to the MemoryStream
    bitmap.Save(memory_stream, System.Drawing.Imaging.ImageFormat.Png)
    
    # Get the binary data from the MemoryStream
    binary_data = memory_stream.ToArray()

    # Encode the binary data to Base64
    base64_data = base64.b64encode(bytes(binary_data)).decode('utf-8')    
    return base64_data

def extract_resource(file_path):
    result = dict()
    try:
        # Load the resource file with explicit read-only FileStream
        file_stream = System.IO.FileStream(file_path, System.IO.FileMode.Open, System.IO.FileAccess.Read, System.IO.FileShare.Read)
        reader = ResourceReader(file_stream)
        for entry in reader:
            try:
                if not isinstance(entry.Value, int ):
                    if not os.path.isfile(f"iset\\resources\\{entry.Key}.png"):
                        entry.Value.Save(f"iset\\resources\\{entry.Key}.png")
                    result[entry.Key] = bitmap_to_base64(entry.Value)
            except Exception as e:
                print('error',e)
    except Exception as e:
        print('resb file parsing error',e)
    finally:
        file_stream.Close()
        reader.Close()
    return result

