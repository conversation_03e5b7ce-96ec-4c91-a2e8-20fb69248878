import logging
import sqlite3

class SQLiteHandler(logging.Handler):
    def __init__(self, db_path):
        super().__init__()
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        self._create_table()

    def _create_table(self):
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT,
                message TEXT,
                timestamp TEXT
            )
        ''')
        self.conn.commit()

    def emit(self, record):
        log_entry = self.format(record)
        self.cursor.execute('''
            INSERT INTO logs (level, message, timestamp)
            VALUES (?, ?, ?)
        ''', (record.levelname, log_entry, record.created))
        self.conn.commit()

    def close(self):
        self.conn.close()
        super().close()

# Configure the logger
logger = logging.getLogger('sqlite_logger')
logger.setLevel(logging.DEBUG)

# Create SQLiteHandler and set formatter
sqlite_handler = SQLiteHandler('logs.db')
formatter = logging.Formatter('%(message)s')
sqlite_handler.setFormatter(formatter)

# Add handler to logger
logger.addHandler(sqlite_handler)

# Log some messages
logger.debug('This is a debug message')
logger.info('This is an info message')
logger.warning('This is a warning message')
logger.error('This is an error message')
logger.critical('This is a critical message')
