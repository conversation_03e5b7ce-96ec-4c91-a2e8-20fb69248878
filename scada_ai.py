import json
import duckdb
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import httpx
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.embeddings import OpenAIEmbeddings
import asyncio
import logging

logger = logging.getLogger(__name__)

class ScadaAI:
    def __init__(self, appstate):
        self.appstate = appstate
        self.embeddings = OpenAIEmbeddings(openai_api_base=appstate.appconfig.get('ollama', {}).get('url', 'http://localhost:11434/v1'))
        self.vector_store = None
        self.metadata_index = {}
        self.initialize_vector_store()
        
    def initialize_vector_store(self):
        """Initialize the vector store with metadata from rtumap and plant mappings"""
        try:
            # Create metadata index from rtumap and plant mappings
            with duckdb.connect() as conn:
                conn.register_filesystem(self.appstate.fs)
                
                # Query rtumap for tag metadata
                rtumap_query = "SELECT * FROM rtumap"
                rtumap_data = conn.query(rtumap_query).fetchdf()
                
                # Query plant mappings
                plant_query = "SELECT * FROM crossref"
                plant_data = conn.query(plant_query).fetchdf()
                
                # Join the data to create rich metadata
                metadata_docs = []
                
                # Process rtumap data
                for _, row in rtumap_data.iterrows():
                    doc = f"Tag: {row['tag']} - Name: {row['name']} - Description: {row['description']} "
                    if 'limits' in row and row['limits']:
                        doc += f"- Limits: {row['limits']} "
                    if 'alarms' in row and row['alarms']:
                        doc += f"- Alarms: {row['alarms']} "
                    
                    metadata = {
                        "tag": row['tag'],
                        "name": row['name'],
                        "type": "tag_metadata"
                    }
                    
                    metadata_docs.append((doc, metadata))
                    self.metadata_index[row['tag']] = metadata
                
                # Process plant mapping data
                for _, row in plant_data.iterrows():
                    doc = f"Plant: {row['page']} - Tag: {row['tag']}"
                    metadata = {
                        "plant": row['page'],
                        "tag": row['tag'],
                        "type": "plant_mapping"
                    }
                    metadata_docs.append((doc, metadata))
                
                # Create vector store
                texts = [doc for doc, _ in metadata_docs]
                metadatas = [meta for _, meta in metadata_docs]
                
                self.vector_store = FAISS.from_texts(texts, self.embeddings, metadatas=metadatas)
                logger.info("Vector store initialized with metadata")
                
        except Exception as e:
            logger.error(f"Error initializing vector store: {e}")
    
    async def process_time_series(self, tag, start_date, end_date):
        """Process time series data for a specific tag and time range"""
        try:
            with duckdb.connect() as conn:
                conn.register_filesystem(self.appstate.fs)
                
                # Format dates for Hive partitioning
                start_year = start_date.year
                start_month = start_date.month
                end_year = end_date.year
                end_month = end_date.month
                
                # Query the time series data
                query = f"""
                SELECT tag, ts, value 
                FROM read_parquet('history/{start_year}/{start_month:02d}/*.parquet', 
                                 'history/{end_year}/{end_month:02d}/*.parquet')
                WHERE tag = '{tag}'
                AND ts BETWEEN '{start_date}' AND '{end_date}'
                ORDER BY ts
                """
                
                df = conn.query(query).fetchdf()
                
                if df.empty:
                    return None
                
                # Calculate statistical features
                stats = {
                    "mean": df['value'].mean(),
                    "std": df['value'].std(),
                    "min": df['value'].min(),
                    "max": df['value'].max(),
                    "count": len(df),
                    "start_ts": df['ts'].min(),
                    "end_ts": df['ts'].max()
                }
                
                # Check for trends
                if len(df) > 1:
                    first_half = df['value'][:len(df)//2].mean()
                    second_half = df['value'][len(df)//2:].mean()
                    if second_half > first_half * 1.1:
                        stats["trend"] = "increasing"
                    elif second_half < first_half * 0.9:
                        stats["trend"] = "decreasing"
                    else:
                        stats["trend"] = "stable"
                
                return stats
                
        except Exception as e:
            logger.error(f"Error processing time series data: {e}")
            return None
    
    async def query_ai(self, user_query, context_data):
        """Query the AI with the user's question and context data"""
        try:
            # Format the context data
            context = json.dumps(context_data, indent=2)
            
            # Create the prompt
            prompt = f"""
            You are an AI assistant for a SCADA system. Answer the following question based on the provided context.
            
            Context information:
            {context}
            
            User question: {user_query}
            
            Provide a concise and helpful response based only on the information provided.
            """
            
            # Call Ollama API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.appstate.appconfig.get('ollama', {}).get('url', 'http://localhost:11434/v1')}/chat/completions",
                    json={
                        "model": self.appstate.appconfig.get('ollama', {}).get('model', 'llama3'),
                        "messages": [
                            {"role": "system", "content": "You are a helpful SCADA system assistant."},
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.7
                    },
                    timeout=30.0
                )
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except Exception as e:
            logger.error(f"Error querying AI: {e}")
            return f"I encountered an error processing your request. Please try again later."

# Initialize the ScadaAI instance
async def init_scada_ai(appstate):
    appstate.scada_ai = ScadaAI(appstate)
    return appstate.scada_ai

# Process user queries
async def process_scada_query(query, auth_data):
    try:
        # Get the ScadaAI instance
        scada_ai = appstate.scada_ai
        
        # Search for relevant metadata in the vector store
        search_results = scada_ai.vector_store.similarity_search_with_score(query, k=3)
        
        # Extract relevant tags and plants from search results
        relevant_tags = []
        relevant_plants = []
        
        for doc, score in search_results:
            if score < 0.8:  # Only consider relevant matches
                metadata = doc.metadata
                if metadata.get("type") == "tag_metadata" and metadata.get("tag") not in relevant_tags:
                    relevant_tags.append(metadata.get("tag"))
                if metadata.get("type") == "plant_mapping" and metadata.get("plant") not in relevant_plants:
                    relevant_plants.append(metadata.get("plant"))
        
        # Get time series data for relevant tags
        now = datetime.now()
        start_date = now - timedelta(days=7)  # Last week's data
        
        time_series_data = {}
        for tag in relevant_tags:
            stats = await scada_ai.process_time_series(tag, start_date, now)
            if stats:
                time_series_data[tag] = stats
        
        # Prepare context for AI
        context = {
            "relevant_tags": relevant_tags,
            "relevant_plants": relevant_plants,
            "time_series_data": time_series_data,
            "metadata": {tag: scada_ai.metadata_index.get(tag, {}) for tag in relevant_tags}
        }
        
        # Query the AI
        response = await scada_ai.query_ai(query, context)
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        return "I'm sorry, I couldn't process your request at this time."