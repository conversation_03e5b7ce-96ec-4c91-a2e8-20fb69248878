import xml.etree.ElementTree as ET
import asyncio
import json
import re
from .isetwidgets import ISETSynPanel
import os
from .isetresource import extract_resource


def reverse_bits(num, bits=10):
  binary_string = bin(num)[2:].zfill(bits)
  reversed_binary = binary_string[::-1]
  reversed_num = int(reversed_binary, 2)
  return reversed_num


def get_tree(user = None, ui_path = None):
    pages = {}
    indice = 0
    html = ""
    i = 0
    button_id = "main-menu"

    #fetch root element
    root = ET.parse(f'{ui_path}pages.config').getroot()

    #get all groups
    groups = dict()
    profiles = root.findall('.//profile')
    for e in profiles:
        profile = e.attrib
        groups[profile['code']] = {'name': profile['name'],'group' : profile['groups.cfg']}

    #getpage tree
    tree = root.findall('.//page')

    for e in tree:
            page = e.attrib
            try:    
                #grant key
                
                if 'ply' in page:

                    page_grant = page['ply']#.lstrip("0x")
                    user_grant = None
                    if user:
                        for access in user['profile']['DR']['group']:
                            if access['name'] == 'LAY':
                                user_grant = access['right']
                        #skip to the next iteration if the grant logical and operation is not successful
                        number = int(page_grant,16)
                        reversed_number = reverse_bits(number,bits=10) 
                        if(user_grant & reversed_number) == 0 : #!= user_grant
                            if page.get('node','') == 'Open':       
                                indice += 1                                  
                                html += f'<li>\n<ul>\n'
                            continue  
                #----------------------------------------------------------------------------------------------
                if 'name' in page.keys(): 
                    name = page['name']      

                if 'node' in page.keys():                            
                    if page['node'] == 'Open':                              
                            indice += 1 
                            pages[i]  = {name:indice}
                            icon = ""
                            if indice == 1:
                                html += f'''<li>\n<button aria-controls="{button_id}" data-collapse-toggle="{button_id}" 
                                            class="flex items-center p-2 w-full text-base font-normal text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700">
                                            <svg class="flex-shrink-0 w-6 h-6 text-gray-400 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                            <path fill-rule="evenodd" d="M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6Zm2 8v-2h7v2H4Zm0 2v2h7v-2H4Zm9 2h7v-2h-7v2Zm7-4v-2h-7v2h7Z" clip-rule="evenodd"/></svg><span class="flex-1 ml-3 text-left whitespace-nowrap">{name}</span>
                                            <svg aria-hidden="true" class="w-6 h-6 ml-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>                                
                                            <ul id="{button_id}" class="hidden py-2 space-y-2">'''                                                                    
                            else:    
                                html += f'''<li>\n<button aria-controls="{button_id}" data-collapse-toggle="{button_id}" 
                                            class="flex items-center pl-5 p-2 w-full text-base font-normal text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700"><span class="flex-1 ml-3 text-left whitespace-nowrap">{name}</span>
                                            <svg aria-hidden="true" class="w-6 h-6 ml-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>                                
                                            <ul id="{button_id}" class="hidden pl-5 py-2 space-y-2">'''                            
                         
                            button_id = f'{name.replace(" ","-").lower()}-{i}'                     
                    if page['node'] == 'Close':       
                        indice -= 1 
                        html += f'</ul>\n</li>\n'

                if 'index' in page.keys():     
                     index = int(page['index'])
                     key =  f"page.{index}.config" 
                     if index < 1000:
                        key =  f"page.0{index}.config"                     
                     if index < 100:
                        key =  f"page.00{index}.config" 
                     if index < 10:
                        key =  f"page.000{index}.config" 

                     pages[key] = f"{index} | {name}"
                     html += f'<li class="pl-5"><button  hx-get=/webscada/page/{key} hx-indicator="#spinner" data-drawer-hide="drawer-navigation" hx-target="#panel" class="flex items-center pl-5 p-2 w-full text-base font-normal text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700">{index} | {name}</button></li>\n' 
                i +=1
            except Exception as e:
                print(e)

    return (pages,html,groups)

def tag_to_topic(tag):
    try:
        engine = tag.split("::")[0]
        tag_adr = tag.split("::")[1]

        NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
        types =  ["AIN","DIN","AOUT","DOUT","SP"]
        type_idx = int(tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)])
        type = types[type_idx]
        index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])

        topic = f"iset.config.{engine}.{NLogico}.{type}.{index}"
    except:
        topic = ""
    
    return topic

async def read_analytics(path = None, config_dict = None, ui_path = None):
    if path is None:
        tree = get_tree()[0] 
    else:
        tree = path
    #tree = {'0': {'suap':'page.1730.config'}} example 
    pages = {}
    #mapkv = await nc.key_value(bucket = 'iset')
    
    for index,name in tree.items():
        #check if this is a navigation menu   
        elements = {}        
        try:            
            file = ET.parse(f'{ui_path}{index}')
            root = file.getroot()
            title = root.attrib['title']
            elements = {title:{}}
            ui = elements[title]
            i = 0
            objs = root.findall('objects')
            
            for child in objs:

                for subchild in child:

                    if  subchild.tag != 'ISETExtendedList':
                        continue

                    ui[i] = {}
                    if subchild.tag == 'ISETExtendedList':
                        
                        for ssubchild in subchild:                           
                                if ssubchild.tag == 'Lines':
                                    ui[i]['list']={'rows':ssubchild.attrib['value']} 
                        i += 1
                    if subchild.tag == 'ISETStandardGraph':
                        ui[i] = {subchild.tag:"graph"}
                        i += 1
            
            tags = {}  
            isettags = {}
            i = 0          
            for e in elements[title]:
                element = elements[title][e]
                tags[i] = {}
                if 'list' in element.keys():
                    ls = element['list']['rows']
                    rows = re.findall('{(.+?)}',ls)
                    for row in rows:
                         element = row.split(',')
                         description = ""
                         for el in element:     
                            #tag                      
                            if 'id=' in el:
                                key =  el.replace("id=","").replace("'","").strip()  
                                topic = tag_to_topic(key)
                                isettags[key]=""
                                if len(topic) > 0 and len(description) == 0:
                                    try:                                                                            
                                        tags[i][key] = config_dict[topic]['Nome']
                                    except:
                                        tags[i][key]= ''
                                else:
                                    tags[i][key] = description
                            #title   
                            if 'text=' in el and len(element) == 1:
                                key =  el.replace("text=","").replace("'","").strip() 
                                tags[i][key] = 'title'

                            elif 'text=' in el and len(element) > 1:
                                key =  el.replace("text=","").replace("'","").strip() 
                                description = key 

                         '''
                         row = row.replace("text=","").replace("id=","").replace("'","")
                         head, sep, tag = row.partition(',')
                         head = head.strip()
                         if 'ley' in tag:
                             tag =""
                         #the split is a workaround for tags with weird attributes
                         tag = tag.split(',')[0].strip()                         
                         if len(tag) == 0:
                            if len(head) > 0:
                              try:
                                #get tag name from  nats config topic
                                topic = tag_to_topic(head)
                                if len(topic) > 0:
                                    rawtag = await nc.get(topic)
                                    tagobj = json.loads(rawtag.value.decode())
                                    tags[i][head] = tagobj["Nome"]
                                    isettags[head]=""
                                else:
                                    tags[i][head] = 'title'
                              except:  
                                tags[i][head] = "title"                              
                         else:                                                        
                             if len(head) > 0:
                                if 'ley=' not in head: 
                                    tags[i][tag] = head
                                    isettags[tag]=""
                                else:
                                    tags[i][head] = "title"
                        '''
                    i +=1 

            #return tags 
            
            pages[name] = {'title':title, 'tags':tags}
            if path:           
                #await nc.put(base64.b64encode(name.encode()).decode("utf-8"),json.dumps(pages[name]).encode())
                return {'title':title, 'isetpanel':tags, 'tags':isettags}
        except Exception as e:
            print(e)
    return pages

async def read_synoptics(path = None, ui_path = None):
    if path is None:
        tree = get_tree()[0] 
    tree = path
    for index,name in tree.items():
        try:
            page = {}
            page[index]= {}
            if index != 'dummy_page':#dummy if
                file = ET.parse(f'{ui_path}{index}')
                root = file.getroot()
                title = root.attrib['title']
                page.update({index:{'title':title}})
                objs = root.findall('objects')
                for child in objs:
                    for subchild in child:
                        if subchild.tag == 'ISETExtendedList':
                            raise ValueError("this is an analytics page")
                        root_widget = subchild.tag.lower()
                        page[index].update({root_widget:{}})

                        for ISETSyn_Panel in subchild:

                            #root container for the page with it's attributes
                            page[index][root_widget][ISETSyn_Panel.tag.lower()] = ISETSyn_Panel.attrib.get('value')
                            page[index][root_widget].update({'imgurl': 'img/'})

                            if ISETSyn_Panel.tag == 'objects':
                                i = 0
                                widget_props = {}
                                for element in ISETSyn_Panel:
                                    #graphical widget 
                                    widget_props[i] = {'type':element.tag.lower()}     
                                    
                                    #id for multiimage object
                                    id = element.attrib.get('id')
                                    props = {}                       
                                    for prop in element:                                     
                                        #widget_props[index].update({'title':title})
                                        props.update({prop.tag.lower() : prop.attrib.get('value')}) 
                                        if id:
                                            props.update({'id' : id}) 
                                        #graphical widget properties                                
                                    widget_props[i].update({'props':props})
                                    i += 1
                        page[index][root_widget].update({'widgets':widget_props})

                #check if this page have resource such as images 
                file_path = f"{ui_path}{index.replace('config','resb')}"
                resource = None
                if os.path.isfile(file_path):
                       resource =  extract_resource(file_path)
                panel = ISETSynPanel(page,resource)
                if path:
                    return{'svg':panel.svg,'tags':panel.tags}
                else:                    
                    return 'no page'
        except Exception as e:
            #print(e)
            pass
                
def pageparser(url = None):
    asyncio.run(read_synoptics(url))

if __name__ == '__main__':
    pageparser()

