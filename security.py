from fastapi.security import H<PERSON><PERSON>uthorizationCredentials, HTT<PERSON><PERSON>ear<PERSON>
from fastapi import Security, Request
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Union
from jose import jwt
import pyotp
import asyncio
from logger import logger

def reverse_bits(num, bits=10):
  binary_string = bin(num)[2:].zfill(bits)
  reversed_binary = binary_string[::-1]
  reversed_num = int(reversed_binary, 2)
  return reversed_num

#data = {"<EMAIL>":"1234","<EMAIL>":"4321"}
class AuthHandler():
    def __init__(self, appstate):
        self.appstate = appstate

    security = HTTPBearer()
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    ALGORITHM = 'HS256'
    ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24
    totp = pyotp.TOTP(pyotp.random_base32(),interval=180)

    async def decode_token(self, token):
        secret_key = self.appstate.iset.get('iset.user.jwtkey','')
        counter = 0
        while len(secret_key) == 0:
            try:
                data = await self.appstate.nc.get('iset.user.jwtkey')
                secret_key = data.value
                self.appstate.iset['iset.user.jwtkey'] = secret_key
            except Exception as e:
                logger.warning(e)
            await asyncio.sleep(0.5)
            counter += 1

            if counter > 10:
                counter = 0
                logger.warning('Auth secret key not found')
                raise RequiresLoginException()
        try:

            payload = jwt.decode(token, secret_key, algorithms=self.ALGORITHM)            
            return payload['sub']
        except jwt.ExpiredSignatureError:
            logger.warning('Auth token expired')
            raise RequiresLoginException()
        except jwt.JWTError as e:
            logger.warning(f'Auth JWT error {e}')
            raise RequiresLoginException()
        except Exception as e:
            logger.warning(f'Auth generic error {e}')
            raise RequiresLoginException()
    
    async def auth_request_wrapper(self, request : Request, auth: HTTPAuthorizationCredentials = Security(security)):
        relative_url = str(request.url).replace(str(request.base_url),'')

        creds = dict()
        creds['grant'] = {'webscada':False,'helpdesk':False}  
        creds['email'] = await self.decode_token(auth.credentials)
        
        #page reload or main page call
        key = f"iset.user.{creds['email'].replace('@','').replace('.','')}"
        self.appstate.iset[f'{key}.lastseen'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            await self.appstate.nc.put(f'{key}.lastseen',self.appstate.iset[f'{key}.lastseen'].encode())
        except Exception as e:
            logger.warning(f'lastseen persistence {e}')
        
        if len(relative_url)>0:
            creds['lasturl'] = relative_url 
            excluded_paths = ['gisanalytics', 'gisdata', 'scadaplants']                  
            if not any(excluded_path in relative_url for excluded_path in excluded_paths):                 
                self.appstate.iset[f'{key}.lastpage'] = relative_url
                try:
                    await self.appstate.nc.put(f'{key}.lastpage', creds['lasturl'].encode())
                except Exception as e:
                    logger.warning(f'lastpage persistence {e}')                
        else:
            last_page = self.appstate.iset.get(f'{key}.lastpage','')
            if isinstance(last_page, bytes):
                creds['lasturl'] = last_page.decode()
            else:
                creds['lasturl'] = last_page       

        emailkey = creds["email"].lower().replace("@",".")
        #admin
        key = f'iset.user.admin.{emailkey}'
        if  key in self.appstate.iset:
            creds['grant'] = {'webscada':True,'helpdesk':True,'admin':True}
            return creds
        
        #users        
        key = f'iset.user.{emailkey}' 
        if key in self.appstate.iset: 
            item = self.appstate.iset[key]
            creds['grant']['webscada'] = item['webscada'] 
            creds['grant']['helpdesk'] = item['helpdesk']         
             
        return creds
    
    async def auth_wrapper(self,  auth: HTTPAuthorizationCredentials = Security(security)):
        creds = dict()
        creds['grant'] = {'webscada':False,'helpdesk':False}  
        creds['email'] = await self.decode_token(auth.credentials)

        key = f"iset.user.{creds['email'].replace('@','').replace('.','')}"
        self.appstate.iset[f'{key}.lastseen'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            await self.appstate.nc.put(f'{key}.lastseen',self.appstate.iset[f'{key}.lastseen'].encode())
        except Exception as e:
            logger.warning(f'lastseen persistence {e}')

        emailkey = creds["email"].lower().replace("@",".")
        #admin
        key = f'iset.user.admin.{emailkey}'
        if  key in self.appstate.iset:
            creds['grant'] = {'webscada':True,'helpdesk':True,'admin':True}
            return creds
        
        #users        
        key = f'iset.user.{emailkey}' 
        if key in self.appstate.iset: 
            item = self.appstate.iset[key]
            creds['grant']['webscada'] = item['webscada'] 
            creds['grant']['helpdesk'] = item['helpdesk']         
             
        return creds

    async def ws_auth_wrapper(self,  websocket):
        creds = dict()
        creds['grant'] = {'webscada':False,'helpdesk':False}  
        creds['email'] = await self.decode_token(websocket.cookies['Authorization'])

        key = f"iset.user.{creds['email'].replace('@','').replace('.','')}"
        self.appstate.iset[f'{key}.lastseen'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            await self.appstate.nc.put(f'{key}.lastseen',self.appstate.iset[f'{key}.lastseen'].encode())
        except Exception as e:
            logger.warning(f'lastseen persistence {e}')

        emailkey = creds["email"].lower().replace("@",".")
        #admin
        key = f'iset.user.admin.{emailkey}'
        if  key in self.appstate.iset:
            creds['grant'] = {'webscada':True,'helpdesk':True,'admin':True}
            return creds
        
        #users        
        key = f'iset.user.{emailkey}' 
        if key in self.appstate.iset: 
            item = self.appstate.iset[key]
            creds['grant']['webscada'] = item['webscada'] 
            creds['grant']['helpdesk'] = item['helpdesk']         
                
        return creds
      
    def get_acl(self,email):
        creds = dict()
        creds['email'] = email
        creds['grant'] = {'webscada':False,'helpdesk':False, 'alarms':True, 'archive':True, 
                          'ack':False, 'write':False, 'setpoint':False, 'update':False, 'cctv':False}  
        """
        key = f"iset.user.{creds['email'].replace('@','').replace('.','')}"
        self.appstate.iset[f'{key}.lastseen'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            self.appstate.nc.put(f'{key}.lastseen',self.appstate.iset[f'{key}.lastseen'].encode())
        except Exception as e:
            logger.warning(f'lastseen persistence {e}')
        """
        emailkey = email.lower().replace("@",".")
        #match iset user with webuser grants
        key = f'iset.user.{emailkey}' 
        user = None
        if key in self.appstate.iset:
            email_user = self.appstate.iset[key]
            for item in self.appstate.users:            
                if item['usr_id'].lower() == email_user['id']:
                    user = item
                    user['alarmgroup'] = email_user.get('group','')
                    creds['session'] = email_user.get('sessione','24')
                    creds['id'] = int(email_user['id'])+1
                    break      
                 
        #admin
        key = f'iset.user.admin.{emailkey}'        
        if  key in self.appstate.iset:
            email_user = self.appstate.iset[key]
            creds['grant'] = {'webscada':True,'helpdesk':True,'admin':True,'alarms':True, 'archive':True, 
                              'ack':True, 'write':True, 'setpoint':True, 'update':True,'user':user,'cctv':True}
            creds['grant']['alarmgroup'] = ''
            creds['session'] = email_user.get('sessione','24')
            return creds

        #legacy iset mask based grant system ! could be better....!   
        reversed_number = 0
        if user:
            for access in user['profile']['DR']['group']:
                if access['name'] == 'OPR':
                    user_grant = access['right'] 
                    reversed_number = reverse_bits(user_grant,bits=13) 
                    #print(reversed_number,bin(reversed_number))
        key = f'iset.user.{emailkey}'       
        if key in self.appstate.iset: 
            item = self.appstate.iset[key]
            creds['grant']['webscada'] = item['webscada'] 
            creds['grant']['helpdesk'] = item['helpdesk'] 
            creds['grant']['cctv'] = item.get('cctv',False) 

            mask = 1 << 7
            creds['grant']['alarms'] = True#(reversed_number & mask) != 0 
            creds['grant']['archive'] = True#(reversed_number & mask) != 0 
            mask = 1 << 5
            creds['grant']['ack'] = (reversed_number & mask) != 0 
            mask = 1 << 1
            creds['grant']['write'] = (reversed_number & mask) != 0 
            mask = 1 << 2
            creds['grant']['setpoint'] = (reversed_number & mask) != 0 
            mask = 1 << 12            
            creds['grant']['update'] = (reversed_number & mask) != 0 
            creds['grant']['user'] = user
            creds['grant']['alarmgroup'] = user['alarmgroup']

            return creds      

    def get_presence(self,email):
        key = f"iset.user.{email.replace('@','').replace('.','')}"
        presence = self.appstate.iset.get(f'{key}.lastseen','')
        result = dict()
        for key,item in self.appstate.iset.items():
            if 'lastseen' in key:
                result[key] = item
        return result

    def create_access_token(self,
        subject: Union[str, Any], expires_delta: timedelta = None
    ) -> str:
        secret_key = self.appstate.iset.get('iset.user.jwtkey')
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes= self.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        to_encode = {"exp": expire, "sub": str(subject)}
        encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=self.ALGORITHM)
        return encoded_jwt

    def verify_otp(self, input):
        verify = self.totp.verify(input, valid_window = 1) 
        logger.info(f'verify code {input}')
        if verify:
            logger.info(f"OTP verification successful")   
        else:
            logger.warning(f"OTP verification failed")  
        return verify
    
    async def validate_email(self, email): 
        emailkey = email.lower().replace("@",".")
        if f'iset.user.admin.{emailkey}' in self.appstate.iset:
            logger.info(f"User {email} validation success")
            return True
        else:
            for key,item in self.appstate.iset.items():
                if "iset.user." in key and not '.info' in key:
                    try:
                        if email == item["email"]:
                            logger.info(f"User {email} validation success")
                            return True
                    except:
                        continue
            logger.info(f"User {email} validation failed")
            return False
                   
    async def authenticate_user(self, username, password):
        try:
            # get admin users from kv store

            user = {'email':username, 'password':password} #User(email = username, password = password)  
            #if user['email'] in data: 
            email = username.lower().replace("@",".")

            if f'iset.user.admin.{email}' in self.appstate.iset.keys():            
                password_check = self.verify_otp(user['password'])
                logger.info(f"Admin user authentication {username}")
                return password_check
            else: 
                for key,item in self.appstate.iset.items():
                    if "iset.user." in key:
                        try:
                            if username == item["email"]:
                                logger.info(f"user authentication {username}")
                                return self.verify_otp(user['password'])
                        except:
                            continue
                return False
            
        except:
            logger.error(f"user {username} authentication failed")
            raise RequiresLoginException()            

    async def getotp(self):   
        otp = self.totp.now()
        return otp

    async def sendotp(self, email):            
        try:   
            otp = self.totp.now()
            logger.info(f'send otp code {otp}')
            topic = "scada.mmm.sendEmail"
            payload = {  "name": "OTP- webSCADA",
                        "dataSet":{
                        "mail_options": {
                            "subject": "Codice accesso webSCADA"
                        },
                        "mail_body": {
                            "dataSet": {
                                "header": [],
                                "rows": [
                                    True,
                                    True
                                ],
                                "code": otp
                            }
                        }
                
                        },
                        "from": "<EMAIL>",
                        "to": [{"text": email}],
                        "template_id": 34, 
                        "is_enable": 1,
                        "is_test": 1,
                        "options": None,
                        "runOnInit": 0
                    }
            #response = await self.appstate.nc.req(topic,json.dumps(payload).encode(),5.0)
            print(otp)
            logger.info(f"OTP sent to {email}")

        except Exception as e:
            logger.error(f"sending OTP to {email}")
    
class RequiresLoginException(Exception):
    pass

class RequiresGrantException(Exception):
    pass