{% extends 'base.html' %}
{% block header %}
<title>Webscada Novareti</title>
<link href="https://unpkg.com/tabulator-tables/dist/css/tabulator.min.css" rel="stylesheet">
<script type="text/javascript" src="https://unpkg.com/tabulator-tables/dist/js/tabulator.min.js"></script>
{% endblock header %}

{% block content %}

{% include 'common/nav.html' %}
<div class="relative overflow-x-auto pt-16" id="alarms-table" hx-ext="sse" hx-swap="none" sse-connect="/rtalarms" sse-swap="message"></div>
<!--
<div class="relative overflow-x-auto pt-16">
    <table id="alarmsTable" class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4">
                    <div class="flex items-center">
                        <input id="checkbox-all" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-all" class="sr-only">checkbox</label>
                    </div>
                </th>
                <th scope="col" class="px-6 py-3 text-center">
                   timestamp
                </th>
                <th scope="col" class="px-6 py-3 text-center">
                    livello
                </th>
                <th scope="col" class="px-6 py-3 text-center">
                    nome
                </th>
                <th scope="col" class="px-6 py-3 text-center">
                    messaggio
                </th>
                <th scope="col" class="px-6 py-3 text-center">
                    periferica
                </th>
                
            </tr>
        </thead>
        <tbody>
            {% for value in items %}
            {% set color = 'bg-white-700' %}
            {% if value[1] == 0 %}
                {% set color = 'bg-white-700' %} 
            {% endif %}

            {% if value[1] == 1 %}
                {% set color = 'bg-white-700' %} 
            {% endif %}

            {% if value[1] == 2 %}
                {% set color = 'text-slate-950 dark:text-black bg-yellow-300 hover:bg-yellow-500 focus:ring-yellow-300 dark:focus:ring-yellow-900' %} 
            {% endif %}

            {% if value[1] == 3 %}
                {% set color = 'text-slate-950 dark:text-white bg-yellow-300 hover:bg-yellow-400 focus:ring-yellow-300 dark:focus:ring-yellow-900' %} 
            {% endif %} 

            {% if value[1] == 4 %}
                {% set color = 'text-white bg-red-700 hover:bg-red-800 focus:ring-red-300  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900' %} 
            {% endif %}                
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="w-4 p-4">
                    <div class="flex items-center">
                        <input id="checkbox-table-1" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-table-1" class="sr-only">checkbox</label>
                    </div>
                </td>
                                                              
                <th scope="row" class=" font-medium text-center text-gray-900 whitespace-nowrap dark:text-white">
                    {{value[0]}}
                </th>
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 {{color}}">
                    {{value[1]}}
                </td>
                <td class=" font-medium text-center">
                    {{value[2]}}
                </td>
                <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  {{color}}">
                    {{value[3]}}
                </td>
                <td class=" font-medium text-center">
                    {{value[4]}}
                </td>
            </tr>
            {% endfor %}

        </tbody>
    </table>
</div>
-->
<script>
    //define some sample data
 var tabledata = [];

 //create Tabulator on DOM element with id "alarms-table"
var table = new Tabulator("#alarms-table", {
 	//height:205, // set height of table (in CSS or here), this enables the Virtual DOM and improves render speed dramatically (can be any valid css height value)
 	//data:tabledata, //assign data to table
    //reactiveData:true,
    //ajaxURL:"/alarmsdata",
    //layout:"fitDataFill",
 	layout:"fitColumns", //fit columns to width of table (optional)
 	columns:[ //Define Table Columns
	 	{title:"TIMESTAMP", field:"timestamp", headerFilter:"input"},
	 	{title:"LIVELLO", field:"livello", hozAlign:"center",width:100, editor:"list", editorParams:{values:{"0":0,"1":1,"2":2,"3":3,"4":4, clearable:true}},headerFilter:true, headerFilterParams:{values:{"0":0,"1":1,"2":2,"3":3,"4":4, "":""}}, clearable:true,responsive:3},
	 	{title:"NOME", field:"name", headerFilter:"input"},
	 	{title:"MESSAGIO",field:"messaggio",hozAlign:"center",headerFilter:"input"},
	 	{title:"PERIFERICA",field:"periferica",hozAlign:"center",headerFilter:"input",responsive:2},        
 	],
     rowFormatter: function(row) {
        var data = row.getData();
        if (data.livello == 4) {
            row.getElement().style.backgroundColor = "#ef4444";
            row.getElement().style.color= "white";
        }
        if (data.livello == 3 || data.livello == 2) {
            row.getElement().style.backgroundColor = "#fde047";
        }         
    }, 
    rowHeader:{headerSort:false,width:50, resizable: false, frozen:true, headerHozAlign:"center", hozAlign:"center", formatter:"rowSelection", titleFormatter:"rowSelection", cellClick:function(e, cell){
      cell.getRow().toggleSelect();
    }},   
});

htmx.on('htmx:sseMessage', (msg)=> {         
          result = JSON.parse(msg.detail.data); 
          table.setData(result);   
          /*for (var obj of result) {
            if (tabledata.some(e => e.timestamp === obj.timestamp && e.name === obj.name && e.messaggio === obj.messaggio)) {
                //console.log('xxxx',obj)
            }
            else{
                tabledata.unshift(obj); 
                console.log(obj);
            }  
        }  */                           
      })

</script>
{% include 'common/footer.html' %}

{% endblock content %}