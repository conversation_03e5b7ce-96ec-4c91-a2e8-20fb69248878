
<div class="flex  justify-center min-w-full rounded-lg">
    <div id="dbsync" class="flex  bg-white ">
               
        <div class="dark:bg-gray-700 dark:border-gray-600 shadow-md  w-full max-w-lg ">
                <div date-rangepicker class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="relative">
                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input name="start" type="text"  id="start-date"  class="bg-gray-50  text-gray-900 text-sm  focus:ring-gray-500 block w-full ps-10 p-2  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Inizio 'anno/mese/giorno'">
                      </div>
                </div>
                <div class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="relative">
                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                            </svg>
                        </div>
                        <input name="end" type="text" id="end-date" class="bg-gray-50  text-gray-900 text-sm  focus:ring-gray-500 block w-full ps-10 p-2  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Fine 'anno/mese/giorno'">
                    </div>
                </div>
                <div   class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="flex items-center ps-3">
                      <input   id="rtuSearchButton" data-dropdown-toggle="rtuSearch" data-dropdown-placement="bottom"  type="radio" value="some" name="list-rtu" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label for="list-radio-license" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Cerca rtu...</label>
                    </div>
                    <!--Dropdown menu -->
                      <div id="rtuSearch" class="absolute hidden bg-white rounded-lg shadow w-48 dark:bg-gray-700">
                          <div class="flex-1 p-3">
                            <label for="input-rtu-search" class="sr-only">Search</label>
                            <div class="relative">
                              <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                              </div>
                              <input type="text" id="input-rtu-search" oninput="rtu_search()"  class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-500 dark:focus:border-gray-500" placeholder="Trova rtu">
                            </div>
                          </div>
                          <ul id="input-rtu-ul" class="h-96 px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="rtuSearchButton">
                              {% for rtu in items %}
                              <li>
                                  <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                    <input   id="{{rtu}}-id"  type="checkbox" value="{{rtu}}" name="{{rtu}}" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
            
                                  <label  class="w-full py-2 ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">{{rtu}}</label>
                                  </div>
                              </li>
                              {% endfor %}
                          </ul>
                      </div>
                </div>                
                <div class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="flex items-center ps-3">
                        <input id="list-radio-pb" type="radio" value="pb" name="list-rtu" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label for="list-radio-pb" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Tutti i PB</label>
                    </div>
                </div>
                <div class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="flex items-center ps-3">
                        <input id="list-radio-gb" type="radio" value="gb" name="list-rtu" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label for="list-radio-gb" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Tutti i GB</label>
                    </div>
                </div>
                <div class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="flex items-center ps-3">
                        <input id="list-radio-all" type="radio" value="all" name="list-rtu" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label for="list-radio-all" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Tutte le RTU</label>
                    </div>
                </div>            
                <div class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                    <div class="flex items-center ps-3">
                        <input id="check-ovveride" type="checkbox" value="ovveride" name="check-ovveride" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label for="list-ovveride" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Sovvrascrivi il MySql</label>
                    </div>
                </div>    
                {% if response %}
                {% include 'log.html'%}
                {% else %}  
                <div class="w-full  rounded-t-lg dark:border-gray-600">
                  <div class="flex ">                  
                      <Button hx-get=/webscada/dbsync/run/  hx-vals='js:{query: sync()}' class="w-full py-3 focus:ring-gray-500 text-sm font-medium text-gray-900 dark:text-gray-300">Recupera</label>                    
                    </div>
                </div>
                {% endif %}
        </div>
    </div>
</div>
<script>
//initFlowbite();
  function rtu_search(){
      var input, filter, ul, li, a, i, txtValue;
      input = document.getElementById('input-rtu-search');
      filter = input.value.toUpperCase();
      ul = document.getElementById("input-rtu-ul");
      li = ul.getElementsByTagName('li');
      for (i = 0; i < li.length; i++) {
            label = li[i].getElementsByTagName("label")[0];
            txtValue = label.textContent || label.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                li[i].style.display = "";
            } else {
                li[i].style.display = "none";
            }
      }
    }

    function getQuery(){
        let obj = {}
        inputElements.forEach(function(input) {
            if(input.name.length > 0){
            obj[input.name] = input.value;
            }      
        });
        
        obj['offset'] = 0
        obj['limit'] = 9

        //filter builder
        let model = gridArchive.getFilterModel()
        console.log(model)
        for(let e in model){
        for(let filter in model[e])
            console.log(e,filter)
        }

        obj['filter'] = JSON.stringify(model)
        return obj
  }

  function sync(){
        var radioButtons = document.querySelectorAll('input[name="list-rtu"]');
        var startDate = document.getElementById('start-date');
        var endDate = document.getElementById('end-date');
        // Loop through the radio buttons to find the checked one
        data = {};
        let ovveride = false;
        radioButtons.forEach(function(radioButton) {
            //if (radioButton.value == "some") {
                if (radioButton.checked){
                    let checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    list = []
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.name == 'check-ovveride'){
                            ovveride = checkbox.checked;
                        }else{
                            if(checkbox.checked)
                                list.push(checkbox.value);
                        }
                        
                    });    
                    if(radioButton.value == "some") 
                        data['rtus'] = list  
                    data['value'] = radioButton.value;                      
                }

        });
        data['override'] = ovveride;
        data['start'] = startDate.value;
        data['end'] = endDate.value;
        return data
    }
</script>