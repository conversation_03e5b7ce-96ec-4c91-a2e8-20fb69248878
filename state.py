import duckdb 
import json
from logger import logger
import os
import asyncio

async def persist_appstate(appstate):
    """Persist appstate.iset dictionary to DuckDB as a key-value store"""
    try:
        # Convert the dictionary to a list of key-value pairs
        kv_pairs = []
        for key, value in appstate.iset.items():
            # Convert value to JSON string
            if isinstance(value, dict) or isinstance(value, list):
                value_json = json.dumps(value)
            elif isinstance(value, bytes):
                # Skip binary data or handle differently if needed
                continue
            else:
                value_json = json.dumps(value)
            
            kv_pairs.append({"key": key, "value": value_json})
        
        if not kv_pairs:
            logger.warning("No items to persist in appstate.iset")
            return
        
        # Use in-memory filesystem to store JSON data temporarily
        with appstate.fs.open('appstate_kv_pairs.json', 'w') as f:
            json.dump(kv_pairs, f)
        
        # Use DuckDB to store the data
        with duckdb.connect('appstate.db') as conn:
            # Register the memory filesystem
            conn.register_filesystem(appstate.fs)
            
            # Create the table if it doesn't exist
            conn.execute("""
                CREATE TABLE IF NOT EXISTS appstate_kv (
                    key VARCHAR PRIMARY KEY,
                    value VARCHAR,
                    updated_at TIMESTAMP
                )
            """)
            
            # Insert or update the key-value pairs
            conn.execute("""
                INSERT INTO appstate_kv (key, value, updated_at)
                SELECT key, value, NOW()
                FROM read_json_auto('memory://appstate_kv_pairs.json')
                ON CONFLICT (key) DO UPDATE SET 
                    value = excluded.value,
                    updated_at = NOW()
            """)
            
            # Log the number of persisted items
            count = conn.execute("SELECT COUNT(*) FROM appstate_kv").fetchone()[0]
            #logger.info(f"Persisted {len(kv_pairs)} items to DuckDB (total: {count})")
    
    except Exception as e:
        logger.error(f"Error persisting appstate to DuckDB: {e}")

async def restore_appstate(appstate):
    """Restore appstate.iset dictionary from DuckDB"""
    try:
        # Check if the database file exists

        if not os.path.exists('appstate.db'):
            logger.warning("No appstate.db file found, skipping restore")
            pass
        
        # Use DuckDB to retrieve the data
        with duckdb.connect('appstate.db',read_only=True) as conn:
            # Check if the table exists
            conn.sql("""
                SELECT table_name 
                FROM information_schema.tables
                """).show()            
            table_exists = conn.execute("""
                SELECT table_name 
                FROM information_schema.tables
                WHERE table_name = 'appstate_kv'
                """).fetchone()
            
            if not table_exists:
                logger.warning("No appstate_kv table found in database, skipping restore")
                return
            
            # Retrieve all key-value pairs
            kv_pairs = conn.execute("""
                SELECT key, value FROM appstate_kv
            """).fetchall()
            conn.sql("""
                SELECT key, value FROM appstate_kv
            """).show()
            # Restore to appstate.iset
            restored_count = 0
            for key, value_json in kv_pairs:
                try:
                    # Parse the JSON value
                    value = json.loads(value_json)
                    appstate.iset[key] = value
                    restored_count += 1
                except json.JSONDecodeError:
                    appstate.iset[key] = value_json
                    logger.warning(f"Failed to parse JSON for key: {key}")
            
           # logger.info(f"Restored {restored_count} items to appstate.iset")
    
    except Exception as e:
        logger.error(f"Error restoring appstate from DuckDB: {e}")        

# Add a periodic task to persist appstate.iset
async def persistance_loop(appstate):
    """Periodically persist appstate.iset to DuckDB"""
    # Persist every 5 minutes
    await asyncio.sleep(300*6)  
    while True:
        try:
            await persist_appstate(appstate)
        except Exception as e:
            logger.error(f"Error in periodic persist: {e}")
        
      