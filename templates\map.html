
<div id="map" style=" height: 500px;"></div>
<script>
    async function fetchAndParseJSON(url) {
    try {
        // Fetch the JSON file
        const response = await fetch(url);

        // Check if the response is OK (status code 200-299)
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Parse the JSON file
        const data = await response.json();

        // Log the parsed data
        return(data)
    } catch (error) {
        // Handle any errors
        console.error('Error fetching or parsing JSON:', error);
    }
}
var map = L.map('map').setView([46, 11], 13);
    var tiles = L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(map);


    fetchAndParseJSON("/webscada/gisdata").then(gisdata =>{
      console.log(gisdata)       
        for(let i in gisdata){
          L.marker([gisdata[i].LATITUDINE, gisdata[i].LONGITUDINE]).addTo(map)
            .bindPopup(`<b>${gisdata[i].ID_MISURA}</b><b>${gisdata[i].ID_PERIFERICA}</b><b>${gisdata[i].IDENTIFICATIVO_OGGETTO_MAPPA}</b>`);
          }
      });
</script>
