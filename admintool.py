from messaging import component
import asyncio
import json
import pyotp
import sys

async def loop(emails, key):
    nc = component()
    await nc.connect(bucket = 'iset')

    if len(key) > 0:    
        val = await nc.put(f'iset.user.jwtkey',key.encode())

    for email in emails:
         val = await nc.put(f'iset.user.admin.{email.lower().replace("@",".")}',json.dumps({'email':email.lower()}).encode())
         print(val)
    await asyncio.sleep(0.5)
    #await nc.close()

if __name__ == "__main__":  
    inputs = input("Inserisci le mail separate da virgola:\n") 
    emails = inputs.split(',')
    print(emails)
    inputs = input("Vuoi generare la chiave segreta per l'autenticazione JWT si/no:\n").lower() 
    key =""
    if(inputs == 'si' or inputs == 'sì'):
        key = pyotp.random_base32()
    try:
        asyncio.run(loop(emails, key))
    except Exception as e:
        print(e)
    input("Premi Enter per uscire...")
