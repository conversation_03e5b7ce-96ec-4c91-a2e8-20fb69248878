
{% if delete %}
    <label hx-get="/webscada/addchart/" hx-swap="outerHTML" hx-vals='{"chartIndex":{{chartIndex}}}' class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
        <svg class="w-6 h-6 mr-4 text-white dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
        <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4.243a1 1 0 1 0-2 0V11H7.757a1 1 0 1 0 0 2H11v3.243a1 1 0 1 0 2 0V13h3.243a1 1 0 1 0 0-2H13V7.757Z" clip-rule="evenodd"/>
        </svg>
    Aggiungi grafico
    </label>
{% else %}

<div id="chart-{{chartIndex}}">
          <div class="rounded-lg dark:bg-gray-800 p-1 md:p-1">
              <div class="flex justify-between mb-2">
                <div id="chart-legend-{{chartIndex}}" class="flex p-1 mx-auto my-2 bg-gray-100 rounded-lg dark:bg-gray-600" role="group">
                  <button id='zoom-{{chartIndex}}-0' onclick="zoomLocalChart(this,0.1,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    1 h
                  </button>
                  <button id='zoom-{{chartIndex}}-1' onclick="zoomLocalChart(this,1,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    1 g
                  </button>
                  <button id='zoom-{{chartIndex}}-2' onclick="zoomLocalChart(this,7,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    7 g
                  </button>
                  <button id='zoom-{{chartIndex}}-3' onclick="zoomLocalChart(this,30,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    1 m
                  </button>    
                  <button id='zoom-{{chartIndex}}-4' onclick="zoomLocalChart(this,90,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    3 m
                  </button> 
                  <button id='zoom-{{chartIndex}}-5' onclick="zoomLocalChart(this,180,'chart-timeline-{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    6 m
                  </button>   

                  <button id='zoom-{{chartIndex}}-6' onclick="zoomLocalChart(this,365,'chart-timeline-{{chartIndex}}')" 
                  class="hidden md:block px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    1 y
                  </button>     

                  <button  id="zoom-{{chartIndex}}-7" data-dropdown-toggle="datetimePicker-{{chartIndex}}" type="button" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.5 11.5 2.071 1.994M4 10h5m11 0h-1.5M12 7V4M7 7V4m10 3V4m-7 13H8v-2l5.227-5.292a1.46 1.46 0 0 1 2.065 2.065L10 17Zm-5 3h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
                    </svg>
                    
                  </button>     
                  <!-- datetime dropdown menu -->
                  <div id="datetimePicker-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm w-auto dark:bg-gray-700 p-1">
                    <div class="max-w-[16rem] mx-auto gap-4 mb-1">
                        <div class="flex mb-2">
                            <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Inizio:</label>
                              <input type="date" id="start-date-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                              <input type="time" id="start-time-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
                        </div>
                        
                        <div class="flex mb-2">
                          <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Fine:</label>
                            <input type="date" id="end-date-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                            <input type="time" id="end-time-{{chartIndex}}" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
                      </div>
                    </div>          
                    <button type="button" onclick="setDateTime('{{chartIndex}}')"
                     class=" w-full rounded-b-lg  p-2 text-sm font-medium text-red-600 border-t border-gray-200 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">Invia</button>          
                  </div>    
                  
                  <button  data-dropdown-toggle="axisSettings-{{chartIndex}}" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    <svg id="axisSettingsToggle-{{chartIndex}}" class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v15a1 1 0 0 0 1 1h15M8 16l2.5-5.5 3 3L17.273 7 20 9.667"/>
                    </svg>                                           
                  </button> 
          
                  <!-- axis update dropdown menu -->
                  <div id="axisSettings-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700 p-1">
                    <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                      <label for="chart-{{chartIndex}}-min" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Min:</label>
                      <input id="chart-{{chartIndex}}-min" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                      <label for="chart-{{chartIndex}}-max" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Max:</label>
                      <input id="chart-{{chartIndex}}-max" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div> 
                    <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
                      <div class="inline-flex items-center me-5 cursor-pointer">
                        <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Scala</span>
                      </div> 
                      <label>
                        <input type="checkbox" value="" class="sr-only peer" onclick="updateYAxis(this,'{{chartIndex}}')" >
                        <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>
                      </label>
                    </div>   
          
                    <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 border-t 
                     border-gray-200 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 ">
                      <div class="inline-flex items-center me-5 cursor-pointer">
                        <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Aggregati</span>
                       </div>
                       <label>
                        <input type="checkbox" value="" class="sr-only peer" onclick="enableAvg(this,'{{chartIndex}}')" >
                        <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>           
                       </label> 
                    </div> 
          
                    <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 border-t 
                     border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 ">
                      <div class="inline-flex items-center me-5 cursor-pointer">
                        <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Multiasse Y</span>
                       </div>
                       <label>
                        <input id="toggle-yaxis-{{chartIndex}}" type="checkbox" value="" class="sr-only peer" onclick="toggleYAxis(this,'{{chartIndex}}')" >
                        <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>           
                       </label> 
                    </div>           
                  </div>  
          
                  <button data-dropdown-toggle="chartSeries-{{chartIndex}}" onclick="updateSeriesDropdown('{{chartIndex}}')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                    </svg>                                                                                                        
                  </button>
                  <!-- Axis dropdown menu -->
                  <div id="chartSeries-{{chartIndex}}" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700">
          
                    <ul id="charts-list-{{chartIndex}}" class=" px-1 py-1 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownSearchButton">
                    </ul>
                    <button onclick="deleteSeries('{{chartIndex}}')" class="w-full items-center p-2 text-sm font-medium text-red-600 border-t border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
                     Elimina misura
                    </button>
                </div>                                          
                </div>
              </div>
              </div>
              <div id="chart-timeline-{{chartIndex}}" class="chart-timeline"></div>     
                    
              <div class="grid grid-cols-1 items-center border-gray-200 border-t dark:border-gray-700 justify-between mt-2.5">
                  <div class="pt-3">      
                    <label hx-get="/webscada/addchart/" hx-swap="outerHTML" hx-vals='{"chartIndex":{{chartIndex}}}' class="px-5 py-2.5 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        <svg class="w-6 h-6 mr-4 text-white dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                          <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4.243a1 1 0 1 0-2 0V11H7.757a1 1 0 1 0 0 2H11v3.243a1 1 0 1 0 2 0V13h3.243a1 1 0 1 0 0-2H13V7.757Z" clip-rule="evenodd"/>
                          </svg>
                      Aggiungi grafico
                      </label>
                </div>
              
              </div>
          </div>            
</div> 
{% endif %}

<script>
async function setDateTime(id) {
  let startDate = document.getElementById(`start-date-${id}`); 
  let startTime = document.getElementById(`start-time-${id}`);
  let endDate = document.getElementById(`end-date-${id}`);
  let endTime = document.getElementById(`end-time-${id}`);
  let zoomButton = document.getElementById(`zoom-${id}-7`);

  let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
 
  if (startDate.value !== "" && endDate.value !== "") {
    // Set default times if not provided
    const startTimeValue = startTime.value !== "" ? startTime.value : "00:00";
    const endTimeValue = endTime.value !== "" ? endTime.value : "23:59";
    
    // If time fields are empty, update them with default values
    if (startTime.value === "") startTime.value = startTimeValue;
    if (endTime.value === "") endTime.value = endTimeValue;
    
    // Create Date objects to validate the interval
    const startDateTime = new Date(`${startDate.value}T${startTimeValue}`);
    const endDateTime = new Date(`${endDate.value}T${endTimeValue}`);
    
    // Calculate the difference in milliseconds
    const timeDiff = endDateTime - startDateTime;
    
    // Maximum allowed interval (1 year in milliseconds)
    const maxInterval = 366 * 24 * 60 * 60 * 1000;
    
    // Check if interval is valid (positive and not too large)
    if (timeDiff <= 0) {
        alert("La data di fine deve essere successiva alla data di inizio");
        return;
    } else if (timeDiff > maxInterval) {
        alert("l'intervallo selezionato è maggiore di un anno. Scegliere un intervallo più piccolo");
        return;
    }
    
    // Format the datetime strings
    const startDatetime = `${startDate.value}T${startTimeValue}`;
    const endDatetime = `${endDate.value}T${endTimeValue}`;
    
    // Check if we have data for this time range
    let needsDataFetch = false;
    
    try {
      // Get chart series data
      const seriesData = myChart.getOption().series;
      console.log(seriesData)
      if (!seriesData || seriesData.length === 0) {
        needsDataFetch = true;
      } else {
        // Check each series
        for (let i = 0; i < seriesData.length; i++) {
          if (!seriesData[i].data || seriesData[i].data.length === 0) {
            needsDataFetch = true;
            break;
          }
          
          // Check if we have data points within the selected range
          let hasDataInRange = false;
          const tagData = seriesData[i].data;
          
          for (let j = 0; j < tagData.length; j++) {
            const pointDate = new Date(tagData[j][0]);
            const lastDate = new Date(tagData[j].slice(-1)[0]);
            //console.log(pointDate,lastDate);
            if (pointDate < startDateTime | lastDate > endDateTime) {
              console.log(pointDate,startDateTime,endDateTime)

              hasDataInRange = true;
              break;
            }
          }
          
          if (!hasDataInRange) {
            needsDataFetch = true;
            break;
          }
        }
      }
    } catch (error) {
      console.warn("Error checking chart data:", error);
      needsDataFetch = true;
    }
    
    console.log(needsDataFetch)
    // If we need to fetch data, make an HTMX request
    if (needsDataFetch) {
      console.log('fetching data')
      
      // Collect all tags from the chart
      const tags = [];
      try {
        const seriesData = myChart.getOption().series;
        for (let i = 0; i < seriesData.length; i++) {
          if (seriesData[i].tag) {
            tags.push(seriesData[i].tag);
          }
        }
      } catch (error) {
        console.warn("Error getting tags:", error);
      }
      
      if (tags.length > 0) {
        // Format dates for the API request
        const formattedStartDate = startDateTime.getFullYear() + '-' + 
                                  String(startDateTime.getMonth() + 1).padStart(2, '0') + '-' + 
                                  String(startDateTime.getDate()).padStart(2, '0') + ' ' + 
                                  String(startDateTime.getHours()).padStart(2, '0') + ':' + 
                                  String(startDateTime.getMinutes()).padStart(2, '0') + ':' + 
                                  String(startDateTime.getSeconds()).padStart(2, '0');

        const formattedEndDate = endDateTime.getFullYear() + '-' + 
                                String(endDateTime.getMonth() + 1).padStart(2, '0') + '-' + 
                                String(endDateTime.getDate()).padStart(2, '0') + ' ' + 
                                String(endDateTime.getHours()).padStart(2, '0') + ':' + 
                                String(endDateTime.getMinutes()).padStart(2, '0') + ':' + 
                                String(endDateTime.getSeconds()).padStart(2, '0');
        
        // Prepare the query
        for (let tag of tags){
          const query = {
            tags: tag,
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            chartIndex: id
          };
          htmx.ajax('GET', `/webscada/hist/?q=${encodeURIComponent(JSON.stringify(query))}`, {swap:'none', indicator:"#spinner"}).then((response) => {
            console.log(response)
          });
        }/*
        const query = {
          tags: tag,
          start_date: formattedStartDate,
          end_date: formattedEndDate,
          chartIndex: id
        };
        
        // Make the HTMX request
          htmx.ajax('GET', `/webscada/hist/?q=${encodeURIComponent(JSON.stringify(query))}`, {swap:'none', indicator:"#spinner"}).then((response) => {
          console.log(response)
        });*/

      } else {
        // No tags found, just apply zoom to whatever data we have
        myChart.dispatchAction({
          type: 'dataZoom',
          startValue: startDatetime,
          endValue: endDatetime
        });
        
        // Update UI to show active state
        zoomButton.classList.add('bg-gray-900', 'text-white');
      }
    } else {
      // We have the data, just apply the zoom
      myChart.dispatchAction({
        type: 'dataZoom',
        startValue: startDatetime,
        endValue: endDatetime
      });
      
      // Update UI to show active state
      zoomButton.classList.add('bg-gray-900', 'text-white');
    }
  } else {
    // Alert if dates are missing
    alert("Seleziona entrambi le date");
  }
}
</script>
