

<style>
    .ag-header-cell-label {
      justify-content: center;
    }
</style>
<!-- Add this near your grid element -->

<div id="global-modal" data-modal-target="global-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" 
class="hidden overflow-y-auto overflow-x-hidden  fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <!-- Modal content -->
    
    <div class="relative  p-4 w-full max-w-2xl max-h-full bg-white dark:bg-gray-800 rounded-lg shadow ">
        <!-- Modal header -->
        <div class="flex flex-wrap  border-b border-gray-200 dark:border-gray-700">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="default-tab" data-tabs-toggle="#default-tab-content" role="tablist">
                <li class="me-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg" id="profile-tab" data-tabs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">Grafico globale</button>
                </li>
                <li class="me-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300" id="dashboard-tab" data-tabs-target="#dashboard" type="button" role="tab" aria-controls="dashboard" aria-selected="false">Analitica</button>
                </li>
                <li class="me-2" >
                    <button class="inline-block p-4  rounded-t-lg text-red-400 hover:text-gray-700 hover:border-gray-300 dark:hover:text-red-300" onclick="clearGlobalChart()" type="button" >Reset</button>
                </li>                
            </ul>

            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="global-modal">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <div id="default-tab-content">
            <div class="hidden rounded-lg bg-gray-50 dark:bg-gray-800" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                <!-- global chart handles-->
                <div class="rounded-lg dark:bg-gray-800 p-1 md:p-1">
                    <div class="flex justify-between mb-2">
                      <div id="chart-legend-global" class="flex p-1 mx-auto my-2 bg-gray-100 rounded-lg dark:bg-gray-600" role="group">
                        <button id='zoom-global-0' onclick="zoomChart(0.1)" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          1 h
                        </button>
                        <button id='zoom-global-1' onclick="zoomChart(1)" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          1 g
                        </button>
                        <button id='zoom-global-2' onclick="zoomChart(7)" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          7 g
                        </button>
                        <button id='zoom-global-3' onclick="zoomChart(30)" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          1 m
                        </button>    
                        <button id='zoom-global-4' onclick="zoomChart(90)" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          3 m
                        </button> 
                        <button id='zoom-global-5' onclick="zoomChart(180)" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          6 m
                        </button>   
      
                        <button id='zoom-global-6' onclick="zoomChart(365)" 
                        class="hidden md:block px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          1 y
                        </button>     
      
                        <button  id="zoom-global-7" data-dropdown-toggle="datetimePicker-global" type="button" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.5 11.5 2.071 1.994M4 10h5m11 0h-1.5M12 7V4M7 7V4m10 3V4m-7 13H8v-2l5.227-5.292a1.46 1.46 0 0 1 2.065 2.065L10 17Zm-5 3h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
                          </svg>
                          
                        </button>     
                        <!-- datetime dropdown menu -->
                        <div id="datetimePicker-global" class="z-10 hidden bg-white rounded-lg shadow-sm w-auto dark:bg-gray-700 p-1">
                          <div class="max-w-[16rem] mx-auto gap-4 mb-1">
                              <div class="flex mb-2">
                                  <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Inizio:</label>
                                    <input type="date" id="start-date-global" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                                    <input type="time" id="start-time-global" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
                              </div>
                              
                              <div class="flex mb-2">
                                <label  class="w-64 p-1 text-sm font-medium text-gray-900 dark:text-white">Fine:</label>
                                  <input type="date" id="end-date-global" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                                  <input type="time" id="end-time-global" class="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />                             
                            </div>
                          </div>          
                          <button type="button" onclick="setDateTime('global')"
                           class=" w-full rounded-b-lg  p-2 text-sm font-medium text-red-600 border-t border-gray-200 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">Invia</button>          
                        </div>    
                        
                        <button  data-dropdown-toggle="axisSettings-global" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          <svg id="axisSettingsToggle-global" class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v15a1 1 0 0 0 1 1h15M8 16l2.5-5.5 3 3L17.273 7 20 9.667"/>
                          </svg>                                           
                        </button> 
                
                        <!-- axis update dropdown menu -->
                        <div id="axisSettings-global" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700 p-1">
                          <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                            <label for="chart-global-min" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Min:</label>
                            <input id="chart-global-min" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                          </div>
                          <div class="flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                            <label for="chart-global-max" class="w-full mx-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">Max:</label>
                            <input id="chart-global-max" type="number" class="text-center w-20 bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-1 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                          </div> 
                          <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
                            <div class="inline-flex items-center me-5 cursor-pointer">
                              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Scala</span>
                            </div> 
                            <label>
                              <input type="checkbox" value="" class="sr-only peer" onclick="updateYAxis(this,'global')" >
                              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>
                            </label>
                          </div>   
                
                          <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 border-t 
                           border-gray-200 bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 ">
                            <div class="inline-flex items-center me-5 cursor-pointer">
                              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Aggregati</span>
                             </div>
                             <label>
                              <input type="checkbox" value="" class="sr-only peer" onclick="enableAvg(this,'global')" >
                              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>           
                             </label> 
                          </div> 
                
                          <div class="flex w-full justify-between p-1 text-sm font-medium text-red-600 border-t 
                           border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 ">
                            <div class="inline-flex items-center me-5 cursor-pointer">
                              <span class="mx-2 text-sm font-medium text-gray-900 dark:text-gray-300">Multiasse Y</span>
                             </div>
                             <label>
                              <input id="toggle-yaxis-global" type="checkbox" value="" class="sr-only peer" onclick="toggleYAxis(this,'global')" >
                              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600 dark:peer-checked:bg-red-600"></div>           
                             </label> 
                          </div>           
                        </div>  
                
                        <button data-dropdown-toggle="chartSeries-global" onclick="updateSeriesDropdown('global')" class="px-2 py-1.5 text-xs font-medium text-gray-900 hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700 rounded-lg">
                          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                          </svg>                                                                                                        
                        </button>
                        <!-- Axis dropdown menu -->
                        <div id="chartSeries-global" class="z-10 hidden bg-white rounded-lg shadow-sm  dark:bg-gray-700">
                
                          <ul id="charts-list-global" class=" px-1 py-1 overflow-y-auto text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownSearchButton">
                          </ul>
                          <button onclick="deleteGlobalSeries('global')" class="w-full items-center p-2 text-sm font-medium text-red-600 border-t border-gray-200 rounded-b-lg bg-gray-50 dark:border-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-red-500 text-center">
                           Elimina misura
                          </button>
                      </div>                                          
                      </div>
                    </div>
                    </div>
                <!--  end global chart handle   -->
                <div id="chart-global">
                    <!-- chartIndex var -->
                    <div id="chart-timeline-global" class="chart-timeline" style="height: 400px;"></div>
                </div> 
            </div>
            <div class="hidden p-2 rounded-lg bg-gray-50 dark:bg-gray-800" id="dashboard" role="tabpanel" aria-labelledby="dashboard-tab">
  
                <!-- DATAGRID-->
                <div id="global-analyticsGrid" class="ag-theme-quartz pb-16" style="height: 450px;">

                    <div class="flex justify-between relative  py-1 ">
        
                        <button id="download-grid" 
                        class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        type="button" onclick="onGlobalBtnExport()">Esporta tabella</button>

                        <button id="clear-grid" 
                        class="px-3 py-2 inline-flex items-center text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        type="button" onclick="clearGlobalFilters()">Azzera filtri</button>
                    </div>  
                </div> 
            </div>
        </div>

        
    </div>
</div> 
<script> 

//Local grid init method
function localGridInit(id){
      var gridElement = document.querySelector(id);    
      document.addEventListener('dark-mode', function(e) {
          if (gridElement.classList.contains('ag-theme-quartz-dark')) {
              // If dark theme is applied, switch to light theme
              gridElement.classList.remove('ag-theme-quartz-dark');
              gridElement.classList.add('ag-theme-quartz');
          } else {
              // If light theme is applied, switch to dark theme
              gridElement.classList.remove('ag-theme-quartz');
              gridElement.classList.add('ag-theme-quartz-dark');
          }    
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                  gridElement.classList.remove('ag-theme-quartz');
                  gridElement.classList.add('ag-theme-quartz-dark');
              } else {
                  gridElement.classList.remove('ag-theme-quartz-dark');
                  gridElement.classList.add('ag-theme-quartz');
              }
              var localeText = JSON.parse(localStorage.getItem('grid-it'));
      
      var gridOptions = {
              // Row Data: The data to be displayed.
              rowData : [],
              // Column Definitions: Defines the columns to be displayed.
              columnDefs: [],
              defaultColDef: {
                          flex: 1,
                      }, 
                      //enableRangeSelection: true,
        //enableCharts: true,
        localeText: localeText,
        rowSelection: "multiple",                
              };        
      let gridApix = agGrid.createGrid(gridElement, gridOptions); 
      
      // Function to apply the quick filter
      /*function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      } 
      document.getElementById('grid-filter').addEventListener('input', quickFilter)*/
      return gridApix;
}


async function gridHandle() {
    // Optimized function to transform the object into AG Grid row data
    function transformToRowData(obj) {
        // Use more efficient data structures
        const timestamps = new Set();
        
        // First pass: collect unique timestamps more efficiently
        for (let key in obj) {
            Object.values(obj[key]).forEach(series => {
                // Process in smaller batches to avoid UI blocking
                const batchSize = 1000;
                for (let i = 0; i < series.length; i += batchSize) {
                    const batch = series.slice(i, i + batchSize);
                    batch.forEach(dataPoint => {
                        timestamps.add(dataPoint[0]);
                    });
                    // Allow UI thread to breathe between batches
                    if (i + batchSize < series.length) {
                        // This is just a marker for batching, actual async happens later
                    }
                }
            });
        }
        
        // Create a map for faster lookups
        const dataMap = {};
        // Convert to array and sort for better performance
        Array.from(timestamps).sort().forEach(timestamp => {
            dataMap[timestamp] = { timestamp };
        });
        
        // Second pass: populate values more efficiently
        for (let key in obj) {
            // Process each series in batches
            Object.entries(obj[key]).forEach(([point, series]) => {
                // Create a temporary lookup map for this series
                const seriesMap = {};
                series.forEach(([timestamp, value]) => {
                    seriesMap[timestamp] = value;
                });
                
                // Apply values from the lookup map
                Object.keys(dataMap).forEach(timestamp => {
                    if (seriesMap[timestamp] !== undefined) {
                        dataMap[timestamp][point] = seriesMap[timestamp];
                    }
                });
            });
        }
        
        // Convert to array for AG Grid
        const rowData = Object.values(dataMap);
        return rowData;
    }
    
    // Main processing with optimizations
    try {
        // Create a worker for heavy processing if supported
        if (window.Worker && typeof transformToRowDataWorker === 'undefined') {
            try {
                const workerCode = `
                    self.onmessage = function(e) {
                        const obj = e.data;
                        
                        // Collection phase
                        const timestamps = new Set();
                        for (let key in obj) {
                            Object.values(obj[key]).forEach(series => {
                                series.forEach(dataPoint => {
                                    timestamps.add(dataPoint[0]);
                                });
                            });
                        }
                        
                        // Create data map
                        const dataMap = {};
                        Array.from(timestamps).sort().forEach(timestamp => {
                            dataMap[timestamp] = { timestamp };
                        });
                        
                        // Population phase
                        for (let key in obj) {
                            Object.entries(obj[key]).forEach(([point, series]) => {
                                series.forEach(([timestamp, value]) => {
                                    if (dataMap[timestamp]) {
                                        dataMap[timestamp][point] = value;
                                    }
                                });
                            });
                        }
                        
                        // Convert to array
                        const rowData = Object.values(dataMap);
                        self.postMessage(rowData);
                    };
                `;
                
                const blob = new Blob([workerCode], { type: 'application/javascript' });
                const workerUrl = URL.createObjectURL(blob);
                window.transformToRowDataWorker = new Worker(workerUrl);
                
                // Clean up the URL
                URL.revokeObjectURL(workerUrl);
            } catch (workerError) {
                console.warn("Web Worker creation failed, falling back to main thread processing", workerError);
            }
        }
        
        // Process data
        if (window.transformToRowDataWorker) {
            // Use worker for processing if available
            return new Promise((resolve) => {
                // Create a copy of the data for the worker
                const seriesDataCopy = JSON.parse(JSON.stringify(seriesData));
                
                // Set up response handler
                transformToRowDataWorker.onmessage = function(e) {
                    const rowData = e.data;
                    
                    // Create optimized column definitions
                    const cols = [];
                    cols.push({ 
                        field: "timestamp", 
                        filter: true, 
                        filter: 'agDateColumnFilter', 
                        cellStyle: { textAlign: 'center' },
                        filterParams: { 
                            comparator: (filterLocalDateAtMidnight, cellValue) => {
                                const cellDate = new Date(cellValue);
                                if (cellDate < filterLocalDateAtMidnight) {
                                    return -1;
                                } else if (cellDate > filterLocalDateAtMidnight) {
                                    return 1;
                                }
                                return 0;
                            },
                            filterOptions: ['greaterThan','lessThan','inRange'],
                            browserDatePicker: true,
                            inRangeFloatingFilterDateFormat: 'YYYY-MM-DDTHH:mm:ss',
                            maxNumConditions: 1
                        }
                    });
                    
                    for (let key in seriesData) {
                        for (let title in seriesData[key]) {
                            let keyName = document.getElementsByName(title)[0];
                            if (keyName) {
                                cols.push({
                                    field: title, 
                                    headerName: keyName.innerText, 
                                    filter: true,
                                    cellStyle: { textAlign: 'center' },
                                    valueFormatter: (params) => {
                                        if (params.value !== undefined && params.value !== null) {
                                            const numValue = typeof params.value === 'number' ? 
                                                params.value : params.value;
                                            
                                            if (!isNaN(numValue)) {
                                                return numValue;
                                            }
                                        }
                                        return params.value !== undefined ? String(params.value) : '';
                                    }
                                });
                            }
                        }
                    }
                    
                    // Update grid
                    gridApi.setGridOption("columnDefs", cols);
                    gridApi.setGridOption("rowData", rowData);
                    resolve();
                };
                
                // Send data to worker
                transformToRowDataWorker.postMessage(seriesDataCopy);
            });
        } else {
            // Fall back to optimized main thread processing
            // Create column definitions
            const cols = [];
            cols.push({ 
                field: "timestamp", 
                filter: true, 
                filter: 'agDateColumnFilter', 
                cellStyle: { textAlign: 'center' },
                filterParams: { 
                    comparator: (filterLocalDateAtMidnight, cellValue) => {
                        const cellDate = new Date(cellValue);
                        if (cellDate < filterLocalDateAtMidnight) {
                            return -1;
                        } else if (cellDate > filterLocalDateAtMidnight) {
                            return 1;
                        }
                        return 0;
                    },
                    filterOptions: ['greaterThan','lessThan','inRange'],
                    browserDatePicker: true,
                    inRangeFloatingFilterDateFormat: 'YYYY-MM-DDTHH:mm:ss',
                    maxNumConditions: 1
                }
            });
            
            for (let key in seriesData) {
                for (let title in seriesData[key]) {
                    let keyName = document.getElementsByName(title)[0];
                    if (keyName) {
                        cols.push({
                            field: title, 
                            headerName: keyName.innerText, 
                            filter: true,
                            cellStyle: { textAlign: 'center' },
                            valueFormatter: (params) => {
                                if (params.value !== undefined && params.value !== null) {
                                    const numValue = typeof params.value === 'number' ? 
                                        params.value : params.value;
                                    
                                    if (!isNaN(numValue)) {
                                        return numValue;
                                    }
                                }
                                return params.value !== undefined ? String(params.value) : '';
                            }
                        });
                    }
                }
            }
            
            // Process data in smaller chunks to avoid UI freezing
            const rowData = transformToRowData(seriesData);
            
            // Update grid
            gridApi.setGridOption("columnDefs", cols);
            gridApi.setGridOption("rowData", rowData);
        }
    } catch (error) {
        console.error("Error processing grid data:", error);
    }
}

//--------------------------global grid init method
function globalGridInit(id){
      var gridElement = document.querySelector(id);    
      document.addEventListener('dark-mode', function(e) {
          if (gridElement.classList.contains('ag-theme-quartz-dark')) {
              // If dark theme is applied, switch to light theme
              gridElement.classList.remove('ag-theme-quartz-dark');
              gridElement.classList.add('ag-theme-quartz');
          } else {
              // If light theme is applied, switch to dark theme
              gridElement.classList.remove('ag-theme-quartz');
              gridElement.classList.add('ag-theme-quartz-dark');
          }    
      });
      if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                  gridElement.classList.remove('ag-theme-quartz');
                  gridElement.classList.add('ag-theme-quartz-dark');
              } else {
                  gridElement.classList.remove('ag-theme-quartz-dark');
                  gridElement.classList.add('ag-theme-quartz');
              }
              var localeText = JSON.parse(localStorage.getItem('grid-it'));
      
      var gridOptions = {
              // Row Data: The data to be displayed.
              rowData : [],
              // Column Definitions: Defines the columns to be displayed.
              columnDefs: [],
              defaultColDef: {
                          flex: 1,
                      }, 
                      //enableRangeSelection: true,
        //enableCharts: true,
        localeText: localeText,
        rowSelection: "multiple",                
              };        
      var gridApi = agGrid.createGrid(gridElement, gridOptions); 
      
      // Function to apply the quick filter
      function quickFilter() {
        var filterValue = document.getElementById('grid-filter').value;
        gridApi.updateGridOptions({ quickFilterText: filterValue })
      } 
      //document.getElementById('grid-filter').addEventListener('input', quickFilter)
      return gridApi;
} 
   
var globalGridApi = globalGridInit('#global-analyticsGrid')   

function onGlobalBtnExport() {
    globalGridApi.exportDataAsCsv();
}

function clearGlobalFilters() {
    globalGridApi.setFilterModel(null);
}

async function globalGridHandle(seriesData){
          // Function to transform the object into AG Grid row data
        function transformToRowData(obj) {
          let rowData = [];
          let timestamps = new Set();
        
          // Collect all unique timestamps
          for (let key in obj){
              obj[key].data.forEach(dataPoint => {
                timestamps.add(dataPoint[0]);
              });
          }
          // Create a map for easy lookup
          let dataMap = {};
          timestamps.forEach(timestamp => {
              dataMap[timestamp] = { timestamp };
          });
        
          // Populate the map with values from each point
          for (let key in obj){
                point = obj[key].tag;
                obj[key].data.forEach(([timestamp, value]) => {
            
                  if (! dataMap[timestamp][point]) {
                    dataMap[timestamp][point] = value;             
                  } else {
                    // Handle duplicate timestamps within the same point if necessary
                    // For example, you could sum the values, take the average, etc.
                  }
                });
          }
          // Convert the map to an array of row data
          rowData = Object.values(dataMap);
        
          // Sort by timestamp if necessary
          rowData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
          return rowData;
        }
        
        // Transform the object to row data  
        var cols = []
        cols.push({ field: "timestamp", filter: true, filter: 'agDateColumnFilter',cellStyle: { textAlign: 'center' }, 
        filterParams: { 
          comparator: (filterLocalDateAtMidnight, cellValue) => {
                const cellDate = new Date(cellValue);
                if (cellDate < filterLocalDateAtMidnight) {
                    return -1;
                } else if (cellDate > filterLocalDateAtMidnight) {
                    return 1;
                }
                return 0;
            },
          filterOptions : [ 'greaterThan','lessThan','inRange'],
          maxNumConditions:1}})
        for (let key in seriesData){
            cols.push({field:seriesData[key].tag, headerName:seriesData[key].name, filter: true,cellStyle: { textAlign: 'center' }})
        }          
        // Transform the object to row data          
        let gridRowData = transformToRowData(seriesData);
        globalGridApi.setGridOption("columnDefs",cols);
        globalGridApi.setGridOption("rowData",gridRowData);
}
//--------------------------- Local persistence
var db = new Dexie("scadadb");
db.version(1).stores({
    objects: 'name,page,chartid,data'
});

// Open the database
db.open().catch(function (e) {
    console.error("Open failed: " + e);
});

function addObject(object,index) {

    let result = object.tags;
    //let id = 'global-timeline-'+index;
    let id = 'chart-timeline-global';
    let globalSeriesData = {};                  
    var data = []
    if (!globalSeriesData.hasOwnProperty(index)) {
        globalSeriesData[index] = {}
    }
    
    for (let e in result) {
        globalSeriesData[index][e] = result[e]
    } 
    
    for(let key in globalSeriesData[index]){
        let keyName;
        let obj;
        keyName = document.getElementsByName(key)[0];
        if (keyName){
            obj = {tag:key, name : keyName.innerText, page: 'globalchart', chartid: id, data: globalSeriesData[index][key] };
        }
        else{
            keyName = document.getElementById(key)
            let name = key;
            if (keyName){
                name = keyName.id;
            }
            
            obj = {tag:key, name : object.description[name], page: 'globalchart', chartid: id, data: globalSeriesData[index][key] };
        }
        //console.log(id,key,keyName.innerText,globalSeriesData[index][key])
        db.objects.put(obj).then(function() {
            loadChart();
            // Notify other tabs/windows that a change has occurred
            broadcastChannel.postMessage({operation:'add',name :obj.name});
        });
    } 
    


}

function getObjectByName(name) {
    db.objects.where('name').equals(name).first().then(function(obj) {
        if (obj) {
            console.log('Object found:',obj);
        } else {
            console.log('Object not found');            
        }
    }).catch(function(error) {
        console.error('Failed to fetch object:', error);
    });        
}

// Create a new Broadcast Channel
var broadcastChannel = new BroadcastChannel('global_chart');

// Listen for messages from other tabs
broadcastChannel.onmessage = function (e) {
        let name = e.data.name;
        /* for now donothing!!!
        if (e.data.operation == 'add'){
            getObjectByName(name);            
        }
        if (e.data.operation == 'clear'){
            clearGlobalChart();            
        }
        */
};


//helper method to get div index id for the chart
function getQueryParameters(url) {
    try {
        const urlObj = new URL(url, window.location.origin); // Base URL is needed only for parsing relative URLs
        // Use URLSearchParams to get the query parameters
        const params = new URLSearchParams(urlObj.search);
        
        // First check if chartIndex is directly in the URL
        let chartIndex = params.get('chartIndex');
        
        // If not found directly, try to extract from the 'q' parameter which might contain JSON
        if (!chartIndex && params.has('q')) {
            try {
                // Decode the URL-encoded JSON string
                const decodedJson = decodeURIComponent(params.get('q'));
                // Parse the JSON
                const jsonData = JSON.parse(decodedJson);
                // Extract chartIndex from the parsed JSON
                if (jsonData && jsonData.chartIndex) {
                    chartIndex = jsonData.chartIndex;
                }
            } catch (jsonError) {
                console.error("Error parsing JSON from query parameter:", jsonError);
            }
        }
        
        // Convert to integer and add 1 (as per original logic)
        if (chartIndex) {
            return parseInt(chartIndex, 10) + 1;
        } else {
            console.warn("chartIndex not found in URL:", url);
            return 1; // Default to 1 if not found
        }
    } catch (error) {
        console.error("Error parsing URL parameters:", error);
        return 1; // Default to 1 on error
    }
}

//chart init method
function chartInit(id){
    var chartElement = document.getElementById(id); 
    var chartsObj = [];
    var myChart = echarts.init(chartElement, null, {
        renderer: 'svg',
        useDirtyRect: false
    });
    
    var app = {};
    var data = []
    var option = {
        grid: {
            containLabel: true
        },
        tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            type: 'cross',
            label: {
            backgroundColor: '#6a7985'
            }
        },    
        position: function (pt) {
            return [pt[0], '10%'];
        }
        },
        legend: {
        //data: ['Legend A', 'Legend B', 'Legend C'],
        //backgroundColor: '#ccc',
        textStyle: {
            color: '#777'
            // ...
        }
        // ...
        },
        toolbox: {
        feature: {
            dataZoom: {
            yAxisIndex: 'none'
            },
            //restore: {},
            saveAsImage: {},
            magicType: {show: true, type: ['line', 'bar']},
        }
        },
        xAxis: {
        type: 'time',
        boundaryGap: false,
        },
        yAxis: {
        type: 'value',
        boundaryGap: ['50%', '100%'],
        splitLine: {
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        
        dataZoom: [
        {
            type: 'inside',
            start: 0,
            end: 100
        },
        {
            start: 0,
            end: 100
        }
        ],
        series: [
        {
            type: 'line',
            sampling: 'average',//lttb
            smooth: true,
            symbol: 'none',
            //areaStyle: {},
            /*markPoint: {
                    data: [
                    { type: 'max', name: 'Max' },
                    { type: 'min', name: 'Min' }
                    ]
                },            
            markLine: {
                data: [
                    { type: 'average', name: 'Med' }
                ]
            },*/          
            data: data
        }
        ]
    };
    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }
    
    window.addEventListener('resize', myChart.resize);
    return myChart;
}

function digital_chart(id,dataSeries){

    let chartElement = document.getElementById(id);
    let barChart = echarts.init(chartElement, null, {
        renderer: 'canvas',
        useDirtyRect: false
    });
    var yData = [];
    var digitalData = [];
    var analogSeries = [];
    var series;
    var categories = [];
    var startTime = +new Date();
    var endTime = +new Date();

    dataSeries.map(function (item,index) {
            if (item.dataType == 'DIN' | item.dataType == 'DOUT'){
            categories.push(item.name);
            var baseTime;
            var color;
            item.data.forEach(function(e,i){
                color = 'tomato'
                if (e[1] == 1)
                color = 'lime'

                if(i==0){
                baseTime = new Date(e[0]).getTime();
                startTime = baseTime;
                }
                if(i>0){
                    duration =new Date(e[0]).getTime() - baseTime;
                    digitalData.push({
                        name: item.name,
                        value: [index, baseTime, (baseTime += duration)],
                        itemStyle: {
                        normal: {
                            color: color
                        }
                        }
                    });
                }
            })
            }else{
            analogSeries.push(item)
            }
        })

    function renderItem(params, api) {
    var categoryIndex = api.value(0);
    var start = api.coord([api.value(1), categoryIndex]);
    var end = api.coord([api.value(2), categoryIndex]);
    var height = api.size([0, 1])[1] * 0.6;
    var rectShape = echarts.graphic.clipRectByRect(
        {
        x: start[0],
        y: start[1] - height / 2,
        width: end[0] - start[0],
        height: height
        },
        {
        x: params.coordSys.x,
        y: params.coordSys.y,
        width: params.coordSys.width,
        height: params.coordSys.height
        }
    );
    return (
        rectShape && {
        type: 'rect',
        transition: ['shape'],
        shape: rectShape,
        style: api.style()
        }
    );
    }

    if(digitalData.length > 0){
        series = [
            {
            type: 'custom',
            yAxisIndex: 0,
            renderItem: renderItem,
            itemStyle: {
                opacity: 0.8
            },
            encode: {
                x: [1, 2],
                y: 0
            },
            data: digitalData
            }
        ].concat(analogSeries);
        }
    else{series = analogSeries;}      

    option = {
    tooltip: {
        //trigger: 'axis',
        confine: true,
        axisPointer: {
            type: 'cross'
        }
    },
        legend: {
        //data: ['Legend A', 'Legend B', 'Legend C'],
        //backgroundColor: '#ccc',
        textStyle: {
            color: '#777'
            // ...
        }
        // ...
        },
        toolbox: {
        feature: {
            dataZoom: {},
            restore: {},
            saveAsImage: {}
        }
        },
    dataZoom: [
        {
        type: 'inside',
        //filterMode: 'weakFilter',
        showDataShadow: false,
        //top: 400,
        labelFormatter: ''
        },
        {
        start: 0,
            end: 100
        //filterMode: 'weakFilter'
        }
    ],
    grid: {
        height: 300
    },
    xAxis: {
        //min: startTime,
        //max: endTime,
        scale: true,
        type:'time'
    },
    yAxis: [{data: categories},{type: 'value',boundaryGap: ['50%', '100%']}],
    series: series
    };
    if (option && typeof option === 'object') {
    barChart.setOption(option);
    }
    
    window.addEventListener('resize', barChart.resize);
    return barChart;
}

async function clearLocalChart(index,id) {
        seriesData[index] = {}
        chartElement = document.getElementById(id)  
        let myChart = echarts.init(chartElement, null, {
                          renderer: 'canvas',
                          useDirtyRect: false
                      });       
        myChart.setOption({}, true);
        await gridHandle()
}

//Function to clear the database
async function clearGlobalChart() {
    try{
        await clearChart();
    }catch{}
    db.objects.clear().then(function() {
        console.log("Database cleared");
        broadcastChannel.postMessage({operation:'clear',name :"globalchart"});
    }).catch(function(error) {
        console.error("Failed to clear database:", error);
    });
}

async function deleteGlobalSeries(id) {
    let chartId = `chart-timeline-${id}` 
    let listId = `charts-list-${id}`
    let myChart = echarts.init(document.getElementById(chartId));
    let chartList = document.getElementById(listId)
    let seriesChoices = chartList.querySelectorAll('input');
    seriesChoices.forEach(choice => {
      if(choice.checked){
        let seriesName = choice.checked;
        let option = myChart.getOption();
        option.series = option.series.filter(function (serie) {

              if (serie.tag == choice.value){
                 //delete globalSeriesData[id][serie.tag]
                 db.objects.where('name')
                            .equals(serie.name)
                            .delete()
                            .then(function (deleteCount) {
                                        console.log( "Deleted series: " + serie.name  );
                                    });
              }
              return serie.tag !== choice.value;
          });
          myChart.setOption(option, true);
          gridHandle(gridApi)
          updateSeriesDropdown(id);
      }
    });
    loadChart();
  }

async function clearChart(){
       let all =  await db.objects.toArray(); 
       let myChart;
       let id;
       for(let idx in all){
        if (id != all[idx].chartid){
                chartElement = document.getElementById(all[idx].chartid)  
                myChart = echarts.init(chartElement, null, {
                    renderer: 'svg',
                    useDirtyRect: false
                });       
                myChart.setOption({}, true);
                id = all[idx].chartid;                
                console.log('global chart cleared')
            }
        } 
        globalGridApi.setGridOption("columnDefs",[]);
        globalGridApi.setGridOption("rowData",[]);
}

async function loadChart(){
       let all =  await db.objects.toArray() 
       if (all.length > 0){
            let data = []
            let myChart;
            for(let idx in all){
                    myChart = chartInit(all[idx].chartid)                       
                    data.push({tag: all[idx].tag, name: all[idx].name ,data: all[idx].data, type: 'line',sampling: 'lttb',smooth: true,symbol: 'none'/*,areaStyle: {}*/})             
                } 
            myChart.setOption({
                series: data
            });  
            globalGridHandle(data)
        }
}

async function restoreChart(){
    loadChart();
}

  // Update dropdown whenever series are added or removed
function deleteSeries(id) {
    let chartId = `chart-timeline-${id}` 
    let listId = `charts-list-${id}`
    let myChart = echarts.init(document.getElementById(chartId));
    let chartList = document.getElementById(listId)
    let seriesChoices = chartList.querySelectorAll('input');
    seriesChoices.forEach(choice => {
      if(choice.checked){
        let seriesName = choice.checked;
        let option = myChart.getOption();
        option.series = option.series.filter(function (serie) {
              if (serie.tag == choice.value)
                 delete seriesData[parseInt(id)][serie.tag]
              return serie.tag !== choice.value;
          });
          myChart.setOption(option, true);
          gridHandle(gridApi)
          updateSeriesDropdown(id);
      }
    });
  }

async function zoomChart(startDate, endDate) {
    let all =  await db.objects.toArray(); 
    for (let idx in all){
        let myChart = echarts.init(document.getElementById(all[idx].chartid));
        myChart.dispatchAction({
            type: 'dataZoom',
            startValue: startDate,
            endValue: endDate
        });        
    }

}

async function zoomChart(days) {
    var endDate = new Date(); // Today's date
    var startDate = new Date();

    if(days == 0.1){
    startDate.setHours(startDate.getHours() - 1)
  }
  else if( days == 1){
    startDate.setHours(0);
    startDate.setMinutes(0);
    startDate.setSeconds(0);
    startDate.setMilliseconds(0);
  }
  else{
    endDate.setDate(endDate.getDate() + 1);     
    startDate.setDate(endDate.getDate() - days);
  }
  

    let all =  await db.objects.toArray(); 
    //startDate.setDate(endDate.getDate() - days); // Calculate 'today - X days'
    for (let idx in all){
        let myChart = echarts.init(document.getElementById(all[idx].chartid));

        try {
            var seriesData = myChart.getOption().series;  
        } catch (error) {
            console.warn(error);
            continue;
        }
        
        for (var i = 0; i < seriesData.length; i++) {

            let earlyDate = new Date(Date.parse(seriesData[i].data[0][0]));
            if(earlyDate > startDate){
                htmx.ajax('GET', `/webscada/globalchart/${days}/${seriesData[i].tag}?chartIndex=1`, {swap:'none', indicator:"#e-spinner"});
            }
        }

        myChart.dispatchAction({
            type: 'dataZoom',
            startValue: startDate.toISOString(), // Format as 'YYYY-MM-DD'
            endValue: endDate.toISOString() // Format as 'YYYY-MM-DD'
        });
    }
}

async function enableAvg(e,id) {
    let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
    let option = myChart.getOption();
    let updatedSeries;
    if (e.checked ){       
      updatedSeries = option.series.map(function(serie) {
                return {
                    ...serie,
                    markPoint: {
                        data: [
                            { type: 'min', name: 'Min' },
                            { type: 'max', name: 'Max' },
                            { type: 'average', name: 'Avg' }
                        ]
                    }
                };
            });

            myChart.setOption({
                series: updatedSeries
            });
      }else{
        updatedSeries = option.series.map(function(serie) {
                return {
                    ...serie,
                    markPoint: { data: [] }
                };
            });

            myChart.setOption({
                series: updatedSeries
            });
      }
    }

async function setDateTime(id) {
    let startDate = document.getElementById(`start-date-${id}`); 
    let startTime = document.getElementById(`start-time-${id}`);
    let endDate = document.getElementById(`end-date-${id}`);
    let endTime = document.getElementById(`end-time-${id}`);
    let zoomButton = document.getElementById(`zoom-${id}-7`);

    let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
  
    if (startDate.value !== "" && endDate.value !== "") {
        // Set default times if not provided
        const startTimeValue = startTime.value !== "" ? startTime.value : "00:00";
        const endTimeValue = endTime.value !== "" ? endTime.value : "23:59";
        
        // If time fields are empty, update them with default values
        if (startTime.value === "") startTime.value = startTimeValue;
        if (endTime.value === "") endTime.value = endTimeValue;
        
        // Create Date objects to validate the interval
        const startDateTime = new Date(`${startDate.value}T${startTimeValue}`);
        const endDateTime = new Date(`${endDate.value}T${endTimeValue}`);
        
        // Calculate the difference in milliseconds
        const timeDiff = endDateTime - startDateTime;
        
        // Maximum allowed interval (1 year in milliseconds)
        const maxInterval = 365 * 24 * 60 * 60 * 1000;
        
        // Check if interval is valid (positive and not too large)
        if (timeDiff <= 0) {
            alert("La data di fine deve essere successiva alla data di inizio");
            return;
        } else if (timeDiff > maxInterval) {
            alert("L'intervalo selezionato è meggiore di un anno. Scegliere un intervallo più piccolo");
            return;
        }
        
        // Format the datetime strings
        startDatetime = `${startDate.value}T${startTimeValue}`;
        endDatetime = `${endDate.value}T${endTimeValue}`; 
        
        // Update UI to show active state
        zoomButton.classList.add('bg-gray-900', 'text-white');
        
        // Apply the zoom
        myChart.dispatchAction({
            type: 'dataZoom',
            startValue: startDatetime,
            endValue: endDatetime
        });
    } else {
        // Alert if dates are missing
        alert("Seleziona entrambi le date");
    }
}  

function updateYAxis(e,id) {
    const min = document.getElementById(`chart-${id}-min`)
    const max = document.getElementById(`chart-${id}-max`)
    const button = document.getElementById(`axisSettingsToggle-${id}`)
    let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
    if (e.checked ){
       
      button.classList.remove('text-gray-800','dark:text-white')
      button.classList.add('text-red-500')

      myChart.setOption({
                  yAxis: {
                      min: min.value,
                      max: max.value
                  }
            });
      }else{
        button.classList.add('text-gray-800','dark:text-white')
        button.classList.remove('text-red-500')
        myChart.setOption({yAxis: { min: undefined,max: undefined}});
      }
    }

    async function toggleYAxis(e,id) {
        let myChart = echarts.init(document.getElementById(`chart-timeline-${id}`));
        let option = myChart.getOption();
        var currentYAxisCount = option.yAxis.length;
        if (e.checked ){
            // Update to multiple y-axes with series names
            var updatedYAxis = option.series.map((serie, index) => {
                return {
                    type: 'value',
                    //name: serie.name,
                    position: 'left',
                    alignTicks: true,
                    axisLine: { /*show: true,*/lineStyle: { color: option.color[index] } },
                    axisLabel: { show: true, color: option.color[index] },
                    offset: index * 30 // Offset each y-axis for visibility
                };
            });

            myChart.setOption({
                yAxis: updatedYAxis,
                series: option.series.map((serie, index) => ({
                    ...serie,
                    yAxisIndex: index // Assign each series to the corresponding y-axis
                }))
            });
        } else {
            // Update to single y-axis
            myChart.setOption({
                yAxis: [{ type: 'value', boundaryGap: ['50%', '100%']}],
                series: option.series.map(serie => ({
                    ...serie,
                    yAxisIndex: 0 // Assign all series to the single y-axis
                }))
            });
        }          
    }  

htmx.on('htmx:afterRequest', (msg)=> {    
      //update chartsindex for the UI
      /*if (msg.detail.pathInfo.responsePath.includes("/addchart")){    
        chartIndex = getQueryParameters(msg.detail.pathInfo.responsePath)           
      }*/
      if (msg.detail.pathInfo.responsePath.includes("/globalchart")){    
            //const index = getQueryParameters(msg.detail.pathInfo.responsePath) - 1;
            let index = 'global'
            addObject(JSON.parse(msg.detail.xhr.response), index);   
            //updateSeriesDropdown(id);         
            let toggleYaxis = document.getElementById('toggle-yaxis-'+index)
            if (toggleYaxis.checked)
                toggleYAxis(toggleYaxis,index)          
        }            
     });

    var localChartLegends = document.querySelectorAll('[id^="chart-legend"]');
    localChartLegends.forEach(chartLegend => {
            let buttons = chartLegend.querySelectorAll('button');
            buttons.forEach(button => {
              if(button.id.includes('zoom')){
                  button.addEventListener('click', () => {
                    buttons.forEach(btn => {          
                      btn.classList.remove('bg-gray-900', 'text-white');
                    });
                    button.classList.add('bg-gray-900', 'text-white');
                  });
                }
            });
          })
  
        function updateSeriesDropdown(id) {
            let chartId = `chart-timeline-${id}` 

            let myChart = echarts.init(document.getElementById(chartId));
            let chartOption = myChart.getOption();
            let seriesDropdown = document.getElementById('charts-list-' + id);
            seriesDropdown.innerHTML = ''; // Clear existing options
            chartOption.series.forEach(series => {
                let option = document.createElement('li');
                let html =` 
                        <div class="flex items-center p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
                        <input id="tag-${series.tag}" type="checkbox" value="${series.tag}" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label for="tag-${series.tag}" class="w-full ms-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">${series.name}</label>
                        </div>
                        `
                option.innerHTML = html;  
                seriesDropdown.appendChild(option);
            });
        }        
  </script>
