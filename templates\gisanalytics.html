<div class="p-2">
<div class="flex justify-center items-center" hx-ext="sse" hx-swap="none" sse-connect="/webscada/realtime/analytics/{{pageid}}" sse-swap="message">
    <label  class="bg-blue-700 hover:bg-blue-800
    focus:ring-4 focus:ring-blue-300 font-large  text-left px-2 my-1 rounded-sm font-semibold 
     text-white">{{data['title']}}</label>    
</div>

<ul  role="list" class=" text-wrap divide-y divide-gray-200 dark:divide-gray-700">

{% for key,value in data.items() %}
    {% if key == 'isetpanel' %}
        {% for _,subvalue in value.items() %}
            {% for tag,name in subvalue.items() %}
                {% if  tag in config%}
                    {% set tag_config = config[tag] %}
                {% endif %}
                {% set key = tag.replace('::','') %}
                {% set adr = tag.split('::')[1] %}
                {% set len = adr|length %}
                {% if adr %}
                    {% set idx = adr[len-5]|int %}
                {% endif %}
                {% if name == 'title' %}
                <li >
                    <div class="flex flex-1 text-sm  justify-center items-center my-2">
                        <p class="text-m font-medium text-blue-700 dark:text-blue-500 "> {{tag}} </p>                    
                    </div>    
                </li>         
                {% else  %}
                {% if  tag in config%}
                <li>
                    <div class="flex items-center justify-between" id="accordion-{{key}}" data-accordion="collapse" >
                        <div class="flex items-center min-w-0">
                        <div>                            
                            <p name="{{tag}}" class="text-wrap ex font-medium text-gray-900 truncate dark:text-white">
                                {{name}} 
                            </p>
                            <div   class="flex flex-1 text-sm text-green-500 dark:text-green-400">
                            <span id=ts-{{tag}} data-src="iset" class="text-gray-500">{{tag}}</span>  
                            <button hx-get=/webscada/config/{{tag}}/  hx-vals='{"chartsIndex": "-1"}' hx-trigger="click" hx-swap="outerhtml" hx-target="#{{key}}"  hx-indicator="#spinner"
                             data-accordion-target="#{{key}}" aria-controls="{{key}}" type="button">
                                <svg class="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                </svg>
                            </button>      
                            <button data-tooltip-target="{{key}}-update" data-popper-placement="bottom" hx-get=/webscada/updaterequest/{{tag}} hx-indicator="#spinner" hx-trigger="click" hx-swap="none" hx-target="#{{key}}" type="button">
                                <svg xmlns="http://www.w3.org/2000/svg"class="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500" 
                                viewBox="0 -960 960 960" width="24px" fill="currentColor">
                                <path d="M480-120q-75 0-140.5-28.5t-114-77q-48.5-48.5-77-114T120-480q0-75 28.5-140.5t77-114q48.5-48.5 114-77T480-840q82 0 155.5 35T760-706v-94h80v240H600v-80h110q-41-56-101-88t-129-32q-117 0-198.5 81.5T200-480q0 117 81.5 198.5T480-200q105 0 183.5-68T756-440h82q-15 137-117.5 228.5T480-120Zm112-192L440-464v-216h80v184l128 128-56 56Z"/></svg>
                                <div id="{{key}}-update" role="tooltip"  class="absolute z-10 inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm tooltip opacity-0 invisible" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(793px, 60px);" >
                                    Aggiorna
                                  <div class="tooltip-arrow" data-popper-arrow="" style="position: absolute; left: 0px; transform: translate(69px, 0px);"></div>                                                                                      
                            </button>          
                                               
                            <!--  alarms table -->
                                {% if 'Allarme' in tag_config['ModoOp'] %}
                                <button hx-get=/webscada/allarme/  hx-vals='{"tag":"{{tag}}"}' hx-trigger="click"  hx-target="#{{key}}"
                                 data-accordion-target="#{{key}}" aria-controls="{{key}}"  type="button">
                                    <svg class="w-3.5 h-3.5 ml-2 text-gray-400 hover:text-gray-500" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M17.133 12.632v-1.8a5.407 5.407 0 0 0-4.154-5.262.955.955 0 0 0 .021-.106V3.1a1 1 0 0 0-2 0v2.364a.933.933 0 0 0 .021.106 5.406 5.406 0 0 0-4.154 5.262v1.8C6.867 15.018 5 15.614 5 16.807 5 17.4 5 18 5.538 18h12.924C19 18 19 17.4 19 16.807c0-1.193-1.867-1.789-1.867-4.175Zm-13.267-.8a1 1 0 0 1-1-1 9.424 9.424 0 0 1 2.517-6.391A1.001 1.001 0 1 1 6.854 5.8a7.43 7.43 0 0 0-1.988 5.037 1 1 0 0 1-1 .995Zm16.268 0a1 1 0 0 1-1-1A7.431 7.431 0 0 0 17.146 5.8a1 1 0 0 1 1.471-1.354 9.424 9.424 0 0 1 2.517 6.391 1 1 0 0 1-1 .995ZM8.823 19a3.453 3.453 0 0 0 6.354 0H8.823Z"></path>
                                    </svg><span class="sr-only">Show information</span>
                                </button>
                                {% endif %}                           
                            </div>
                        </div>
                        </div>

                        {% if idx == 3 %}
                        <div class="grid grid-cols-1 gap-1 mr-3">
                            <button  id={{tag}} data-src="iset"  hx-swap="none" 
                            data-popover-trigger="click" data-popover-target="w-{{key}}" data-popover-placement="bottom-end"
                            class=" text-right   font-semibold bg-pink text-green-500  dark:text-green-400">                                                
                            </button>
                            <div    data-popover="" id="w-{{key}}" role="tooltip" class="absolute overflow-auto z-10 min-w-max  text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-2xl w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 opacity-0 invisible " style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(221px, 731px);" data-popper-placement="bottom-start">
                                <ul class=" text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <li class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                                        <div class="flex items-center ps-3">
                                            <input id="{{key}}-on" type="radio"   value="" name="list-radio" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="{{key}}-on" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{tag_config['DNormale']}} </label>
                                        </div>
                                    </li>
                                    <li class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                                        <div class="flex items-center ps-3">
                                            <input id="{{key}}-off" type="radio"  value="" name="list-radio" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="{{key}}-off" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{tag_config['DAllarme']}} </label>
                                        </div>
                                    </li>

                                    <li class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                                        <div class="flex items-center ps-3">
                                            <input id="{{key}}-auto" type="radio"  value="" name="list-radio-mode" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="{{key}}-auto" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Automatico</label>
                                        </div>
                                    </li>
                                    <li class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                                        <div class="flex items-center ps-3">
                                            <input id="{{key}}-man" type="radio"  value="" name="list-radio-mode" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="{{key}}-man" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Manuale</label>
                                        </div>
                                    </li>
                                    <li class="w-full border-b border-gray-200 rounded-b-lg dark:border-gray-600 ">
                                            <button id="{{key}}-send"  hx-get=/webscada/write/  hx-swap="none" hx-vals='js:{tag: "{{tag}}", value: document.getElementById("{{key}}-off").checked, mode : document.getElementById("{{key}}-man").checked}'
                                            class="w-full rounded-b-lg  py-3 text-sm font-medium text-gray-900 dark:text-gray-300 hover:bg-gray-500">INVIA</button>
                                    </li>                                    
                                </ul>                                
                            </div>             
                            <label  id=state-{{tag}} data-src="iset" class="text-right font-semibold bg-pink text-green-500  dark:text-green-400">
                                indefinito
                            </label>    
                        </div>       
                        {% elif idx == 2 %}

                        <div class="grid grid-cols-1 gap-1 mr-3">
                            <button  id={{tag}} data-src="iset" 
                            data-popover-trigger="click" data-popover-target="w-{{key}}" data-popover-placement="bottom-end"
                            class="text-right font-semibold bg-pink text-green-500  dark:text-green-400">                                                
                            </button>                                 
                            <div data-popover="" id="w-{{key}}" role="tooltip" class="absolute overflow-auto z-10   text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-2xl w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 opacity-0 invisible " style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(221px, 731px);" data-popper-placement="bottom-start">
                                <div class="relative flex items-center rounded-t-lg ">
                                    <div id="min-{{key}}-button" class="bg-gray-100 dark:bg-gray-700 rounded-tl-lg  dark:border-gray-600  border border-gray-300  p-3 h-11 ">
                                        {{ '%0.2f' % tag_config['MinimoAmmesso']|float }}
                                    </div>
                                    <input  type="number" inputmode="numeric" step="0.01" min="{{tag_config['MinimoAmmesso']}}" max="{{tag_config['MassimoAmmesso']}}" id="{{key}}-input"  oninput="clamp(this);"
                                    class="bg-gray-50 border-x-0 border-gray-300 h-11 text-center text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block w-full py-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="0.0" required />
                                    <label  id="min-{{key}}-button" class="bg-gray-100 dark:bg-gray-700 rounded-tr-lg dark:border-gray-600   p-3 h-11 border border-gray-300  ">
                                        {{ '%0.2f' % tag_config['MassimoAmmesso']|float }}
                                    </label>                                

                                </div>
                                <ul class=" text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-b-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <li class="w-full border-b border-gray-200 rounded-t-lg dark:border-gray-600">
                                        <div class="flex items-center ps-3">
                                            <input id="{{key}}-auto" type="radio"  value="" name="list-radio-mode" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="{{key}}-auto" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Automatico</label>
                                        </div>                                        
                                        <div class="flex items-center ps-3">
                                            <input id="{{key}}-man" type="radio"  value="" name="list-radio-mode" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="{{key}}-man" class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Manuale</label>
                                        </div>
                                    </li>
                                    <li class="w-full rounded-b-lg border-b border-gray-200  dark:border-gray-600 ">
                                            <button type="button" id="max-{{key}}" hx-get=/webscada/write/  hx-vals='js:{tag: "{{tag}}", value: document.getElementById("{{key}}-input").value, mode : document.getElementById("{{key}}-man").checked}'  hx-swap="none" 
                                            class="w-full rounded-b-lg py-3 text-sm font-medium text-gray-900 dark:text-gray-300  hover:bg-gray-500">INVIA</button>
                                    </li>                                    
                                </ul> 
                            </div>        
                            <label  id=state-{{tag}} data-src="iset" class="text-right font-semibold bg-pink text-green-500  dark:text-green-400">
                                indefinito
                            </label>   
                        </div>  

                        {% elif idx == 4 %}
                        <button  id={{tag}} data-src="iset" hx-get=/webscada/write/  hx-vals='{"tag": "{{tag}}", "value": 1.5, "mode" : 0}'  hx-swap="none" class="mr-3 inline-flex items-center text-base font-semibold bg-pink text-green-500  dark:text-green-400">                                                
                        </button>                        
                        {% else %}      
                            <div class="mr-3 grid grid-cols-1 gap-1">
                                <label  id={{tag}} data-src="iset" class="text-right font-semibold bg-pink text-green-500  dark:text-green-400">
                                </label>
                                <label  id=state-{{tag}} data-src="iset" class="text-right font-semibold bg-pink text-green-500  dark:text-green-400">
                                    indefinito
                                </label>
                          </div>  
                        {% endif %}                   
                    </div>                    
                    <div id="{{key}}" class="hidden" aria-labelledby="accordion-open-heading-1">
                    </div>  
                </li>
                {% endif %}
                {% endif %}
            {% endfor %}
         {% endfor %}
    {% endif %}
{% endfor %}

</ul>
<!-- Main modal -->
</div>
<script>
   /* Draggable.create("#static-modal", {
  type: "x,y",
  bounds: "#panel",
  inertia: true,
  snap: {
    x: function (value) {
      return Math.round(value / valueX) * valueX;
    },
    y: function (value) {
      return Math.round(value / valueY) * valueY;
    }
  }
});*/
downloadGrid.classList.add('hidden');
function clamp(sender) {
    if (parseFloat(sender.value) > sender.max) {
        sender.value = sender.max;
    } else if (parseFloat(sender.value) < sender.min) {
        sender.value = sender.min;
    }
}

</script>