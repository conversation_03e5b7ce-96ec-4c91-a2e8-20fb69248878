{%- for value in items -%}
    {% set color = 'bg-white-700' %}
    {% if value['livello'] == 0 %}
        {% set color = 'bg-white-700' %} 
    {% endif %}

    {% if value['livello'] == 1 %}
        {% set color = 'bg-white-700' %} 
    {% endif %}

    {% if value['livello'] == 2 %}
        {% set color = 'text-slate-950 dark:text-black bg-yellow-300 hover:bg-yellow-500 focus:ring-yellow-300 dark:focus:ring-yellow-900' %} 
    {% endif %}

    {% if value['livello'] == 3 %}
        {% set color = 'text-slate-950 dark:text-white bg-yellow-300 hover:bg-yellow-400 focus:ring-yellow-300 dark:focus:ring-yellow-900' %} 
    {% endif %} 

    {% if value['livello'] == 4 %}
        {% set color = 'text-white bg-red-700 hover:bg-red-800 focus:ring-red-300  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900' %} 
    {% endif %}                
    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
        <td class="w-4 p-4">
            <div class="flex items-center">
                <input id="checkbox-table-1" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="checkbox-table-1" class="sr-only">checkbox</label>
            </div>
        </td>
                                                    
        <th scope="row" class=" font-medium text-center text-gray-900 whitespace-nowrap dark:text-white">
            {{value['timestamp']}}
        </th>
        <td class=" font-medium text-center">
            {{value['name']}}
        </td>
        <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2  {{color}}">
            {{value['messaggio']}}
        </td>
        <td class=" font-medium text-center">
            {{value['periferica']}}
        </td>
        <td class=" focus:outline-none focus:ring-4  font-medium text-sm px-5 py-2.5 text-center me-2 mb-2 {{color}}">
            {{value['livello']}}
        </td>                
    </tr>
{%- endfor -%}